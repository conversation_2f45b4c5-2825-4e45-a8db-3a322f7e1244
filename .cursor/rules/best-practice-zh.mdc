---
alwaysApply: true
---

---

name: best-practice-python description: Python 编码规范与最佳实践 globs: ['app/**/*.py']

---

核心原则:

- 编写简洁的技术性响应，附带准确的 Python 示例
- 决策流程描述:
  1. 从业务需求分析开始确定编程范式
  2. 评估关键技术考量:
     - 涉及状态管理、资源依赖或复杂生命周期 → 采用基于类的 oop 实现
     - 处理数据转换管道、无副作用操作或高阶函数组合 → 使用纯函数式编程
  3. 实施指南:
     - 面向对象类用于:
       - 管理有状态交互
       - 处理资源生命周期（如数据库连接）
       - 实现具有继承层次的领域模型
     - 优先使用纯函数:
       - 无状态数据处理流程
       - 可预测的输入/输出转换
       - 可组合的实用操作
- 优先使用迭代和模块化而非代码重复
- 使用带助动词的描述性变量名（如 is_active, has_permission）
- 目录和文件使用小写下划线命名（如 routers/user_routes.py）
- 优先使用命名导出路由和工具函数
- 采用"接收对象，返回对象"（RORO）模式

Python/FastAPI:

- 纯函数使用`def`，异步操作使用`async def`
- 所有函数签名使用类型提示，优先使用 Pydantic 模型而非原生字典进行输入验证
- 文件结构: 导出路由 → 子路由 → 工具 → 静态内容 → 类型（模型/模式）
- 避免条件语句中不必要的花括号
- 单行条件语句省略花括号
- 简单条件语句使用简洁单行语法（如`if 条件: 执行操作()`）

错误处理与验证:

- 优先处理错误和边界情况:
  - 在函数开头处理错误和边界情况
  - 使用提前返回避免深层嵌套 if 语句
  - 将正常执行路径放在函数最后以提升可读性
  - 避免不必要的 else 语句，改用 if-return 模式
  - 使用守卫子句提前处理前提条件和无效状态
  - 实现适当的错误日志记录和友好错误提示
  - 使用自定义错误类型或错误工厂保持错误处理一致性

依赖项:

- FastAPI
- Pydantic v2
- 异步数据库库（如 asyncpg/aiomysql）
- SQLAlchemy 2.0（如需 ORM 功能）
- 使用最流行异步任务库和事件库

FastAPI 专项指南:

- 使用函数式组件（普通函数）和 Pydantic 模型进行输入验证和响应模式定义
- 使用声明式路由定义并明确返回类型注解
- 同步操作用`def`，异步操作用`async def`
- 减少使用@app.on_event，优先使用生命周期上下文管理器处理启动/关闭事件
- 使用中间件进行日志记录、错误监控和性能优化
- 通过异步函数（I/O 密集型任务）、缓存策略和延迟加载优化性能
- 使用 HTTPException 处理预期错误并映射为具体 HTTP 响应
- 使用 Pydantic 的 BaseModel 保持输入/输出验证和响应模式一致性

性能优化:

- 最小化阻塞 I/O 操作，所有数据库调用和外部 API 请求使用异步操作
- 使用 Redis 或内存存储对静态和频繁访问数据实施缓存
- 利用 Pydantic 优化数据序列化/反序列化
- 大数据集和复杂 API 响应使用延迟加载技术

关键约定:

1. 依赖 FastAPI 的依赖注入系统管理状态和共享资源
2. 优先关注 API 性能指标（响应时间、延迟、吞吐量）
3. 限制路由中的阻塞操作:
   - 优先使用异步和非阻塞流程
   - 数据库和外部 API 操作使用专用异步函数
   - 清晰组织路由和依赖关系以优化可读性和可维护性
