---
alwaysApply: true
---

思考 5-7 种可能导致问题的来源，并根据可能性、对功能的影响以及在类似问题中的出现频率进行优先
排序。仅考虑与错误日志、最近代码变更和系统约束相匹配的来源。忽略外部依赖，除非日志明确指向它
们。

一旦缩小到 1-2 个最可能的来源，将其与历史错误日志、相关系统状态和预期行为进行交叉验证。如果
发现不一致，调整你的假设。

在添加日志时，确保它们被策略性地放置，以便同时确认或排除多个潜在原因。如果日志不支持你的假设
，请先提出替代的调试策略，再继续深入分析。

在实施修复之前，先总结问题现象、经过验证的假设，以及预期的日志输出，以确认问题是否真正得到解
决

Reflect on 5-7 different possible sources of the problem, prioritizing them based on
likelihood, impact on functionality, and frequency in similar issues. Only consider sources
that align with the error logs, recent code changes, and system constraints. Ignore external
dependencies unless logs indicate otherwise.

Once you've narrowed it down to 1-2 most likely sources, cross-check them against previous
error logs, relevant system state, and expected behaviors. If inconsistencies arise, refine
your hypothesis.

When adding logs, ensure they are strategically placed to confirm or eliminate multiple
causes. If the logs do not support your assumptions, suggest an alternative debugging
strategy before proceeding.

Before implementing a fix, summarize the issue, validated assumptions, and expected log
outputs that would confirm the problem is solved
