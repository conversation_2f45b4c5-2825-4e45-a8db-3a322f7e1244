# ENV
APP_ENV="local"
PYTHON_VERSION=313 # which dockerfile to use. see in dockerfiles/python*/Dockerfile
PYTHONPATH=.

# 应用基础配置
PROJECT_NAME="aitools"
API_V1_STR="/api/v1"
SECRET_KEY="09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"
ACCESS_TOKEN_EXPIRE_MINUTES="11520"
REFRESH_TOKEN_EXPIRE_MINUTES="11520"
PASSWORD_RESET_TOKEN_EXPIRE_MINUTES="10"

WECHAT_APP_ID="wx7c7e559acf0a4316"
WECHAT_APP_SECRET="604b63f91fabc5c71edbdcf54edcc750"
WECHAT_AUTO_CREATE_USER="true"
WECHAT_ENABLED="true"
# API认证配置
API_USERNAME="ubuntu"
API_PASSWORD="debian"
API_ALGORITHM="HS256"

# 管理员配置
ADMIN_USERNAME="<EMAIL>"
ADMIN_PASSWORD="qinjun666"

# CORS配置
BACKEND_CORS_ORIGINS='["http://localhost:3000","http://localhost:8080","http://localhost"]'

# 数据库配置
POSTGRES_SERVER="localhost"
POSTGRES_USER="funny"
POSTGRES_PASSWORD="qinjun666"
POSTGRES_DB="aitools"
DATABASE_URL="postgresql+asyncpg://funny:qinjun666@localhost/aitools"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB="0"
REDIS_PASSWORD=""

# 租户配置
MULTI_TENANT_ENABLED="false"
DEFAULT_TENANT_ID="default"

# 测试数据库配置
SQLALCHEMY_DATABASE_TEST_URI="sqlite+aiosqlite://"

