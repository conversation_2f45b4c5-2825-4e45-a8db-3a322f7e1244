services:
  api:
    build:
      context: ./
      dockerfile: ./dockerfiles/python${PYTHON_VERSION}/Dockerfile
      args:
        PYTHON_VERSION: ${PYTHON_VERSION:-3.13-slim}
    ports:
      - "5001:5001"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - __pycache__:/app/__pycache__
      - pytest_cache:/app/.pytest_cache

    environment:
      - DATABASE_URL=${DATABASE_URL:-postgresql+asyncpg://funny:qinjun666@db:5432/aitools}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - SECRET_KEY=${SECRET_KEY:-09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7}
      - BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-'["http://localhost:3000","http://localhost:8080","http://localhost:5001"]'}
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai  # 替换为您的时区
      - DOCKER_TIME_SYNC=1
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    develop:
      watch:
        - action: sync
          path: ./
          target: /app
          ignore:
            - __pycache__/
            - .git/
            - .pytest_cache/
            - .dockerignore
            - .DS_Store

  # caddy:
  #   image: caddy:latest
  #   container_name: caddy
  #   restart: unless-stopped
  #   networks:
  #     - app-network
  #   env_file:
  #     - .env
  #   ports:
  #     - "5002:5002"
  #   volumes:
  #     - type: bind
  #       source: ./Caddyfile
  #       target: /etc/caddy/Caddyfile
  #     - type: volume
  #       source: caddy_data
  #       target: /data
  #     - type: volume
  #       source: caddy_config
  #       target: /config
  #   depends_on:
  #     - api
  db:
    image: postgres:latest
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-funny}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-qinjun666}
      - POSTGRES_DB=${POSTGRES_DB:-aitools}
    ports:
      - "5432:5432"
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-funny} -d ${POSTGRES_DB:-aitools}"]
      interval: 10s
      timeout: 5s
      retries: 3

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
  # caddy_data:
  # caddy_config:
  pytest_cache:
  __pycache__: