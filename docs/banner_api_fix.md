# Banner 接口修复说明

## 问题描述

在实现首页 banner 用户端接口时，遇到了以下错误：

```
AttributeError: 'AlbumService' object has no attribute 'set_cache'
```

## 问题原因

1. **缓存方法名错误**：使用了错误的方法名

    - 错误：`set_cache` 和 `get_cache`
    - 正确：`cache_resource` 和 `get_cached_resource`

2. **CacheMixin 初始化问题**：CacheMixin 需要手动设置`self.redis`属性

## 修复内容

### 1. 修复缓存方法调用

**修复前：**

```python
# 尝试从缓存获取
cached_result = await self.get_cache(cache_key)
if cached_result:
    return self.create_success_result(BannerListResponse.model_validate(cached_result))

# 缓存结果
await self.set_cache(cache_key, result.model_dump(), ttl=3600)
```

**修复后：**

```python
# 尝试从缓存获取
cached_result = await self.get_cached_resource(cache_key, lambda data: BannerListResponse.model_validate(data))
if cached_result:
    return self.create_success_result(cached_result)

# 缓存结果
await self.cache_resource(cache_key, result, expire=3600)
```

### 2. 修复 CacheMixin 初始化

**修复前：**

```python
def __init__(self, redis: Optional[Redis] = None, album_repo: Optional[AlbumRepository] = None):
    super().__init__(redis)
    self.album_repo = album_repo
```

**修复后：**

```python
def __init__(self, redis: Optional[Redis] = None, album_repo: Optional[AlbumRepository] = None):
    super().__init__(redis)
    self.redis = redis  # 为CacheMixin设置redis属性
    self.album_repo = album_repo
```

## 修复的文件

1. `svc/apps/albums/services/album.py`
    - 修复了`get_banners`方法中的缓存调用
    - 修复了构造函数中的 CacheMixin 初始化

## 验证方法

1. **启动应用**：确保应用正常启动
2. **调用接口**：访问 `GET /api/album/banner?limit=5`
3. **检查响应**：应该返回正确的 banner 数据或空列表
4. **检查日志**：确认没有缓存相关的错误

## 注意事项

1. **缓存方法使用**：

    - 使用`cache_resource`缓存资源对象
    - 使用`get_cached_resource`获取缓存，需要提供反序列化函数
    - 使用`delete_cache`删除缓存

2. **CacheMixin 要求**：

    - 必须在构造函数中设置`self.redis`属性
    - 缓存方法会自动检查`self.redis`是否存在

3. **错误处理**：
    - 缓存操作失败不会影响主要业务逻辑
    - 会自动记录缓存统计信息

## 相关文档

-   [Banner 接口使用说明](./banner_api_usage.md)
-   [BaseService 使用指南](../guide/BaseService使用指南.md)
