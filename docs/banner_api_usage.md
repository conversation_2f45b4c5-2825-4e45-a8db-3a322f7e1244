# 首页 Banner 轮播图接口使用说明

## 接口概述

新增的首页 banner 接口用于获取首页轮播图数据，支持用户端访问，无需认证权限。

## 接口详情

### 获取首页轮播图

**接口地址：** `GET /api/album/banner`

**请求参数：**

-   `limit` (可选): 返回轮播图数量限制，默认 10，最大 20

**请求示例：**

```bash
curl -X GET "http://localhost:8000/api/album/banner?limit=5"
```

**响应格式：**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "banners": [
      {
        "id": 1,
        "title": "新品上市",
        "image_url": "https://oss.example.com/banner1.jpg",
        "link_url": "https://example.com/product/1",
        "sort_order": 1,
        "description": "新品上市活动"
      }
    ],
    "total": 1
  }
}
```

## 数据配置说明

### 1. 创建 Banner 图册

在管理端创建图册时，需要：

1. 设置图册标签包含 `"banner"`
2. 设置图册状态为 `"active"`
3. 上传图片到图册中
4. 设置图片的 `sort_order` 控制显示顺序

### 2. 配置跳转链接

可以通过以下两种方式配置跳转链接：

**方式一：在图册 meta_data 中配置**

```json
{
  "name": "新品上市",
  "tags": ["banner"],
  "status": "active",
  "meta_data": {
    "link_url": "https://example.com/product/1"
  }
}
```

**方式二：在图片 meta_data 中配置**

```json
{
  "album_id": 1,
  "url": "https://oss.example.com/banner1.jpg",
  "meta_data": {
    "link_url": "https://example.com/product/1"
  }
}
```

### 3. 排序控制

-   图册按 `sort_order` 排序
-   图片按 `sort_order` 排序
-   最终 banner 列表按图片的 `sort_order` 排序

## 缓存机制

-   Banner 数据缓存 1 小时
-   缓存键格式：`banners:home:{limit}`
-   当图册或图片更新时，需要手动清除缓存

## 权限要求

-   用户端接口：无需认证，公开访问
-   管理端接口：需要相应权限

## 错误处理

接口会返回标准的错误响应格式：

```json
{
  "code": 400,
  "message": "获取首页轮播图失败: 具体错误信息",
  "data": null
}
```

## 使用建议

1. **性能优化**：建议设置合理的 limit 值，避免返回过多数据
2. **图片优化**：确保 banner 图片尺寸合适，文件大小适中
3. **链接安全**：确保跳转链接的安全性，避免恶意链接
4. **缓存管理**：定期检查缓存状态，确保数据及时更新

## 测试

可以使用提供的测试脚本进行接口测试：

```bash
python test_banner_api.py
```
