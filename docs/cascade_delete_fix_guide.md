# CASCADE 删除修复指南

## 问题描述

虽然接口返回成功，但数据没有正确删除，这是因为数据库中的外键约束还没有更新。

## 解决方案

### 1. 模型定义已更新

我们已经更新了模型定义，添加了 CASCADE 删除设置：

```python
# album_images.album_id - CASCADE DELETE
album_id = Column(BigInteger, ForeignKey("albums.id", ondelete="CASCADE"), nullable=False)

# shops.album_id - SET NULL
album_id = Column(BigInteger, ForeignKey("albums.id", ondelete="SET NULL"), nullable=True, unique=True)

# products.album_id - SET NULL
album_id = Column(BigInteger, ForeignKey("albums.id", ondelete="SET NULL"), nullable=True, unique=True)
```

### 2. 服务层逻辑已简化

删除了复杂的外键处理逻辑，现在由数据库自动处理：

```python
# 简化的删除逻辑
async def delete_shop(self, shop_id: int) -> Result[Dict[str, Any]]:
    # 逻辑删除门店
    update_data = ShopUpdate(status=ShopStatus.DELETED).model_dump(exclude_unset=True)
    await self.shop_repo.update(shop_to_delete, update_data)
    # 数据库会自动处理CASCADE删除
```

### 3. 需要手动执行数据库迁移

由于 alembic 配置问题，需要手动执行 SQL 脚本来更新数据库约束。

#### 步骤 1: 启动数据库

```bash
# 启动Docker容器
docker-compose up -d db

# 或者使用其他方式启动PostgreSQL数据库
```

#### 步骤 2: 执行 SQL 脚本

```bash
# 连接到数据库并执行修复脚本
psql $DATABASE_URL -f scripts/fix_foreign_key_constraints.sql
```

或者手动执行以下 SQL：

```sql
-- 1. 删除现有的外键约束
ALTER TABLE album_images DROP CONSTRAINT IF EXISTS album_images_album_id_fkey;
ALTER TABLE shops DROP CONSTRAINT IF EXISTS shops_album_id_fkey;
ALTER TABLE products DROP CONSTRAINT IF EXISTS products_album_id_fkey;

-- 2. 重新创建外键约束，添加CASCADE删除设置
ALTER TABLE album_images
ADD CONSTRAINT album_images_album_id_fkey
FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE CASCADE;

ALTER TABLE shops
ADD CONSTRAINT shops_album_id_fkey
FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE SET NULL;

ALTER TABLE products
ADD CONSTRAINT products_album_id_fkey
FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE SET NULL;
```

#### 步骤 3: 验证约束

```sql
-- 验证约束是否正确创建
SELECT
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.table_name IN ('album_images', 'shops', 'products')
    AND tc.constraint_type = 'FOREIGN KEY'
    AND tc.constraint_name LIKE '%album_id%';
```

预期结果：

```
table_name    | constraint_name           | constraint_type | delete_rule
--------------|---------------------------|-----------------|------------
album_images  | album_images_album_id_fkey| FOREIGN KEY     | CASCADE
shops         | shops_album_id_fkey       | FOREIGN KEY     | SET NULL
products      | products_album_id_fkey    | FOREIGN KEY     | SET NULL
```

### 4. 测试验证

执行测试脚本验证修复效果：

```bash
python scripts/test_cascade_delete.py
```

## CASCADE 删除策略说明

### 删除相册时的行为：

1. **album_images**: 删除相册时，自动删除所有相关图片（CASCADE DELETE）
2. **shops**: 删除相册时，自动将引用该相册的门店 album_id 设为 NULL（SET NULL）
3. **products**: 删除相册时，自动将引用该相册的商品 album_id 设为 NULL（SET NULL）

### 删除门店/商品时的行为：

1. 门店/商品被逻辑删除（状态设为 DELETED）
2. 相册保持不变（因为门店/商品只是状态变化，不是物理删除）

## 优势

1. **数据库层面处理**: 由数据库自动处理，性能更好
2. **代码简洁**: 服务层逻辑简化，维护成本更低
3. **原子性**: 要么全部成功要么全部失败
4. **一致性**: 保证数据完整性

## 注意事项

1. 执行 SQL 脚本前请备份数据库
2. 在生产环境中执行前请在测试环境验证
3. 确保所有相关服务都已停止，避免并发访问问题

## 验证清单

-   [ ] 数据库约束已更新
-   [ ] 删除相册时，相关图片被正确删除
-   [ ] 删除相册时，门店和商品的 album_id 被正确设为 NULL
-   [ ] 删除门店/商品时，状态被正确更新
-   [ ] 接口返回成功且数据确实被删除
