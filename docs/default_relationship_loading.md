# 默认关系加载指南

## 概述

为了简化服务层的代码，提高开发效率，我们在仓库层实现了默认关系加载功能。现在每个仓库类都会根
据其业务需求自动加载相应的关系数据，服务层无需手动传递`load_options`参数。

## 默认关系加载策略

### ProductRepository（商品仓库）

**默认加载的关系：**

-   `category`: 商品分类（selectin 关系）
-   `album`: 商品图册（selectin 关系）

**使用示例：**

```python
# 服务层代码 - 无需传递load_options
product = await product_repo.get_by_id(1)
print(product.category.name)  # 自动加载，无需额外查询
print(product.album.name)     # 自动加载，无需额外查询

# 分页查询也会自动加载关系
products, total = await product_repo.get_paginated(
    page_num=1,
    page_size=20
)
for product in products:
    print(f"{product.name} - {product.category.name}")  # 关系已预加载
```

### CategoryRepository（分类仓库）

**默认加载的关系：**

-   `parent`: 父分类（selectin 关系）
-   `children`: 子分类（selectin 关系）

**使用示例：**

```python
# 自动加载父子关系
category = await category_repo.get_by_id(1)
if category.parent:
    print(f"父分类: {category.parent.name}")
for child in category.children:
    print(f"子分类: {child.name}")
```

### SpecRepository（规格仓库）

**默认加载的关系：**

-   `categories`: 关联的分类（selectin 关系）
-   `options`: 规格选项（selectin 关系）

**使用示例：**

```python
# 自动加载分类和选项
spec = await spec_repo.get_by_id(1)
for category in spec.categories:
    print(f"分类: {category.name}")
for option in spec.options:
    print(f"选项: {option.value}")
```

### SpecOptionRepository（规格选项仓库）

**默认加载的关系：**

-   `spec`: 所属规格（selectin 关系）
-   `skus`: 关联的 SKU（selectin 关系）

**使用示例：**

```python
# 自动加载规格和SKU信息
option = await spec_option_repo.get_by_id(1)
print(f"规格: {option.spec.name}")
for sku in option.skus:
    print(f"SKU: {sku.sku}")
```

### ProductSKURepository（SKU 仓库）

**默认加载的关系：**

-   `product`: 所属商品（selectin 关系）
-   `spec_options`: 规格选项（selectin 关系）

**使用示例：**

```python
# 自动加载商品和规格选项
sku = await sku_repo.get_by_id(1)
print(f"商品: {sku.product.name}")
for option in sku.spec_options:
    print(f"规格选项: {option.value}")
```

### ShopRepository（店铺仓库）

**默认加载的关系：**

-   `album`: 店铺图册（selectin 关系）

**使用示例：**

```python
# 自动加载图册信息
shop = await shop_repo.get_by_id(1)
if shop.album:
    print(f"店铺图册: {shop.album.name}")
```

### AlbumRepository（图册仓库）

**默认加载的关系：**

-   `images`: 图册图片（selectin 关系）
-   `cover_image`: 封面图片（selectin 关系）

**使用示例：**

```python
# 自动加载图片信息
album = await album_repo.get_by_id(1)
print(f"封面图片: {album.cover_image.url if album.cover_image else '无'}")
for image in album.images:
    print(f"图片: {image.url}")
```

### InventoryRepository（库存仓库）

**默认加载的关系：**

-   `product`: 所属商品（selectin 关系）

**使用示例：**

```python
# 自动加载商品信息
inventory = await inventory_repo.get_by_id(1)
print(f"商品: {inventory.product.name}")
```

### SubscriptionRepository（订阅仓库）

**默认加载的关系：**

-   `plan`: 订阅计划（selectin 关系）

**使用示例：**

```python
# 自动加载订阅计划信息
subscription = await subscription_repo.get_by_id(1)
print(f"订阅计划: {subscription.plan.name}")
```

### InvoiceRepository（账单仓库）

**默认加载的关系：**

-   `subscription`: 关联的订阅（selectin 关系）
-   `payments`: 支付记录（selectin 关系）

**使用示例：**

```python
# 自动加载订阅和支付信息
invoice = await invoice_repo.get_by_id(1)
print(f"订阅: {invoice.subscription.id}")
for payment in invoice.payments:
    print(f"支付: {payment.payment_id}")
```

### PaymentRepository（支付仓库）

**默认加载的关系：**

-   `invoice`: 关联的账单（selectin 关系）

**使用示例：**

```python
# 自动加载账单信息
payment = await payment_repo.get_by_id(1)
print(f"账单: {payment.invoice.invoice_number}")
```

### SubscriptionPlanRepository（订阅计划仓库）

**默认加载的关系：**

-   `subscriptions`: 关联的订阅（selectin 关系）

**使用示例：**

```python
# 自动加载订阅信息
plan = await subscription_plan_repo.get_by_id(1)
for subscription in plan.subscriptions:
    print(f"订阅: {subscription.id}")
```

### UserRepository（用户仓库）

**默认加载的关系：**

-   `wechat_users`: 关联的微信用户（selectin 关系）

**使用示例：**

```python
# 自动加载微信用户信息
user = await user_repo.get_by_id(1)
for wechat_user in user.wechat_users:
    print(f"微信用户: {wechat_user.openid}")
```

### RoleRepository（角色仓库）

**默认加载的关系：**

-   `permissions`: 关联的权限（selectin 关系）

**使用示例：**

```python
# 自动加载权限信息
role = await role_repo.get_by_id(1)
for permission in role.permissions:
    print(f"权限: {permission.name}")
```

### CampaignRepository（活动仓库）

**默认加载的关系：**

-   `reward_strategies`: 奖励策略（selectin 关系）

**使用示例：**

```python
# 自动加载奖励策略信息
campaign = await campaign_repo.get_by_id(1)
for strategy in campaign.reward_strategies:
    print(f"奖励策略: {strategy.name}")
```

## 优势

### 1. 简化服务层代码

**之前：**

```python
# 需要手动指定load_options
product = await product_repo.get_by_id(
    1,
    load_options=[selectinload(Product.category), selectinload(Product.album)]
)
```

**现在：**

```python
# 自动加载关系，代码更简洁
product = await product_repo.get_by_id(1)
```

### 2. 避免遗漏关系加载

由于关系加载是默认的，开发者不会忘记加载必要的关系数据，减少了关系数据为空的问题。

### 3. 提高开发效率

服务层开发者可以专注于业务逻辑，而不需要关心底层的关系加载细节。

### 4. 保持灵活性

虽然提供了默认加载，但仍然支持手动指定`load_options`来覆盖默认行为：

```python
# 覆盖默认加载，只加载特定关系
product = await product_repo.get_by_id(
    1,
    load_options=[selectinload(Product.category)]  # 只加载分类，不加载图册
)
```

## 性能考虑

### 1. 自动加载的影响

默认关系加载会增加查询的复杂度，但对于大多数业务场景来说，这些关系数据是必需的，预加载可以避
免 N+1 查询问题。

### 2. 性能优化建议

-   **监控查询性能**：关注日志中的关系加载信息
-   **按需覆盖**：如果某些查询不需要关系数据，可以传递空的 load_options
-   **使用缓存**：对于频繁访问的数据，考虑在服务层使用缓存

```python
# 如果不需要关系数据，可以传递空列表
products = await product_repo.get_list(load_options=[])
```

## 最佳实践

### 1. 服务层代码

```python
class ProductService:
    async def get_product_detail(self, product_id: int):
        # 无需关心关系加载，仓库层会自动处理
        product = await self.product_repo.get_by_id(product_id)

        # 直接使用关系数据
        return {
            "id": product.id,
            "name": product.name,
            "category": product.category.name if product.category else None,
            "album": product.album.name if product.album else None
        }
```

### 2. 列表查询

```python
async def get_product_list(self, page_num: int, page_size: int):
    # 分页查询也会自动加载关系
    products, total = await self.product_repo.get_paginated(
        page_num=page_num,
        page_size=page_size
    )

    # 构建响应时直接使用关系数据
    return [{
        "id": product.id,
        "name": product.name,
        "category": product.category.name if product.category else None
    } for product in products]
```

### 3. 搜索功能

```python
async def search_products(self, search_term: str):
    # 搜索结果也会自动加载关系
    products = await self.product_repo.search_products(search_term)

    return [{
        "id": product.id,
        "name": product.name,
        "category": product.category.name if product.category else None
    } for product in products]
```

## 总结

默认关系加载功能大大简化了服务层的开发工作，提高了代码的可维护性和开发效率。开发者现在可以专
注于业务逻辑，而不需要担心关系数据的加载问题。同时，系统仍然保持了足够的灵活性，允许在需要时
覆盖默认的加载行为。
