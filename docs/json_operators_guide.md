# JSON 操作符使用指南

## 概述

基类现在支持多种 JSON 操作符，可以用于查询 JSON 类型的字段。

## 支持的 JSON 操作符

### 1. JSON_CONTAINS - JSON 数组包含检查

**用途**: 检查 JSON 数组是否包含特定值

**语法**: `field__json_contains`

**示例**:

```python
# 检查 tags 数组是否包含 "banner"
filters = {"tags__json_contains": ["banner"]}

# 检查 tags 数组是否包含 "featured" 或 "popular"
filters = {
    "__or__": {
        "tags__json_contains": ["featured"],
        "tags__json_contains": ["popular"]
    }
}
```

**生成的 SQL**: `tags @> '["banner"]'`

### 2. JSON_EXTRACT - JSON 字段提取

**用途**: 从 JSON 对象中提取特定路径的值进行比较

**语法**: `field__json_extract`

**示例**:

```python
# 检查 meta_data.user.role 是否等于 "admin"
filters = {"meta_data__json_extract": ["user.role", "admin"]}

# 检查 meta_data.settings.theme 是否等于 "dark"
filters = {"meta_data__json_extract": ["settings.theme", "dark"]}
```

**生成的 SQL**: `meta_data->'user'->>'role' = 'admin'`

### 3. JSON_SEARCH - JSON 字符串搜索

**用途**: 在 JSON 字符串中搜索文本（类似 ILIKE）

**语法**: `field__json_search`

**示例**:

```python
# 在 meta_data.description 中搜索包含 "重要" 的记录
filters = {"meta_data__json_search": "%重要%"}
```

**生成的 SQL**: `meta_data::text ILIKE '%重要%'`

### 4. JSON_HAS_KEY - JSON 对象包含键

**用途**: 检查 JSON 对象是否包含特定键

**语法**: `field__json_has_key`

**示例**:

```python
# 检查 meta_data 是否包含 "user" 键
filters = {"meta_data__json_has_key": "user"}

# 检查 meta_data 是否包含 "settings" 键
filters = {"meta_data__json_has_key": "settings"}
```

**生成的 SQL**: `meta_data ? 'user'`

## 在 Repository 中的使用示例

### AlbumRepository 示例

```python
async def get_albums(
    self,
    skip: int = 0,
    limit: int = 10,
    status: Optional[str] = None,
    tags: Optional[List[str]] = None,
    search_term: Optional[str] = None,
    order_by: str = "sort_order",
    order_desc: bool = False
) -> Tuple[List[Album], int]:
    """获取图册列表及总数，支持标签、状态、搜索、排序"""
    simple_filters = {}
    if status:
        simple_filters["status"] = status

    adv_filters = {}

    # 使用 JSON 操作符实现 tags 搜索
    if tags:
        or_group = {"tags__json_contains": [tag] for tag in tags}
        adv_filters["__or__"] = or_group

    # 搜索条件
    if search_term:
        if "__or__" in adv_filters:
            # 如果已经有 __or__ 分组，合并搜索条件
            adv_filters["__or__"].update({
                "name__ilike": f"%{search_term}%",
                "description__ilike": f"%{search_term}%",
            })
        else:
            # 如果没有 __or__ 分组，创建新的
            adv_filters["__or__"] = {
                "name__ilike": f"%{search_term}%",
                "description__ilike": f"%{search_term}%",
            }

    items, total = await self.get_paginated(
        page_num=(skip // limit) + 1,
        page_size=limit,
        order_by=order_by,
        order_direction="desc" if order_desc else "asc",
        filters=adv_filters or None,
        **simple_filters,
    )
    return items, total
```

### 复杂查询示例

```python
# 复杂的 JSON 查询示例
filters = {
    # 基础条件
    "status": "active",

    # JSON 数组包含多个标签
    "__or__": {
        "tags__json_contains": ["featured"],
        "tags__json_contains": ["popular"]
    },

    # JSON 对象包含特定键
    "meta_data__json_has_key": "user",

    # JSON 字段提取
    "meta_data__json_extract": ["user.role", "admin"]
}

# 执行查询
albums, total = await album_repo.get_paginated(
    page_num=1,
    page_size=10,
    filters=filters
)
```

## 注意事项

1. **JSON 数组格式**: 使用 `JSON_CONTAINS` 时，传入的值应该是数组格式
2. **路径格式**: 使用 `JSON_EXTRACT` 时，路径使用点号分隔，如 `"user.role"`
3. **性能考虑**: JSON 查询可能比普通字段查询慢，建议在 JSON 字段上创建适当的索引
4. **数据库兼容性**: 这些操作符主要针对 PostgreSQL，其他数据库可能需要不同的实现

## 索引建议

为了提高 JSON 查询的性能，建议在 PostgreSQL 中创建以下索引：

```sql
-- 为 JSON 数组字段创建 GIN 索引
CREATE INDEX idx_albums_tags ON albums USING GIN (tags);

-- 为 JSON 对象字段创建 GIN 索引
CREATE INDEX idx_albums_meta_data ON albums USING GIN (meta_data);

-- 为 JSON 路径查询创建索引
CREATE INDEX idx_albums_meta_data_user_role ON albums USING GIN ((meta_data->'user'->>'role'));
```
