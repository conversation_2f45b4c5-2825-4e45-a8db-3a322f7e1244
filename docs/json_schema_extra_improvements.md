# JSON Schema Extra 改进总结

## 概述

本次改进工作针对项目中所有 schema 文件的`json_schema_extra`配置进行了全面优化，使示例数据更
加真实、完整和实用。**更新为头盔行业数据**，使用 SHOEI 等知名头盔品牌作为示例。

## 改进原则

1. **真实性**：使用真实业务场景的数据
2. **完整性**：提供完整的示例，包括所有重要字段
3. **一致性**：确保相关字段之间的数据逻辑一致
4. **实用性**：示例应该能帮助开发者理解 API 的使用

## 主要改进内容

### 1. 产品管理模块 (products)

#### ProductBase

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了完整的 SHOEI GT-AIR II 全盔摩托车头盔产品示例
    -   真实的产品名称、描述、SKU
    -   合理的价格范围（399900 分 = 3999 元）
    -   详细的商品属性（品牌、型号、类型、尺寸、颜色等）
    -   真实的尺寸和重量数据
    -   SEO 相关信息

#### ProductUpdate

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了产品更新示例
    -   价格调整示例（359900 分）
    -   库存更新
    -   促销信息

#### ProductResponse

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了完整的响应示例
    -   包含所有字段的完整数据
    -   真实的时间戳

#### GetProductsParams

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了查询参数示例
    -   合理的分页参数
    -   价格范围过滤（200000-800000 分）
    -   搜索关键词（"SHOEI 头盔"）

#### UserProductResponse

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了用户端产品响应示例
    -   包含嵌套的图册信息
    -   用户友好的价格格式

### 2. 分类管理模块 (category)

#### CategoryBase

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了摩托车头盔分类示例
    -   真实的中文分类名称
    -   详细的分类描述
    -   元数据信息

#### CategoryResponse

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了完整的分类响应示例
    -   包含关联的规格信息（品牌、头盔类型）
    -   时间戳信息

#### UserCategoryResponse

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：添加了用户端分类示例
    -   产品数量统计
    -   热门品牌信息（SHOEI、ARAI、AGV、HJC）
    -   价格范围（¥299-¥8999）

### 3. 库存管理模块 (inventory)

#### InventoryBase

-   **改进前**：简单的示例数据
-   **改进后**：真实的头盔库存数据
    -   具体的仓库位置（上海头盔仓库）
    -   详细的货架位置编码
    -   真实的批次号和批号
    -   合理的成本价格（280000 分）

#### InventoryUpdate

-   **改进前**：简单的更新示例
-   **改进后**：详细的库存调整示例
    -   库存变化说明
    -   货架位置调整

### 4. 用户认证模块 (auth)

#### AuthCredentials

-   **改进前**：简单的英文示例
-   **改进后**：真实的中文用户数据
    -   中文姓名（张伟）
    -   真实的邮箱格式
    -   安全的密码示例

#### TokenData

-   **改进前**：简化的 JWT 示例
-   **改进后**：真实的 JWT 令牌示例
    -   完整的 JWT 格式
    -   合理的过期时间
    -   真实的用户 ID

#### UserBase

-   **改进前**：英文用户示例
-   **改进后**：中文用户示例
    -   中文姓名
    -   真实的中文用户名

### 5. 门店管理模块 (shops)

#### ShopBase

-   **改进前**：简单的门店信息
-   **改进后**：详细的中国门店数据
    -   真实的中国地址（上海南京路）
    -   中文营业时间格式
    -   详细的扩展信息（面积、停车位、员工数等）

#### ShopUpdate

-   **改进前**：简单的更新示例
-   **改进后**：装修升级示例
    -   装修状态信息
    -   服务项目扩展

### 6. 账单管理模块 (billing)

#### GetInvoiceParams

-   **改进前**：简单的 ID 示例
-   **改进后**：真实的账单查询示例
    -   合理的 ID 范围
    -   用户关联

#### CreateInvoiceParams

-   **改进前**：空的示例数据
-   **改进后**：完整的账单创建示例
    -   具体的头盔商品信息
    -   真实的金额和币种
    -   详细的商品描述

#### MarkAsPaidParams

-   **改进前**：简单的支付标记
-   **改进后**：详细的支付信息
    -   支付方式（支付宝）
    -   交易 ID
    -   支付时间

### 7. 支付管理模块 (payment)

#### GetPaymentParams

-   **改进前**：简单的 ID 示例
-   **改进后**：真实的支付记录查询
    -   合理的 ID 范围

#### CreatePaymentParams

-   **改进前**：空的示例数据
-   **改进后**：完整的支付创建示例
    -   具体的头盔商品信息
    -   支付方式
    -   元数据信息

#### ProcessPaymentCallbackParams

-   **改进前**：空的回调数据
-   **改进后**：真实的支付回调示例
    -   完整的回调数据结构
    -   签名验证信息

### 8. 营销管理模块 (marketing)

#### RewardStrategyBase

-   **改进前**：简单的奖励策略
-   **改进后**：详细的 SHOEI 头盔邀请奖励计划
    -   具体的产品关联
    -   阶梯式奖励配置
    -   防滥用策略

#### CampaignBase

-   **改进前**：简单的活动信息
-   **改进后**：详细的 SHOEI 头盔春季活动
    -   具体的活动时间
    -   参与人数限制
    -   防滥用策略

### 9. 图册管理模块 (albums)

#### AlbumBase

-   **改进前**：简单的图册信息
-   **改进后**：详细的 SHOEI 头盔产品图册
    -   真实的产品图册名称
    -   详细的标签信息
    -   品牌和产品类型元数据

#### AlbumResponse

-   **改进前**：简单的响应示例
-   **改进后**：完整的图册响应示例
    -   包含图片列表
    -   真实的时间戳

#### AlbumImageBase

-   **改进前**：简单的图片信息
-   **改进后**：详细的头盔产品图片示例
    -   真实的图片文件名和 URL
    -   详细的图片元数据
    -   品牌和产品类型信息

#### AlbumImageUpdate

-   **改进前**：简单的更新示例
-   **改进后**：详细的图片更新示例
    -   图片类型和场景信息
    -   品牌相关元数据

### 10. 订阅管理模块 (subscription)

#### CreateSubscriptionParams

-   **改进前**：空的示例数据
-   **改进后**：完整的订阅创建示例
    -   具体的头盔产品信息
    -   订阅类型和元数据

#### UpdateSubscriptionParams

-   **改进前**：空的示例数据
-   **改进后**：详细的订阅更新示例
    -   数量调整
    -   更新原因说明

#### CancelSubscriptionParams

-   **改进前**：简单的取消参数
-   **改进后**：详细的取消示例
    -   取消原因
    -   用户关联

### 11. 订阅计划模块 (subscription_plan)

#### GetPlanParams

-   **改进前**：简单的 ID 示例
-   **改进后**：真实的计划查询示例
    -   合理的用户 ID 范围

#### GetPlanByNameParams

-   **改进前**：通用的计划名称
-   **改进后**：头盔行业特定的计划名称
    -   "SHOEI 头盔月度订阅"

#### CreatePlanParams

-   **改进前**：空的示例数据
-   **改进后**：完整的计划创建示例
    -   头盔产品订阅计划
    -   详细的功能特性
    -   行业相关的元数据

#### SubscriptionPlanBase

-   **改进前**：通用的订阅计划
-   **改进后**：头盔行业订阅计划
    -   产品类型和品牌信息
    -   保修和配送服务
    -   目标用户群体

### 12. 角色管理模块 (role)

#### RoleBase

-   **改进前**：通用的管理员角色
-   **改进后**：头盔行业特定角色
    -   "helmet_manager" 头盔产品管理员
    -   行业相关的角色描述

#### RoleCreate

-   **改进前**：通用权限
-   **改进后**：头盔产品相关权限
    -   产品读写权限
    -   库存管理权限

#### RoleUpdate

-   **改进前**：简单的角色更新
-   **改进后**：详细的角色升级示例
    -   高级头盔产品管理员
    -   扩展的权限范围

### 13. 邀请管理模块 (invitation)

#### InvitationCreate

-   **改进前**：简单的邀请示例
-   **改进后**：头盔活动邀请示例
    -   真实的用户 ID 范围
    -   具体的邮箱地址

#### InvitationUpdate

-   **改进前**：简单的状态更新
-   **改进后**：详细的邀请接受示例
    -   被邀请人 ID
    -   邀请状态变更

#### InvitationCodeResponse

-   **改进前**：通用的邀请码
-   **改进后**：头盔品牌邀请码
    -   "SHOEI2024" 品牌相关邀请码
    -   品牌化的邀请链接

#### InvitationResponse

-   **改进前**：简单的邀请信息
-   **改进后**：完整的邀请响应示例
    -   真实的用户 ID
    -   邀请码使用状态
    -   打开次数统计

#### InvitationStatsResponse

-   **改进前**：简单的统计数据
-   **改进后**：详细的邀请统计
    -   真实的邀请数量
    -   转化率计算
    -   活动关联

### 14. 奖励管理模块 (reward)

#### RewardRecordResponse

-   **改进前**：简单的奖励记录
-   **改进后**：详细的头盔邀请奖励
    -   具体的奖励金额
    -   奖励描述和发放状态
    -   真实的时间戳

#### RewardStatsResponse

-   **改进前**：简单的统计信息
-   **改进后**：详细的奖励统计
    -   已发放和待发放奖励
    -   总金额统计
    -   活动关联

### 15. 规格管理模块 (spec)

#### SpecBase

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：头盔尺寸规格示例
    -   头盔尺寸规格名称
    -   品牌和单位信息
    -   行业相关元数据

#### SpecUpdate

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：规格升级示例
    -   版本升级信息
    -   更多尺寸选项

#### SpecOptionBase

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：头盔尺寸选项示例
    -   具体的尺寸值（L）
    -   头围和重量信息
    -   颜色和库存信息

#### SpecOptionUpdate

-   **改进前**：无 json_schema_extra 配置
-   **改进后**：选项更新示例
    -   更详细的尺寸描述
    -   库存信息更新

#### SpecBatchUpdate

-   **改进前**：简单的批量更新
-   **改进后**：头盔规格批量更新
    -   行业相关的描述
    -   排序和状态调整

### 16. 审计日志模块 (audit_log)

#### AuditLogBatchDeleteRequest

-   **改进前**：简单的删除请求
-   **改进后**：头盔产品相关审计日志清理
    -   更多的资源 ID
    -   清理原因说明

## 数据标准化

### 1. ID 范围规范

-   用户 ID：101-999
-   产品 ID：1-999
-   订单 ID：1001-9999
-   支付 ID：2001-9999
-   账单 ID：1001-9999
-   订阅 ID：501-999

### 2. 价格规范

-   使用分为单位（1 元 = 100 分）
-   头盔价格范围：29900-899900 分（299-8999 元）
-   成本价通常为标准价的 70%

### 3. 时间规范

-   使用 ISO 8601 格式
-   创建时间：2024-01-15T10:30:00
-   更新时间：2024-03-24T14:20:00
-   过期时间：2026-12-31T23:59:59

### 4. 地址规范

-   使用真实的中国地址
-   城市：上海、北京、广州等
-   详细地址包含楼层和门牌号

### 5. 联系方式规范

-   手机号：138-xxxx-xxxx 格式
-   固定电话：021-xxxx-xxxx 格式
-   邮箱：中文名.姓@example.com 格式

### 6. 头盔行业特定规范

-   **品牌**：SHOEI、ARAI、AGV、HJC 等知名品牌
-   **类型**：全盔、半盔、揭面盔、越野盔
-   **安全标准**：ECE 22.05、DOT、SNELL 等
-   **材质**：多层复合纤维、碳纤维、聚碳酸酯等
-   **尺寸**：XS、S、M、L、XL、XXL
-   **颜色**：哑光黑、亮光白、金属银等

### 7. 图片和文件规范

-   **图片尺寸**：1200x800 像素
-   **文件大小**：245760 字节（约 240KB）
-   **图片类型**：front_view、side_view 等
-   **文件格式**：JPEG、PNG

### 8. 营销活动规范

-   **邀请码**：品牌相关（如 SHOEI2024）
-   **奖励金额**：200-5000 元范围
-   **转化率**：0.5-0.6 之间
-   **活动规模**：1000-3000 人参与

## 改进效果

1. **提升开发体验**：开发者可以通过示例快速理解 API 的使用方法
2. **减少错误**：真实的示例数据减少了测试时的错误
3. **提高文档质量**：自动生成的 API 文档更加实用
4. **增强一致性**：标准化的数据格式提高了系统的一致性
5. **行业专业性**：使用头盔行业真实数据，提升系统的专业性
6. **完整性覆盖**：涵盖了所有主要模块，无遗漏

## 后续建议

1. **定期更新**：根据业务发展定期更新示例数据
2. **添加更多场景**：为不同的业务场景添加更多示例
3. **国际化支持**：考虑添加多语言示例数据
4. **验证自动化**：建立自动化测试验证示例数据的有效性
5. **行业扩展**：考虑扩展到其他摩托车配件行业
6. **动态示例**：考虑根据用户角色动态生成相关示例
