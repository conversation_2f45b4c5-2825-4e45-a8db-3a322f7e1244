# 关系加载最佳实践

## 概述

本文档介绍在使用新的关系加载方案时的最佳实践，帮助开发者正确使用关系加载功能，避免常见的性能
问题。

## 基本原则

### 1. 按需加载原则

只在需要关系数据时才加载，避免过度加载：

```python
# ✅ 好的做法：只在需要时加载关系
product = await product_repo.get_by_id(1)  # 自动加载selectin关系
print(product.category.name)  # 直接访问，无需额外查询

# ❌ 避免：过度加载不必要的关系
product = await product_repo.get_by_id(
    1,
    load_options=[
        selectinload(Product.category),
        selectinload(Product.album),
        selectinload(Product.inventories),  # 如果不需要库存信息
        selectinload(Product.skus)          # 如果不需要SKU信息
    ]
)
```

### 2. 使用专门的加载方法

对于复杂的关系加载需求，使用仓库提供的专门方法：

```python
# ✅ 使用专门的方法加载完整关系
product = await product_repo.get_product_with_inventory_and_skus(1)

# ✅ 使用专门的方法加载特定关系组合
products = await product_repo.get_products_with_category_and_album(
    page_num=1,
    page_size=10
)
```

### 3. 避免 N+1 查询

使用预加载而不是逐个查询关系：

```python
# ❌ 避免：N+1查询问题
products = await product_repo.get_list()
for product in products:
    category = await category_repo.get_by_id(product.category_id)  # 额外查询
    print(f"{product.name} - {category.name}")

# ✅ 好的做法：预加载关系
products = await product_repo.get_products_with_category_and_album()
for product in products:
    print(f"{product.name} - {product.category.name}")  # 无需额外查询
```

## 不同关系类型的处理

### selectin 关系（自动加载）

```python
# 这些关系会自动加载，无需手动指定
product = await product_repo.get_by_id(1)
print(product.category.name)  # 自动加载
print(product.album.name)     # 自动加载
```

### dynamic 关系（手动加载）

```python
# 需要手动加载dynamic关系
product = await product_repo.get_product_with_inventory_and_skus(1)
# 或者
product = await product_repo.get_by_id(1)
product = await product_repo.load_dynamic_relations(product, ['inventories', 'skus'])
```

### 多对多关系

```python
# 多对多关系使用selectinload
categories = await category_repo.get_categories_with_specs()
for category in categories:
    for spec in category.specs:  # 预加载的关系
        print(f"{category.name} - {spec.name}")
```

## 性能优化建议

### 1. 监控查询日志

关注日志中的关系加载信息：

```
DEBUG: Applied load options: [selectinload(Product.category), selectinload(Product.album)]
DEBUG: Loaded dynamic relation inventories for Product
```

### 2. 使用缓存

对于频繁访问的关系数据，考虑使用缓存：

```python
# 在服务层使用缓存
@cache_result(ttl=300)  # 缓存5分钟
async def get_product_with_relations(self, product_id: int):
    return await self.product_repo.get_product_with_inventory_and_skus(product_id)
```

### 3. 分页查询优化

在分页查询中合理使用关系加载：

```python
# 对于列表页面，只加载必要的关系
products = await product_repo.get_products_with_category_and_album(
    page_num=1,
    page_size=20
)

# 对于详情页面，加载完整关系
product = await product_repo.get_product_with_inventory_and_skus(product_id)
```

## 常见场景示例

### 1. 商品列表页面

```python
# 只需要分类和图册信息
products, total = await product_repo.get_products_with_category_and_album(
    page_num=1,
    page_size=20,
    status="active"
)
```

### 2. 商品详情页面

```python
# 需要完整的关系信息
product = await product_repo.get_product_with_inventory_and_skus(product_id)
```

### 3. 分类管理页面

```python
# 需要分类的规格信息
categories = await category_repo.get_categories_with_specs(
    status="active"
)
```

### 4. 搜索功能

```python
# 搜索结果通常需要基本关系信息
products = await product_repo.search_products(
    search_term="手机",
    load_options=[selectinload(Product.category)]
)
```

## 调试技巧

### 1. 检查关系是否正确加载

```python
# 检查关系是否已加载
product = await product_repo.get_by_id(1)
print(hasattr(product, '_category_loaded'))  # 检查是否已加载
print(product.category.name)  # 访问关系数据
```

### 2. 使用 SQL 日志

启用 SQL 日志来查看实际执行的查询：

```python
# 在开发环境中启用SQL日志
import logging
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

### 3. 性能分析

使用性能分析工具监控查询性能：

```python
import time

start_time = time.time()
products = await product_repo.get_products_with_full_relations()
end_time = time.time()

print(f"查询耗时: {end_time - start_time:.2f}秒")
```

## 错误处理

### 1. 关系不存在

```python
try:
    product = await product_repo.get_by_id(1)
    category_name = product.category.name if product.category else "无分类"
except Exception as e:
    logger.error(f"获取商品关系失败: {str(e)}")
    category_name = "获取失败"
```

### 2. 关系加载失败

```python
# 关系加载失败会被记录在日志中，但不会抛出异常
product = await product_repo.get_by_id(
    1,
    load_options=[selectinload(Product.non_existent_relation)]
)
# 查询会继续执行，但关系不会被加载
```

## 总结

遵循这些最佳实践可以：

1. **提高性能**：避免不必要的查询和过度加载
2. **减少错误**：正确处理不同类型的关系
3. **提升可维护性**：使用统一的关系加载模式
4. **优化用户体验**：快速响应用户请求

记住：关系加载是一个平衡性能和功能的过程，需要根据具体的业务场景来选择合适的加载策略。
