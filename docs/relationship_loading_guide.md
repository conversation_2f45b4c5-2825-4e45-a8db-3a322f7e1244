# 关系加载指南

## 概述

本文档介绍项目中关系表加载的最佳实践和解决方案。

## 问题背景

在查询有关系表的实体时，有时关系数据没有被正确加载，导致返回的数据不完整。

## 解决方案

### 1. 自动关系加载

QueryMixin 现在支持自动加载`lazy="selectin"`关系：

```python
# 自动加载selectin关系
product = await product_repo.get_by_id(1)
# category和album关系会自动加载
print(product.category.name)
print(product.album.name)
```

### 2. 手动指定加载选项

```python
from sqlalchemy.orm import selectinload, joinedload

# 手动指定加载选项
product = await product_repo.get_by_id(
    1,
    load_options=[
        selectinload(Product.category),
        selectinload(Product.album).selectinload(Album.cover_image)
    ]
)
```

### 3. 加载 dynamic 关系

对于`lazy="dynamic"`关系，使用专门的加载方法：

```python
# 加载dynamic关系
product = await product_repo.get_with_dynamic_relations(
    1,
    relation_names=['inventories', 'skus']
)

# 或者手动加载
product = await product_repo.get_by_id(1)
product = await product_repo.load_dynamic_relations(
    product,
    ['inventories', 'skus']
)
```

### 4. 分页查询中的关系加载

```python
# 分页查询时加载关系
products, total = await product_repo.get_paginated(
    page_num=1,
    page_size=10,
    load_options=[
        selectinload(Product.category),
        selectinload(Product.album)
    ]
)
```

## 关系类型说明

### selectin 关系

-   自动预加载
-   适用于一对多关系
-   性能较好

### dynamic 关系

-   需要手动加载
-   适用于一对多关系
-   支持额外的查询条件

### 多对多关系

-   使用 selectinload 预加载
-   适用于多对多关系

## 最佳实践

1. **优先使用自动加载**：对于`lazy="selectin"`关系，让系统自动处理
2. **按需加载**：只在需要关系数据时才指定 load_options
3. **避免 N+1 查询**：使用预加载而不是逐个查询关系
4. **监控性能**：关注查询日志，避免过度加载

## 调试技巧

1. **查看日志**：关系加载的详细信息会记录在日志中
2. **检查关系定义**：确认模型中的关系配置正确
3. **验证数据**：确保关系表中存在对应的数据

## 常见问题

### Q: 关系数据为空怎么办？

A: 检查以下几点：

-   关系表中是否存在对应数据
-   外键约束是否正确
-   软删除是否影响了关系数据

### Q: 性能问题如何解决？

A:

-   使用 selectinload 而不是 joinedload
-   避免加载不必要的关系
-   考虑使用缓存

### Q: 如何处理复杂的嵌套关系？

A: 使用嵌套的 selectinload：

```python
load_options=[
    selectinload(Product.category),
    selectinload(Product.album).selectinload(Album.images)
]
```
