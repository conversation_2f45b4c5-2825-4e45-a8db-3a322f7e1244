# 通用奖励系统使用指南

## 概述

新的通用奖励系统支持多种奖励类型和触发条件，通过配置化的方式实现灵活的奖励策略。

## 核心概念

### 1. 奖励策略 (RewardStrategy)

奖励策略定义了奖励的触发条件、计算规则和分发方式。

#### 策略类型

-   `fixed`: 固定金额奖励
-   `percentage`: 百分比奖励
-   `tiered`: 阶梯奖励
-   `conditional`: 条件奖励

#### 配置字段

-   `trigger_config`: 触发条件配置
-   `target_config`: 目标对象配置
-   `calculation_config`: 奖励计算配置
-   `constraint_config`: 限制条件配置
-   `distribution_config`: 分发配置

### 2. 触发条件配置

```json
{
  "type": "event",
  "events": ["invitation.completed", "registration.completed"]
}
```

或者条件触发：

```json
{
  "type": "condition",
  "conditions": [
    {
      "field": "user_level",
      "operator": ">=",
      "value": 3
    }
  ]
}
```

### 3. 目标对象配置

```json
{
  "type": "single",
  "user_field": "inviter_id"
}
```

或者多用户：

```json
{
  "type": "multiple",
  "users_field": "inviter_id,invitee_id"
}
```

### 4. 奖励计算配置

#### 固定金额

```json
{
  "type": "fixed",
  "value": 100.0
}
```

#### 百分比奖励

```json
{
  "type": "percentage",
  "base_field": "amount",
  "percentage": 10.0
}
```

#### 阶梯奖励

```json
{
  "type": "tiered",
  "count_field": "invitation_count",
  "tiers": [
    {"min_count": 1, "reward": 50.0},
    {"min_count": 5, "reward": 100.0},
    {"min_count": 10, "reward": 200.0}
  ]
}
```

### 5. 限制条件配置

```json
{
  "constraints": [
    {
      "type": "user_limit",
      "max_rewards": 10
    },
    {
      "type": "daily_limit",
      "max_daily": 5
    },
    {
      "type": "total_limit",
      "max_total": 1000
    }
  ]
}
```

### 6. 分发配置

```json
{
  "channels": ["wallet", "points"],
  "priority": "wallet"
}
```

## 使用示例

### 1. 创建邀请奖励策略

```python
# 创建阶梯邀请奖励策略
strategy_data = {
    "name": "邀请阶梯奖励",
    "description": "根据邀请数量给予阶梯奖励",
    "strategy_type": "tiered",
    "trigger_config": json.dumps({
        "type": "event",
        "events": ["invitation.completed"]
    }),
    "target_config": json.dumps({
        "type": "single",
        "user_field": "inviter_id"
    }),
    "calculation_config": json.dumps({
        "type": "tiered",
        "count_field": "invitation_count",
        "tiers": [
            {"min_count": 1, "reward": 50.0},
            {"min_count": 5, "reward": 100.0},
            {"min_count": 10, "reward": 200.0}
        ]
    }),
    "constraint_config": json.dumps({
        "constraints": [
            {"type": "user_limit", "max_rewards": 10}
        ]
    }),
    "distribution_config": json.dumps({
        "channels": ["wallet"],
        "priority": "wallet"
    }),
    "priority": 1
}

result = await reward_strategy_service.create_strategy(**strategy_data)
```

### 2. 处理奖励事件

```python
# 处理邀请完成事件
context = {
    "campaign_id": 1,
    "inviter_id": 123,
    "invitee_id": 456,
    "invitation_count": 5,
    "amount": 1000.0,
    "events": ["invitation.completed"]
}

result = await reward_record_service.process_rewards(
    trigger_event="invitation.completed",
    context=context
)
```

### 3. 查询奖励记录

```python
# 获取用户的奖励记录
result = await reward_record_service.get_user_rewards(
    user_id=123,
    campaign_id=1,
    status="issued"
)

# 获取策略的奖励记录
result = await reward_record_service.get_rewards_by_trigger_event(
    trigger_event="invitation.completed",
    start_time=datetime.now() - timedelta(days=7),
    end_time=datetime.now()
)
```

## API 接口

### 管理端接口

#### 创建奖励策略

```
POST /admin/strategies/
```

#### 更新策略优先级

```
PUT /admin/strategies/{strategy_id}/priority/
```

#### 分发奖励

```
POST /admin/records/distribute/
```

#### 更新奖励状态

```
PUT /admin/records/{record_id}/status/
```

### 客户端接口

#### 获取我的奖励

```
GET /rewards/mine
```

## 事件处理

系统支持以下事件类型：

-   `invitation.completed`: 邀请完成
-   `registration.completed`: 注册完成
-   `purchase.completed`: 购买完成
-   `login.daily`: 每日登录

## 分发渠道

支持的分发渠道：

-   `wallet`: 钱包
-   `points`: 积分
-   `coupon`: 优惠券
-   `gift`: 实物礼品

## 最佳实践

1. **策略设计**: 根据业务需求设计合适的奖励策略
2. **限制设置**: 合理设置限制条件，避免奖励滥用
3. **监控告警**: 监控奖励发放情况，及时发现问题
4. **数据分析**: 分析奖励效果，优化策略配置
5. **测试验证**: 在测试环境验证策略配置的正确性
