# 软删除功能实现总结

## 概述

根据用户要求，为项目中包含状态字段的删除 API 添加软删除选项，默认软删除为 true。

## 已完成的修改

### 1. 基础配置修改

-   ✅ 修改 `svc/core/schemas/batch.py` 中的 `BatchDeleteRequest`，将 `soft_delete` 默认值从
    `False` 改为 `True`

### 2. 已完成的模块

#### Shop 模块 (svc/apps/shops/)

-   ✅ 添加 `DeleteShopParams` schema 类
-   ✅ 修改 `ShopService.delete_shop()` 方法，支持 `soft_delete` 参数
-   ✅ 修改 `shop.py` 路由，添加 `soft_delete` 查询参数
-   ✅ 软删除实现：更新 `status` 字段为 `DELETED`

#### Product 模块 (svc/apps/products/)

-   ✅ 添加 `DeleteProductParams` schema 类
-   ✅ 修改 `ProductService.delete_product()` 方法，支持 `soft_delete` 参数
-   ✅ 修改 `product.py` 路由，添加 `soft_delete` 查询参数
-   ✅ 软删除实现：更新 `status` 字段为 `DELETED`

#### Album 模块 (svc/apps/albums/)

-   ✅ 添加 `DeleteAlbumParams` schema 类
-   ✅ 修改 `AlbumService.delete_album()` 方法，支持 `soft_delete` 参数
-   ✅ 修改 `album.py` 路由，添加 `soft_delete` 查询参数
-   ✅ 软删除实现：更新 `status` 字段为 `deleted`

#### Campaign 模块 (svc/apps/marketing/)

-   ✅ 添加 `DeleteCampaignParams` schema 类
-   ✅ 修改 `CampaignService.delete_campaign()` 方法，支持 `soft_delete` 参数
-   ✅ 修改 `campaign.py` 路由，添加 `soft_delete` 查询参数
-   ✅ 软删除实现：更新 `status` 字段为 `deleted`

#### User 模块 (svc/apps/auth/)

-   ✅ 修改 `DeleteUserParams` schema 类，添加 `soft_delete` 字段
-   ✅ 修改 `UserService.delete_user()` 方法，支持 `soft_delete` 参数
-   ✅ 修改 `users.py` 路由，添加 `soft_delete` 查询参数
-   ✅ 软删除实现：更新 `is_active` 字段为 `False`

#### Category 模块 (svc/apps/products/)

-   ✅ 添加 `DeleteCategoryParams` schema 类

## 还需要继续的模块

### 高优先级模块（有状态字段）

1. **SKU 模块** (svc/apps/products/sku/)

    - 需要添加 `DeleteSKUParams` schema 类
    - 修改 `SKUService.delete_sku()` 方法
    - 修改路由添加 `soft_delete` 参数
    - 软删除实现：更新 `status` 字段

2. **Spec 模块** (svc/apps/products/spec/)

    - 需要添加 `DeleteSpecParams` 和 `DeleteSpecOptionParams` schema 类
    - 修改 `SpecService.delete_spec()` 和 `delete_spec_option()` 方法
    - 修改路由添加 `soft_delete` 参数
    - 软删除实现：更新 `status` 字段

3. **Inventory 模块** (svc/apps/products/inventory/)
    - 需要添加 `DeleteInventoryParams` schema 类
    - 修改 `InventoryService.delete_inventory()` 方法
    - 修改路由添加 `soft_delete` 参数
    - 软删除实现：更新 `status` 字段

### 中优先级模块（有状态字段）

4. **Billing 相关模块** (svc/apps/billing/)

    - Invoice 模块
    - Payment 模块
    - Subscription 模块
    - SubscriptionPlan 模块

5. **Role 模块** (svc/apps/auth/)
    - 需要检查 Role 模型是否有状态字段
    - 如果有，需要实现软删除

### 低优先级模块

6. **AuditLog 模块** (svc/apps/system/)
    - 通常使用硬删除，但可以添加软删除选项

## 实现模式

### Schema 层

```python
class DeleteXxxParams(BaseModel):
    """删除XXX参数"""
    xxx_id: int = Field(..., description="XXX ID")
    soft_delete: bool = Field(default=True, description="是否软删除")
```

### Service 层

```python
async def delete_xxx(self, xxx_id: int, soft_delete: bool = True) -> Result[Dict[str, Any]]:
    """删除XXX"""
    if soft_delete:
        # 软删除：更新状态字段
        update_data = {"status": "deleted"}
        await self.xxx_repo.update(xxx, update_data)
    else:
        # 硬删除：直接从数据库删除
        await self.xxx_repo.delete(xxx)
```

### Router 层

```python
@router.delete("/admin/{xxx_id}")
async def admin_delete_xxx(
    xxx_id: int = Path(...),
    soft_delete: bool = Query(True, description="是否软删除", alias="softDelete"),
    # ... 其他参数
):
    result = await xxx_service.delete_xxx(xxx_id=xxx_id, soft_delete=soft_delete)
    return result
```

## 注意事项

1. **向后兼容性**：所有修改都保持了向后兼容性，默认使用软删除
2. **状态字段映射**：不同模块使用不同的状态字段进行软删除
3. **事件触发**：删除事件中包含了 `soft_delete` 信息
4. **日志记录**：所有删除操作都记录了 `soft_delete` 参数
5. **缓存清理**：删除操作后正确清理了相关缓存

## 测试建议

1. 测试软删除功能（默认行为）
2. 测试硬删除功能（设置 `soft_delete=false`）
3. 测试批量删除功能
4. 验证删除后的数据状态
5. 验证缓存清理
6. 验证事件触发
