# SQL 性能优化指南

## 📊 性能对比结果

### 原始版本 vs 优化版本

| 指标         | 原始版本 | 优化版本   | 提升幅度     |
| ------------ | -------- | ---------- | ------------ |
| SQL 语句数   | 289 条   | 62 条      | **78.5%**    |
| 文件大小     | ~15KB    | ~8KB       | **47%**      |
| 预计执行时间 | 基准     | 2-5 倍更快 | **200-500%** |

## 🚀 主要优化策略

### 1. **批量 INSERT 优化**

#### 原始方式（逐行 INSERT）

```sql
-- 289条单独的INSERT语句
INSERT INTO permissions (id, name, description, created_at, updated_at) VALUES (1, '*:*', '所有权限（超级管理员）', '2025-08-08T19:30:45.910353', '2025-08-08T19:30:45.910353');
INSERT INTO permissions (id, name, description, created_at, updated_at) VALUES (2, 'user:read', '查看用户信息', '2025-08-08T19:30:45.910353', '2025-08-08T19:30:45.910353');
-- ... 更多单行INSERT
```

#### 优化方式（批量 INSERT）

```sql
-- 1条批量INSERT语句，包含78条记录
INSERT INTO permissions (id, name, description, created_at, updated_at) VALUES
(1, '*:*', '所有权限（超级管理员）', '2025-08-08T19:40:10.160013', '2025-08-08T19:40:10.160013'),
(2, 'user:read', '查看用户信息', '2025-08-08T19:40:10.160013', '2025-08-08T19:40:10.160013'),
-- ... 更多记录
(78, 'system:monitor', '系统监控访问权限', '2025-08-08T19:40:10.160013', '2025-08-08T19:40:10.160013');
```

**性能提升**：减少网络往返，降低事务开销，预计提升 **2-3 倍**

### 2. **序列重置优化**

#### 原始方式（ALTER SEQUENCE）

```sql
-- 较慢的序列重置
ALTER SEQUENCE permissions_id_seq RESTART WITH 1;
ALTER SEQUENCE roles_id_seq RESTART WITH 1;
-- ... 更多序列
```

#### 优化方式（setval 函数）

```sql
-- 更快的序列重置
SELECT setval('permissions_id_seq', 1, false);
SELECT setval('roles_id_seq', 1, false);
-- ... 更多序列
```

**性能提升**：`setval` 比 `ALTER SEQUENCE` 快约 **30-50%**

### 3. **数据清空优化**

#### 原始方式（DELETE）

```sql
-- 逐表DELETE，较慢
DELETE FROM reward_records;
DELETE FROM invitations;
-- ... 更多表
```

#### 优化方式（TRUNCATE CASCADE）

```sql
-- 批量TRUNCATE，更快
TRUNCATE TABLE reward_records CASCADE;
TRUNCATE TABLE invitations CASCADE;
-- ... 更多表
```

**性能提升**：`TRUNCATE` 比 `DELETE` 快约 **5-10 倍**

### 4. **外键约束优化**

#### 优化方式（临时禁用约束）

```sql
-- 临时禁用外键约束
SET session_replication_role = replica;

-- 执行数据操作
-- ...

-- 重新启用外键约束
SET session_replication_role = DEFAULT;
```

**性能提升**：避免外键检查开销，预计提升 **1.5-2 倍**

## 📈 性能测试建议

### 1. **基准测试**

```bash
# 测试原始版本
time psql -h localhost -U username -d database_name -f seed_data.sql

# 测试优化版本
time psql -h localhost -U username -d database_name -f seed_data_optimized.sql
```

### 2. **性能监控**

```sql
-- 监控执行时间
\timing on

-- 查看执行计划
EXPLAIN ANALYZE INSERT INTO permissions (...) VALUES (...);
```

### 3. **批量大小调优**

```bash
# 测试不同批量大小
python scripts/generate_sql_seed_optimized.py --batch-size 50
python scripts/generate_sql_seed_optimized.py --batch-size 100
python scripts/generate_sql_seed_optimized.py --batch-size 200
```

## 🔧 进一步优化建议

### 1. **COPY 命令优化（PostgreSQL 特有）**

```sql
-- 最高性能的数据导入方式
COPY permissions (id, name, description, created_at, updated_at) FROM STDIN;
1	*:*	所有权限（超级管理员）	2025-08-08T19:40:10.160013	2025-08-08T19:40:10.160013
2	user:read	查看用户信息	2025-08-08T19:40:10.160013	2025-08-08T19:40:10.160013
\.
```

**性能提升**：比批量 INSERT 快约 **3-5 倍**

### 2. **索引优化**

```sql
-- 在插入前临时禁用索引
DROP INDEX IF EXISTS idx_permissions_name;
-- 插入数据
-- 重新创建索引
CREATE INDEX idx_permissions_name ON permissions(name);
```

### 3. **并行处理**

```sql
-- 启用并行查询
SET max_parallel_workers_per_gather = 4;
SET max_parallel_workers = 8;
```

### 4. **内存优化**

```sql
-- 调整工作内存
SET work_mem = '256MB';
SET maintenance_work_mem = '512MB';
```

## 📋 使用建议

### 1. **选择优化级别**

| 场景     | 推荐优化级别 | 说明                   |
| -------- | ------------ | ---------------------- |
| 开发环境 | 基础优化     | 批量 INSERT + 序列优化 |
| 测试环境 | 中等优化     | + 外键约束优化         |
| 生产环境 | 高级优化     | + COPY 命令 + 索引优化 |

### 2. **批量大小选择**

| 数据量        | 推荐批量大小 | 原因              |
| ------------- | ------------ | ----------------- |
| < 1000 条     | 50-100       | 避免单条 SQL 过长 |
| 1000-10000 条 | 100-200      | 平衡性能和内存    |
| > 10000 条    | 200-500      | 最大化性能        |

### 3. **环境适配**

```bash
# 开发环境：快速生成
python scripts/generate_sql_seed_optimized.py --batch-size 50

# 生产环境：最高性能
python scripts/generate_sql_seed_optimized.py --batch-size 200 --use-copy
```

## ⚠️ 注意事项

### 1. **兼容性考虑**

-   `TRUNCATE CASCADE` 需要 PostgreSQL 8.2+
-   `setval` 函数是 PostgreSQL 特有
-   `COPY` 命令仅支持 PostgreSQL

### 2. **数据一致性**

-   使用事务包装确保原子性
-   外键约束优化可能影响数据完整性检查
-   建议在测试环境验证后再用于生产

### 3. **内存使用**

-   大批量 INSERT 可能增加内存使用
-   监控数据库内存使用情况
-   根据服务器配置调整批量大小

## 🎯 最佳实践

1. **渐进式优化**：从基础优化开始，逐步应用高级优化
2. **性能测试**：在生产环境使用前进行充分测试
3. **监控反馈**：持续监控执行时间和资源使用
4. **文档记录**：记录优化效果和配置参数
5. **版本控制**：将优化后的 SQL 文件纳入版本控制

## 📊 预期性能提升

| 优化策略    | 性能提升   | 适用场景            |
| ----------- | ---------- | ------------------- |
| 批量 INSERT | 2-3 倍     | 所有场景            |
| 序列优化    | 1.3-1.5 倍 | PostgreSQL          |
| 外键优化    | 1.5-2 倍   | 大量数据            |
| COPY 命令   | 3-5 倍     | PostgreSQL 大量数据 |
| 索引优化    | 1.5-2 倍   | 有索引的表          |

**综合优化效果**：预计总体性能提升 **5-10 倍**
