# SQL 种子数据生成指南

## 📋 概述

本项目提供了一个 SQL 生成脚本，可以根据种子数据脚本自动生成可直接在数据库中执行的 SQL 语句。
这个功能特别适用于：

-   生产环境部署
-   数据库迁移
-   离线数据导入
-   数据库备份和恢复

## 🛠️ 功能特性

### 1. **完整的种子数据覆盖**

-   ✅ 权限数据（78 个权限）
-   ✅ 角色数据（3 个角色）
-   ✅ 用户数据（3 个用户）
-   ✅ 订阅计划数据（3 个计划）
-   ✅ 营销活动数据（3 个活动）
-   ✅ 奖励策略数据（5 个策略）
-   ✅ 分类数据（2 个分类）
-   ✅ 规格和规格值数据
-   ✅ Banner 轮播图数据（5 个 Banner）

### 2. **智能依赖处理**

-   自动处理外键约束关系
-   按正确的顺序生成 INSERT 语句
-   自动管理 ID 映射关系

### 3. **数据安全**

-   自动转义特殊字符
-   正确处理 JSON 数据
-   安全的密码哈希处理

## 🚀 使用方法

### 基本使用

```bash
# 生成完整的种子数据SQL
python scripts/generate_sql_seed.py

# 指定输出文件名
python scripts/generate_sql_seed.py --output my_seed_data.sql

# 只生成清空数据的SQL
python scripts/generate_sql_seed.py --clear-only
```

### 参数说明

| 参数             | 说明                 | 默认值          |
| ---------------- | -------------------- | --------------- |
| `--output`, `-o` | 输出文件名           | `seed_data.sql` |
| `--clear-only`   | 只生成清空数据的 SQL | `False`         |

## 📁 生成的文件

### 1. **seed_data.sql**（完整种子数据）

包含完整的数据库初始化 SQL，包括：

```sql
-- 清空现有数据
DELETE FROM reward_records;
DELETE FROM invitations;
-- ... 其他表

-- 重置序列
ALTER SEQUENCE permissions_id_seq RESTART WITH 1;
-- ... 其他序列

-- 插入权限数据
INSERT INTO permissions (id, name, description, created_at, updated_at) VALUES (1, '*:*', '所有权限（超级管理员）', '2025-08-08T19:30:45.910353', '2025-08-08T19:30:45.910353');
-- ... 其他权限

-- 插入角色数据
INSERT INTO roles (id, name, description, is_system, created_at, updated_at) VALUES (1, 'admin', '系统管理员', true, '2025-08-08T19:30:45.910353', '2025-08-08T19:30:45.910353');
-- ... 其他角色

-- 插入用户数据
INSERT INTO users (id, username, email, password, full_name, is_active, is_superuser, created_at, updated_at) VALUES (1, 'super', '<EMAIL>', '$2b$12$...', '系统管理员', true, true, '2025-08-08T19:30:45.910353', '2025-08-08T19:30:45.910353');
-- ... 其他用户

-- 更多数据...
```

### 2. **clear_data.sql**（仅清空数据）

只包含清空数据和重置序列的 SQL：

```sql
-- 清空现有数据
DELETE FROM reward_records;
DELETE FROM invitations;
-- ... 其他表

-- 重置序列
ALTER SEQUENCE permissions_id_seq RESTART WITH 1;
-- ... 其他序列
```

## 🔧 在数据库中使用

### PostgreSQL

```bash
# 连接到数据库
psql -h localhost -U username -d database_name

# 执行SQL文件
\i seed_data.sql

# 或者在命令行中执行
psql -h localhost -U username -d database_name -f seed_data.sql
```

### Docker 环境

```bash
# 在Docker容器中执行
docker-compose exec db psql -U funny -d aitools -f /path/to/seed_data.sql

# 或者复制文件到容器后执行
docker cp seed_data.sql container_name:/tmp/
docker-compose exec db psql -U funny -d aitools -f /tmp/seed_data.sql
```

## 📊 生成的数据统计

| 数据类型 | 数量  | 说明                     |
| -------- | ----- | ------------------------ |
| 权限     | 78 个 | 涵盖所有模块的权限       |
| 角色     | 3 个  | admin、user、vip         |
| 用户     | 3 个  | 包含管理员和测试用户     |
| 订阅计划 | 3 个  | 基础版、专业版、企业版   |
| 营销活动 | 3 个  | 邀请、购买奖励、VIP 专享 |
| 奖励策略 | 5 个  | 不同类型的奖励策略       |
| 分类     | 2 个  | 安全帽、反光衣           |
| 规格     | 5 个  | 颜色、尺寸、材质等       |
| 规格值   | 13 个 | 具体的规格选项           |
| Banner   | 5 个  | 轮播图数据               |

## ⚠️ 注意事项

### 1. **执行前备份**

```sql
-- 建议在执行前备份数据库
pg_dump -h localhost -U username -d database_name > backup.sql
```

### 2. **事务处理**

生成的 SQL 文件使用事务包装，确保数据一致性：

```sql
BEGIN;
-- 所有SQL语句
COMMIT;
```

### 3. **外键约束**

脚本会自动处理外键依赖关系，按正确顺序插入数据。

### 4. **密码安全**

用户密码会自动进行哈希处理，确保安全性。

## 🔄 更新种子数据

当种子数据发生变化时，重新生成 SQL：

```bash
# 重新生成SQL文件
python scripts/generate_sql_seed.py

# 检查生成的内容
head -20 seed_data.sql
```

## 🐛 故障排除

### 1. **权限错误**

```bash
# 确保有足够的权限
sudo -u postgres psql -d database_name -f seed_data.sql
```

### 2. **编码问题**

```bash
# 确保使用UTF-8编码
export PGCLIENTENCODING=UTF8
psql -f seed_data.sql
```

### 3. **序列问题**

如果遇到序列冲突，可以手动重置：

```sql
-- 重置所有序列
SELECT setval(pg_get_serial_sequence('permissions', 'id'), 1, false);
SELECT setval(pg_get_serial_sequence('roles', 'id'), 1, false);
-- ... 其他序列
```

## 📝 自定义扩展

### 添加新的数据类型

1. 在 `DataProvider` 中添加新的数据方法
2. 在 `SQLGenerator` 中添加对应的生成方法
3. 在 `generate_sql_file` 中调用新方法

### 修改数据内容

直接修改 `scripts/services/data_provider.py` 中的相应方法，然后重新生成 SQL。

## 🎯 最佳实践

1. **版本控制**：将生成的 SQL 文件纳入版本控制
2. **环境分离**：为不同环境生成不同的 SQL 文件
3. **测试验证**：在测试环境中先验证 SQL 文件
4. **备份策略**：执行前务必备份数据库
5. **监控执行**：监控 SQL 执行过程和结果
