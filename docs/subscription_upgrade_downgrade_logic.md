# 订阅升降级逻辑梳理

## 概述

本文档梳理了订阅升降级的完整业务逻辑，包括如何同步订单和订阅状态。

## 核心流程

### 1. 订阅升降级触发流程

```
用户请求 → 验证权限 → 业务规则验证 → 自动判断升降级 → 执行升降级 → 生成订单/退款 → 触发事件 → 状态同步
```

### 2. 自动判断升降级逻辑

```python
# 基于价格自动判断
if new_plan.price > current_plan.price:
    # 升级：立即生效，生成差价账单
    return await self._execute_upgrade(params, subscription, current_plan, new_plan)
elif new_plan.price < current_plan.price:
    # 降级：可选择立即生效或周期结束时生效，处理退款
    return await self._execute_downgrade(params, subscription, current_plan, new_plan)
else:
    # 价格相同，无需变更
    return error("价格相同，无需变更")
```

## 升级逻辑详解

### 1. 升级执行流程

```python
async def _execute_upgrade(self, params, subscription, current_plan, new_plan):
    # 1. 记录原始状态
    old_plan_id = subscription.plan_id
    old_period_end = subscription.current_period_end
    old_status = subscription.status

    # 2. 更新订阅状态为pending，等待差价账单支付
    update_data = {
        "plan_id": params.plan_id,
        "status": "pending",  # 升级后先设置为pending，等待差价账单支付
        "current_period_start": now,
        "current_period_end": now + new_plan_period,
        "trial_start": None,  # 清除试用期
        "trial_end": None
    }

    # 3. 更新订阅
    subscription = await self.subscription_repo.update(subscription, update_data)

    # 4. 生成升级差价账单
    invoice = await self._generate_upgrade_invoice(subscription, current_plan, new_plan, now)

    # 5. 将账单ID添加到事件数据中
    if invoice:
        event_data["invoice_id"] = invoice.id

    # 6. 触发升级待支付事件
    dispatch(BILLING_SUBSCRIPTION_UPGRADED, payload=event_data)
```

### 2. 升级差价账单生成

```python
async def _generate_upgrade_invoice(self, subscription, current_plan, new_plan, effective_date):
    # 1. 计算差价
    price_difference = new_plan.price - current_plan.price

    # 2. 计算按比例收费金额（基于剩余时间）
    prorated_amount = await self._calculate_prorated_amount(
        subscription, current_plan, new_plan, effective_date
    )

    # 3. 创建差价账单
    invoice_data = {
        "subscription_id": subscription.id,
        "amount": prorated_amount,
        "currency": new_plan.currency,
        "status": "pending",
        "description": f"订阅升级差价账单：{current_plan.name} → {new_plan.name}",
        "due_date": effective_date,  # 升级时立即收费
        "meta_data": {
            "billing_type": "upgrade_difference",
            "price_difference": price_difference,
            "prorated_amount": prorated_amount,
            "upgrade_type": "immediate"
        }
    }

    # 4. 创建账单并触发事件
    invoice = await self.invoice_repo.create(invoice_data)
    dispatch(BILLING_INVOICE_CREATED, payload=event_data)
```

### 3. 升级事件处理

```python
@local_handler.register(event_name=BILLING_SUBSCRIPTION_UPGRADED)
async def handle_subscription_upgraded(event: Event):
    # 1. 记录审计日志
    dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={...})

    # 2. 更新统计
    dispatch(SYSTEM_STATS_UPDATE_REQUESTED, payload={
        "entity_type": "subscription",
        "metric_type": "upgrades_count",
        "increment_value": 1
    })

    # 3. 根据订阅状态发送不同通知
    subscription = await subscription_service.get_resource_by_id(subscription_id)
    if subscription and subscription.status == "pending":
        # 待支付状态，发送待支付通知
        notification_title = "订阅升级待支付"
        message = f"您的订阅升级待支付：{old_plan_name} → {new_plan_name}，差价：{price_difference}。请及时支付差价账单以激活新功能。"
    else:
        # 已激活状态，发送升级成功通知
        notification_title = "订阅升级成功"
        message = f"您的订阅已成功升级：{old_plan_name} → {new_plan_name}"

    dispatch(SYSTEM_NOTIFICATION_SEND_REQUESTED, payload={
        "recipient_user_id": user_id,
        "title": notification_title,
        "message": message
    })

    # 4. 清理缓存
    dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={
        'resource_type': 'subscription',
        'resource_id': subscription_id
    })
```

### 4. 多个升级订单处理

```python
async def _handle_pending_subscription_payment(self, subscription, paid_invoice_id: int):
    """处理pending订阅的支付（处理升级订单支付）"""
    # 1. 获取已支付的账单
    paid_invoice = await self.invoice_repo.get_by_id(paid_invoice_id)

    # 2. 检查是否是升级差价账单
    if paid_invoice.meta_data.get("billing_type") == "upgrade_difference":
        # 3. 获取升级的目标计划ID
        new_plan_id = paid_invoice.meta_data.get("new_plan_id")

        # 4. 取消其他pending的升级订单
        await self._cancel_other_pending_upgrade_orders(subscription.id, paid_invoice_id)

        # 5. 激活订阅到目标计划
        update_data = {
            "status": "active",
            "plan_id": new_plan_id,
            "current_period_start": now,
            "current_period_end": now + new_plan_period
        }
        await self._update_subscription_status(subscription, update_data)

        # 6. 触发升级完成事件
        dispatch(BILLING_SUBSCRIPTION_UPGRADED, payload=event_data)
```

### 5. 防止重复升级

```python
async def _check_pending_upgrade_orders(self, subscription_id: int, new_plan_id: int):
    """检查是否有其他pending的升级订单"""
    # 1. 获取所有pending升级账单
    pending_upgrade_invoices = [
        inv for inv in invoices
        if inv.status == "pending"
        and inv.meta_data.get("billing_type") == "upgrade_difference"
    ]

    # 2. 检查相同目标计划
    same_plan_pending = [
        inv for inv in pending_upgrade_invoices
        if inv.meta_data.get("new_plan_id") == new_plan_id
    ]
    if same_plan_pending:
        return False, "已有相同目标计划的升级订单待支付"

    # 3. 检查不同目标计划
    different_plan_pending = [
        inv for inv in pending_upgrade_invoices
        if inv.meta_data.get("new_plan_id") != new_plan_id
    ]
    if different_plan_pending:
        return False, "已有其他目标计划的升级订单待支付"

    return True, "pending订单检查通过"
```

## 降级逻辑详解

### 1. 降级执行流程

```python
async def _execute_downgrade(self, params, subscription, current_plan, new_plan):
    # 1. 确定降级生效时间
    if params.effective_date:
        effective_date = parse_iso_date(params.effective_date)
    else:
        effective_date = subscription.current_period_end  # 默认周期结束时降级

    # 2. 更新订阅状态
    update_data = {
        "plan_id": params.plan_id,
        "status": "active",  # 降级后保持活跃状态
        "current_period_start": effective_date,
        "current_period_end": effective_date + new_plan_period
    }

    # 3. 处理试用期（如果降级到基础计划）
    if new_plan.tier == "basic" and new_plan.trial_period_days:
        update_data.update({
            "trial_start": effective_date,
            "trial_end": effective_date + timedelta(days=new_plan.trial_period_days),
            "status": "trialing"
        })

    # 4. 更新订阅
    subscription = await self.subscription_repo.update(subscription, update_data)

    # 5. 处理降级退款
    await self._handle_downgrade_refund(subscription, current_plan, new_plan, effective_date)

    # 6. 触发降级事件
    dispatch(BILLING_SUBSCRIPTION_DOWNGRADED, payload=event_data)
```

### 2. 降级退款处理

```python
async def _handle_downgrade_refund(self, subscription, current_plan, new_plan, effective_date):
    # 1. 计算差价
    price_difference = current_plan.price - new_plan.price

    # 2. 计算按比例退款金额
    prorated_refund = await self._calculate_prorated_amount(
        subscription, current_plan, new_plan, effective_date
    )

    # 3. 查找可退款的账单
    refundable_invoices = await self._find_refundable_invoices(subscription.id)

    # 4. 创建退款记录
    refund_data = {
        "subscription_id": subscription.id,
        "amount": prorated_refund,
        "currency": current_plan.currency,
        "status": "pending",
        "description": f"订阅降级退款：{current_plan.name} → {new_plan.name}",
        "meta_data": {
            "billing_type": "downgrade_refund",
            "price_difference": price_difference,
            "prorated_refund": prorated_refund,
            "effective_date": effective_date.isoformat(),
            "refundable_invoices": [inv.id for inv in refundable_invoices]
        }
    }

    # 5. 触发退款事件
    dispatch(BILLING_REFUND_REQUESTED, payload=event_data)
```

### 3. 降级事件处理

```python
@local_handler.register(event_name=BILLING_SUBSCRIPTION_DOWNGRADED)
async def handle_subscription_downgraded(event: Event):
    # 1. 记录审计日志
    dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={...})

    # 2. 更新统计
    dispatch(SYSTEM_STATS_UPDATE_REQUESTED, payload={
        "entity_type": "subscription",
        "metric_type": "downgrades_count",
        "increment_value": 1
    })

    # 3. 发送降级通知
    dispatch(SYSTEM_NOTIFICATION_SEND_REQUESTED, payload={
        "recipient_user_id": user_id,
        "title": "订阅降级成功",
        "message": f"您的订阅已成功降级：{old_plan_name} → {new_plan_name}"
    })

    # 4. 清理缓存
    dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={
        'resource_type': 'subscription',
        'resource_id': subscription_id
    })
```

## 按比例计算逻辑

### 1. 按比例金额计算

```python
async def _calculate_prorated_amount(self, subscription, current_plan, new_plan, effective_date):
    # 1. 计算当前周期的总天数
    period_start = subscription.current_period_start
    period_end = subscription.current_period_end
    total_days = (period_end - period_start).days

    # 2. 计算剩余天数
    if effective_date:
        remaining_days = (period_end - effective_date).days
    else:
        remaining_days = (period_end - get_utc_now_without_tzinfo()).days

    # 3. 计算按比例金额
    if total_days > 0:
        prorated_ratio = remaining_days / total_days
        price_difference = abs(new_plan.price - current_plan.price)
        prorated_amount = price_difference * prorated_ratio
    else:
        prorated_amount = 0

    return round(prorated_amount, 2)
```

### 2. 可退款账单查找

```python
async def _find_refundable_invoices(self, subscription_id):
    # 查找已支付且未退款的账单
    refundable_invoices = await self.invoice_repo.get_by_subscription(
        subscription_id,
        filters={
            "status": "paid",
            "is_paid": True,
            "refunded": False  # 假设有退款标记字段
        }
    )
    return refundable_invoices
```

## 订单和订阅状态同步

### 1. 升级时的状态同步

| 组件     | 升级前            | 升级后（待支付）  | 支付成功后        | 同步机制   |
| -------- | ----------------- | ----------------- | ----------------- | ---------- |
| 订阅状态 | active            | pending           | active            | 分阶段更新 |
| 订阅计划 | old_plan          | new_plan          | new_plan          | 立即更新   |
| 订阅周期 | old_period        | new_period        | new_period        | 立即更新   |
| 差价账单 | 无                | pending           | paid              | 立即生成   |
| 用户权限 | old_plan_features | old_plan_features | new_plan_features | 支付后生效 |

### 2. 降级时的状态同步

| 组件     | 降级前            | 降级后            | 同步机制       |
| -------- | ----------------- | ----------------- | -------------- |
| 订阅状态 | active            | active/trialing   | 按生效时间更新 |
| 订阅计划 | old_plan          | new_plan          | 按生效时间更新 |
| 订阅周期 | old_period        | new_period        | 按生效时间更新 |
| 退款记录 | 无                | pending           | 立即生成       |
| 用户权限 | old_plan_features | new_plan_features | 按生效时间更新 |

### 3. 状态同步事件链

```
订阅升降级 → 更新订阅状态 → 生成订单/退款 → 触发事件 → 事件处理器 → 状态同步
```

#### 升级事件链：

**升级待支付阶段**：

1. `BILLING_SUBSCRIPTION_UPGRADED` → 订阅升级待支付事件
2. `BILLING_INVOICE_CREATED` → 差价账单创建事件
3. `SYSTEM_AUDIT_LOG_RECORDED` → 审计日志记录
4. `SYSTEM_NOTIFICATION_SEND_REQUESTED` → 待支付通知发送
5. `SYSTEM_CACHE_INVALIDATION_REQUESTED` → 缓存清理

**支付成功后**：

1. `BILLING_INVOICE_PAID` → 账单支付成功事件
2. `BILLING_SUBSCRIPTION_STATUS_UPDATED` → 订阅状态更新事件
3. `BILLING_INVOICE_CANCELED` → 其他 pending 账单取消事件（如果有）
4. `SYSTEM_AUDIT_LOG_RECORDED` → 审计日志记录
5. `SYSTEM_STATS_UPDATE_REQUESTED` → 统计更新
6. `SYSTEM_NOTIFICATION_SEND_REQUESTED` → 升级成功通知发送
7. `SYSTEM_CACHE_INVALIDATION_REQUESTED` → 缓存清理

#### 降级事件链：

1. `BILLING_SUBSCRIPTION_DOWNGRADED` → 订阅降级事件
2. `BILLING_REFUND_REQUESTED` → 退款请求事件
3. `SYSTEM_AUDIT_LOG_RECORDED` → 审计日志记录
4. `SYSTEM_STATS_UPDATE_REQUESTED` → 统计更新
5. `SYSTEM_NOTIFICATION_SEND_REQUESTED` → 通知发送
6. `SYSTEM_CACHE_INVALIDATION_REQUESTED` → 缓存清理

## 业务规则验证

### 1. 升降级权限验证

```python
async def _validate_subscription_rules(self, subscription, operation, **kwargs):
    # 1. 基础规则验证
    if not await self._validate_basic_rules(subscription, operation):
        return False, "基础规则验证失败"

    # 2. 计划变更规则验证
    if operation == "change_plan":
        return await self._validate_plan_change_rules(subscription, **kwargs)

    return True, "验证通过"
```

### 2. 计划变更规则

```python
async def _validate_plan_change_rules(self, subscription, new_plan_id, effective_date):
    # 1. 检查订阅状态
    if subscription.status not in ["active", "trialing"]:
        return False, "只有活跃或试用期的订阅可以变更计划"

    # 2. 检查变更频率限制
    if not await self._check_change_frequency(subscription):
        return False, "变更频率过高，请稍后再试"

    # 3. 检查新计划有效性
    if not await self._validate_new_plan(new_plan_id):
        return False, "新计划无效或不可用"

    return True, "验证通过"
```

## 缓存管理

### 1. 缓存清理策略

```python
# 升级/降级后清理相关缓存
await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
await self._clear_active_subscription_cache(subscription.user_id)
```

### 2. 缓存键设计

```python
# 订阅缓存键
f"subscription:v1:{subscription_id}"

# 用户活跃订阅缓存键
f"user_active_subscription:v1:{user_id}"

# 订阅计划缓存键
f"subscription_plan:v1:{plan_id}"
```

## 错误处理

### 1. 常见错误场景

| 错误场景     | 错误代码               | 处理方式      |
| ------------ | ---------------------- | ------------- |
| 订阅不存在   | SUBSCRIPTION_NOT_FOUND | 返回 404 错误 |
| 权限不足     | PERMISSION_DENIED      | 返回 403 错误 |
| 计划不存在   | PLAN_NOT_FOUND         | 返回 404 错误 |
| 状态不允许   | OPERATION_FAILED       | 返回 400 错误 |
| 变更频率过高 | OPERATION_FAILED       | 返回 429 错误 |

### 2. 异常恢复机制

```python
try:
    # 执行升降级操作
    result = await self._execute_upgrade(params, subscription, current_plan, new_plan)
    return result
except Exception as e:
    # 记录错误日志
    self.logger.error(f"升降级操作失败: {str(e)}", exc_info=True)

    # 返回错误结果
    return self.create_error_result(
        error_code=ErrorCode.OPERATION_FAILED,
        error_message=f"升降级操作失败: {str(e)}"
    )
```

## 总结

订阅升降级逻辑的核心特点：

1. **自动判断**：基于价格自动判断升级或降级
2. **分阶段生效**：升级先待支付后生效，降级可选择立即或周期结束时生效
3. **按比例计费**：升级生成差价账单，降级处理按比例退款
4. **状态一致性**：确保订单支付成功后才激活订阅功能
5. **多订单处理**：正确处理多个升级订单，确保支付哪个账单就激活哪个计划
6. **防止冲突**：防止在有 pending 订单时创建新的升级订单
7. **事件驱动**：通过事件机制实现状态同步
8. **完整审计**：记录所有操作日志和统计信息
9. **缓存管理**：及时清理相关缓存确保数据一致性
10. **错误处理**：完善的错误处理和恢复机制

这种设计确保了订阅升降级操作的原子性、一致性和可追溯性，避免了用户获得功能但未支付的问题，同
时正确处理多个升级订单的复杂场景。
