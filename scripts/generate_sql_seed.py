#!/usr/bin/env python3
"""
SQL种子数据生成脚本
根据种子数据脚本生成可直接在数据库中执行的SQL语句
"""
import argparse
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.services.data_provider import DataProvider
from svc.core.security.password import get_password_hash
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class SQLGenerator:
    """SQL生成器"""
    
    def __init__(self):
        self.data_provider = DataProvider()
        self.now = get_utc_now_without_tzinfo()
        self.sql_statements = []
        self.id_mappings = {}  # 用于存储ID映射关系
        
    def add_sql(self, sql: str):
        """添加SQL语句"""
        self.sql_statements.append(sql)
    
    def escape_string(self, value: str) -> str:
        """转义字符串"""
        if value is None:
            return "NULL"
        return f"'{value.replace("'", "''")}'"
    
    def escape_json(self, value: Any) -> str:
        """转义JSON值"""
        if value is None:
            return "NULL"
        return f"'{json.dumps(value, ensure_ascii=False).replace("'", "''")}'"
    
    def escape_datetime(self, value: datetime) -> str:
        """转义日期时间"""
        if value is None:
            return "NULL"
        return f"'{value.isoformat()}'"
    
    def generate_clear_data_sql(self):
        """生成清空数据的SQL"""
        self.add_sql("-- 清空现有数据")
        self.add_sql("-- 注意：这会删除所有数据，请谨慎使用")
        self.add_sql("")
        
        # 按依赖关系倒序清空表
        tables_to_clear = [
            "reward_records",
            "invitations", 
            "reward_strategies",
            "campaigns",
            "subscription_plans",
            "user_roles",
            "users",
            "role_permissions",
            "roles",
            "permissions",
            "product_skus",
            "product_spec_values",
            "inventories",
            "products",
            "spec_values",
            "specs",
            "categories",
            "shop_images",
            "shop_albums", 
            "shops",
            "album_images",
            "albums",
            "banner_images",
            "banner_albums"
        ]
        
        for table in tables_to_clear:
            self.add_sql(f"DELETE FROM {table};")
        
        self.add_sql("")
        self.add_sql("-- 重置序列")
        sequences = [
            "permissions_id_seq",
            "roles_id_seq", 
            "users_id_seq",
            "subscription_plans_id_seq",
            "campaigns_id_seq",
            "reward_strategies_id_seq",
            "invitations_id_seq",
            "reward_records_id_seq",
            "categories_id_seq",
            "specs_id_seq",
            "spec_values_id_seq",
            "products_id_seq",
            "product_skus_id_seq",
            "inventories_id_seq",
            "shops_id_seq",
            "albums_id_seq",
            "album_images_id_seq",
            "banner_albums_id_seq",
            "banner_images_id_seq"
        ]
        
        for seq in sequences:
            self.add_sql(f"ALTER SEQUENCE {seq} RESTART WITH 1;")
        
        self.add_sql("")
    
    def generate_permissions_sql(self):
        """生成权限数据SQL"""
        self.add_sql("-- 插入权限数据")
        permissions = self.data_provider.get_permissions_data()
        
        for i, perm in enumerate(permissions, 1):
            sql = f"INSERT INTO permissions (id, name, description, created_at, updated_at) VALUES ({i}, {self.escape_string(perm['name'])}, {self.escape_string(perm['description'])}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"
            self.add_sql(sql)
            self.id_mappings[f"permission_{perm['name']}"] = i
        
        self.add_sql("")
    
    def generate_roles_sql(self):
        """生成角色数据SQL"""
        self.add_sql("-- 插入角色数据")
        roles = self.data_provider.get_roles_data()
        
        for i, role in enumerate(roles, 1):
            sql = f"INSERT INTO roles (id, name, description, is_system, created_at, updated_at) VALUES ({i}, {self.escape_string(role['name'])}, {self.escape_string(role['description'])}, {str(role['is_system']).lower()}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"
            self.add_sql(sql)
            self.id_mappings[f"role_{role['name']}"] = i
        
        self.add_sql("")
    
    def generate_role_permissions_sql(self):
        """生成角色权限关联SQL"""
        self.add_sql("-- 插入角色权限关联")
        roles = self.data_provider.get_roles_data()
        
        for role in roles:
            role_id = self.id_mappings[f"role_{role['name']}"]
            for perm_name in role['permissions']:
                if perm_name == "*:*":
                    # 超级管理员拥有所有权限
                    for perm in self.data_provider.get_permissions_data():
                        perm_id = self.id_mappings[f"permission_{perm['name']}"]
                        sql = f"INSERT INTO role_permissions (role_id, permission_id) VALUES ({role_id}, {perm_id});"
                        self.add_sql(sql)
                else:
                    perm_id = self.id_mappings[f"permission_{perm_name}"]
                    sql = f"INSERT INTO role_permissions (role_id, permission_id) VALUES ({role_id}, {perm_id});"
                    self.add_sql(sql)
        
        self.add_sql("")
    
    def generate_users_sql(self):
        """生成用户数据SQL"""
        self.add_sql("-- 插入用户数据")
        users = self.data_provider.get_users_data()
        
        for i, user in enumerate(users, 1):
            hashed_password = get_password_hash(user['password'])
            is_superuser = user.get('is_superuser', False)  # 使用get方法，默认为False
            sql = f"""INSERT INTO users (id, username, email, password, full_name, is_active, is_superuser, created_at, updated_at) 
VALUES ({i}, {self.escape_string(user['username'])}, {self.escape_string(user['email'])}, {self.escape_string(hashed_password)}, {self.escape_string(user['fullname'])}, true, {str(is_superuser).lower()}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
            self.id_mappings[f"user_{user['username']}"] = i
        
        self.add_sql("")
    
    def generate_user_roles_sql(self):
        """生成用户角色关联SQL"""
        self.add_sql("-- 插入用户角色关联")
        users = self.data_provider.get_users_data()
        
        for user in users:
            user_id = self.id_mappings[f"user_{user['username']}"]
            for role_name in user['role_names']:
                role_id = self.id_mappings[f"role_{role_name}"]
                sql = f"INSERT INTO user_roles (user_id, role_id) VALUES ({user_id}, {role_id});"
                self.add_sql(sql)
        
        self.add_sql("")
    
    def generate_subscription_plans_sql(self):
        """生成订阅计划数据SQL"""
        self.add_sql("-- 插入订阅计划数据")
        admin_user_id = self.id_mappings["user_super"]
        plans = self.data_provider.get_subscription_plans_data(admin_user_id)
        
        for i, plan in enumerate(plans, 1):
            sql = f"""INSERT INTO subscription_plans (id, name, description, price, currency, interval, tier, max_users, max_storage, max_projects, features, user_id, created_at, updated_at) 
VALUES ({i}, {self.escape_string(plan['name'])}, {self.escape_string(plan['description'])}, {plan['price']}, {self.escape_string(plan['currency'])}, {self.escape_string(plan['interval'])}, {self.escape_string(plan['tier'])}, {plan['max_users']}, {plan['max_storage']}, {plan['max_projects']}, {self.escape_json(plan['features'])}, {plan['user_id']}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
            self.id_mappings[f"subscription_plan_{plan['name']}"] = i
        
        self.add_sql("")
    
    def generate_campaigns_sql(self):
        """生成营销活动数据SQL"""
        self.add_sql("-- 插入营销活动数据")
        admin_user_id = self.id_mappings["user_super"]
        campaigns = self.data_provider.get_campaigns_data(admin_user_id)
        
        for i, campaign in enumerate(campaigns, 1):
            sql = f"""INSERT INTO campaigns (id, name, description, start_date, end_date, status, creator_id, anti_abuse_strategy, created_at, updated_at) 
VALUES ({i}, {self.escape_string(campaign['name'])}, {self.escape_string(campaign['description'])}, {self.escape_datetime(campaign['start_date'])}, {self.escape_datetime(campaign['end_date'])}, {self.escape_string(campaign['status'])}, {campaign['creator_id']}, {self.escape_string(campaign['anti_abuse_strategy'])}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
            self.id_mappings[f"campaign_{i}"] = i
        
        self.add_sql("")
    
    def generate_reward_strategies_sql(self):
        """生成奖励策略数据SQL"""
        self.add_sql("-- 插入奖励策略数据")
        campaign_ids = [self.id_mappings["campaign_1"], self.id_mappings["campaign_2"], self.id_mappings["campaign_3"]]
        strategies = self.data_provider.get_reward_strategies_data(campaign_ids)
        
        for i, strategy in enumerate(strategies, 1):
            sql = f"""INSERT INTO reward_strategies (id, campaign_id, name, description, reward_type, is_for_inviter, is_for_invitee, base_reward, min_invitations, max_rewards, tiered_config, created_at, updated_at) 
VALUES ({i}, {strategy['campaign_id']}, {self.escape_string(strategy['name'])}, {self.escape_string(strategy['description'])}, {self.escape_string(strategy['reward_type'])}, {str(strategy['is_for_inviter']).lower()}, {str(strategy['is_for_invitee']).lower()}, {strategy['base_reward']}, {strategy['min_invitations'] or 'NULL'}, {strategy['max_rewards']}, {self.escape_json(strategy.get('tiered_config'))}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
        
        self.add_sql("")
    
    def generate_categories_sql(self):
        """生成分类数据SQL"""
        self.add_sql("-- 插入分类数据")
        categories = self.data_provider.get_categories_data()
        
        for i, category in enumerate(categories, 1):
            sql = f"""INSERT INTO categories (id, name, description, slug, created_at, updated_at) 
VALUES ({i}, {self.escape_string(category['name'])}, {self.escape_string(category['description'])}, {self.escape_string(category['slug'])}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
            self.id_mappings[f"category_{category['slug']}"] = i
        
        self.add_sql("")
    
    def generate_specs_and_values_sql(self):
        """生成规格和规格值数据SQL"""
        self.add_sql("-- 插入规格和规格值数据")
        categories = self.data_provider.get_categories_data()
        
        spec_id = 1
        spec_value_id = 1
        
        for category in categories:
            category_id = self.id_mappings[f"category_{category['slug']}"]
            
            for spec_data in category['_specs']:
                # 插入规格
                sql = f"""INSERT INTO specs (id, category_id, name, created_at, updated_at) 
VALUES ({spec_id}, {category_id}, {self.escape_string(spec_data['name'])}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
                self.add_sql(sql)
                
                # 插入规格值
                for option in spec_data['options']:
                    sql = f"""INSERT INTO spec_values (id, spec_id, value, created_at, updated_at) 
VALUES ({spec_value_id}, {spec_id}, {self.escape_string(option)}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
                    self.add_sql(sql)
                    spec_value_id += 1
                
                spec_id += 1
        
        self.add_sql("")
    
    def generate_banner_sql(self):
        """生成Banner数据SQL"""
        self.add_sql("-- 插入Banner数据")
        banner_data = self.data_provider.get_banner_image_urls()
        
        for i, banner in enumerate(banner_data, 1):
            # 插入Banner相册
            sql = f"""INSERT INTO banner_albums (id, title, description, created_at, updated_at) 
VALUES ({i}, {self.escape_string(banner['title'])}, {self.escape_string(banner['description'])}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
            
            # 插入Banner图片
            sql = f"""INSERT INTO banner_images (id, album_id, image_url, link_url, sort_order, created_at, updated_at) 
VALUES ({i}, {i}, {self.escape_string(banner['image_url'])}, {self.escape_string(banner['link_url'])}, {banner['sort_order']}, {self.escape_datetime(self.now)}, {self.escape_datetime(self.now)});"""
            self.add_sql(sql)
        
        self.add_sql("")
    
    def generate_sql_file(self, output_file: str = "seed_data.sql"):
        """生成完整的SQL文件"""
        print(f"🔧 开始生成SQL文件: {output_file}")
        
        # 生成各部分SQL
        self.generate_clear_data_sql()
        self.generate_permissions_sql()
        self.generate_roles_sql()
        self.generate_role_permissions_sql()
        self.generate_users_sql()
        self.generate_user_roles_sql()
        self.generate_subscription_plans_sql()
        self.generate_campaigns_sql()
        self.generate_reward_strategies_sql()
        self.generate_categories_sql()
        self.generate_specs_and_values_sql()
        self.generate_banner_sql()
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- 种子数据SQL脚本\n")
            f.write(f"-- 生成时间: {self.now}\n")
            f.write("-- 注意：执行前请备份数据库\n")
            f.write("\n")
            f.write("BEGIN;\n\n")
            
            for sql in self.sql_statements:
                f.write(sql + "\n")
            
            f.write("\nCOMMIT;\n")
        
        print(f"✅ SQL文件生成完成: {output_file}")
        print(f"📊 共生成 {len(self.sql_statements)} 条SQL语句")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成种子数据SQL脚本")
    parser.add_argument("--output", "-o", default="seed_data.sql", help="输出文件名（默认: seed_data.sql）")
    parser.add_argument("--clear-only", action="store_true", help="只生成清空数据的SQL")
    args = parser.parse_args()
    
    generator = SQLGenerator()
    
    if args.clear_only:
        # 只生成清空数据的SQL
        generator.generate_clear_data_sql()
        output_file = "clear_data.sql"
    else:
        # 生成完整的种子数据SQL
        output_file = args.output
    
    generator.generate_sql_file(output_file)


if __name__ == "__main__":
    main()
