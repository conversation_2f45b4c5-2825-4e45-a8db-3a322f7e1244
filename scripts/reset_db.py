#!/usr/bin/env python
"""
数据库重置脚本
用于重新初始化数据库，包括回滚所有迁移、重新应用所有迁移
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import find_dotenv, load_dotenv

# 加载环境变量
env_file = find_dotenv(filename='.env.local')
if env_file:
    print(f"加载环境配置文件: {env_file}")
    load_dotenv(dotenv_path=env_file)
else:
    load_dotenv()
    print("警告: 未找到 .env.local 文件，使用默认配置")


def validate_environment_variables():
    """验证必要的环境变量"""
    required_vars = {
        'POSTGRES_DB': '数据库名称',
        'POSTGRES_USER': '数据库用户',
        'POSTGRES_PASSWORD': '数据库密码',
        'POSTGRES_SERVER': '数据库服务器'
    }
    
    missing_vars = []
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
        else:
            print(f"✓ {var}: {value}")
    
    if missing_vars:
        print(f"❌ 缺少必要的环境变量: {', '.join(missing_vars)}")
        return False
    
    print("✓ 所有必要的环境变量已配置")
    return True


def run_command(command, check=True, capture_output=False):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(command, shell=True, check=check)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if capture_output and e.stderr:
            print(f"错误详情: {e.stderr}")
        return e


def check_database_exists(db_name, db_user, db_password, db_server):
    """检查数据库是否存在"""
    # 尝试直接连接到目标数据库
    command = f"PGPASSWORD={db_password} psql -h {db_server} -U {db_user} -d {db_name} -c \"SELECT 1;\""
    result = run_command(command, check=False, capture_output=True)
    return result.returncode == 0


def check_database_connections(db_name, db_user, db_password, db_server):
    """检查数据库是否有活跃连接"""
    command = f"PGPASSWORD={db_password} psql -h {db_server} -U {db_user} -d {db_name} -c \"SELECT count(*) FROM pg_stat_activity WHERE datname = '{db_name}' AND state = 'active';\""
    result = run_command(command, check=False, capture_output=True)
    if result.returncode == 0:
        try:
            count = int(result.stdout.strip().split('\n')[2])
            return count
        except (ValueError, IndexError):
            return 0
    return 0


def force_disconnect_database(db_name, db_user, db_password, db_server):
    """强制断开数据库连接"""
    print(f"强制断开数据库 {db_name} 的所有连接...")
    command = f"PGPASSWORD={db_password} psql -h {db_server} -U {db_user} -c \"SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '{db_name}' AND pid <> pg_backend_pid();\""
    run_command(command, check=False)


def reset_database(recreate=False):
    """重置数据库
    
    Args:
        recreate: 是否重新创建数据库，如果为True，将删除并重新创建数据库；
                 如果为False，将只回滚并重新应用迁移
    """
    # 验证环境变量
    if not validate_environment_variables():
        print("❌ 环境变量验证失败，请检查 .env.local 文件")
        return False
    
    # 获取数据库连接信息
    db_name = os.getenv("POSTGRES_DB", "aitools")
    db_user = os.getenv("POSTGRES_USER", "funny")
    db_password = os.getenv("POSTGRES_PASSWORD", "qinjun666")
    db_server = os.getenv("POSTGRES_SERVER", "localhost")
    
    print(f"\n数据库配置:")
    print(f"  服务器: {db_server}")
    print(f"  数据库: {db_name}")
    print(f"  用户: {db_user}")
    
    if recreate:
        # 检查数据库是否存在
        if check_database_exists(db_name, db_user, db_password, db_server):
            print(f"✓ 数据库 {db_name} 已存在")
            
            # 检查是否有活跃连接
            active_connections = check_database_connections(db_name, db_user, db_password, db_server)
            if active_connections > 0:
                print(f"⚠️  数据库 {db_name} 有 {active_connections} 个活跃连接")
                force_disconnect_database(db_name, db_user, db_password, db_server)
            
            # 删除数据库
            print(f"删除数据库 {db_name}...")
            result = run_command(f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}", check=False)
            if result.returncode != 0:
                print("❌ 删除数据库失败，尝试强制删除...")
                # 尝试强制删除
                force_disconnect_database(db_name, db_user, db_password, db_server)
                result = run_command(f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}", check=False)
                if result.returncode != 0:
                    print("❌ 强制删除数据库失败")
                    return False
        else:
            print(f"数据库 {db_name} 不存在，跳过删除步骤")
        
        # 创建数据库
        print(f"创建数据库 {db_name}...")
        result = run_command(f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}")
        if result.returncode != 0:
            print("❌ 创建数据库失败")
            return False
        print("✓ 数据库创建成功")
    else:
        # 回滚所有迁移
        print("回滚所有迁移...")
        result = run_command("alembic downgrade base")
        if result.returncode != 0:
            print("❌ 回滚迁移失败")
            return False
    
    print("✓ 数据库重置完成！")
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重置数据库")
    parser.add_argument("--recreate", action="store_true", help="是否重新创建数据库")
    args = parser.parse_args()
    
    success = reset_database(args.recreate)
    sys.exit(0 if success else 1) 