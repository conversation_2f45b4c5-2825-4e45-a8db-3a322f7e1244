"""
数据库种子数据脚本。
用于导入数据库的基础种子数据。

使用方式：
python scripts/seed_data.py  # 导入种子数据

环境配置：
脚本会自动使用核心配置模块的环境管理，按优先级加载：
1. .env (基础环境文件)
2. .env.{environment} (环境特定文件)
3. .env.local (本地覆盖文件)

注意：使用前请确保数据库表已经创建完成。
"""
import argparse
import asyncio
import logging
import os
import sys
import time
from datetime import timedelta
from functools import wraps
from pathlib import Path
from typing import Any, Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入数据库相关模块
from sqlalchemy.ext.asyncio import AsyncSession
from svc.apps.auth.models import User
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.products.repositories.inventory import InventoryRepository
from svc.apps.products.schemas.inventory import InventoryCreate
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.core.database.utils import get_session_for_script

# 创建日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('seed_data')

def performance_monitor(operation_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            logger.info(f"开始执行 {operation_name}...")
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                execution_time = end_time - start_time
                logger.info(f"{operation_name} 完成，耗时: {execution_time:.2f} 秒")
                return result
            except Exception as e:
                end_time = time.time()
                execution_time = end_time - start_time
                logger.error(f"{operation_name} 失败，耗时: {execution_time:.2f} 秒，错误: {str(e)}")
                raise
        return wrapper
    return decorator

async def create_inventories(db: AsyncSession, products: list) -> list:
    """为每个商品创建库存"""
    try:
        repo = InventoryRepository(db)
        created = []
        # 商品库存
        for product in products:
            invs, _ = await repo.get_paginated(filters={"product_id": product.id})
            if invs:
                logger.info(f"商品 {product.name} 已有库存，跳过")
                created.extend(invs)
                continue
            inv = InventoryCreate(
                product_id=product.id,
                quantity=product.stock_quantity,
                available_quantity=product.stock_quantity,
                status="available"
            )
            created_inv = await repo.create(inv.model_dump())
            created.append(created_inv)
            logger.info(f"创建商品库存: {product.name}")
        await db.commit()
        logger.info(f"成功创建{len(created)}个库存记录")
        return created
    except Exception as e:
        logger.error(f"创建库存时发生错误: {str(e)}", exc_info=True)
        await db.rollback()
        raise

async def prepare_invitations_data(campaigns: List, users: List[User]) -> List[Dict[str, Any]]:
    """准备邀请记录数据"""
    now = get_utc_now_without_tzinfo()
    
    return [
        # user1邀请user2(已使用)
        {
            "campaign_id": campaigns[0].id,
            "inviter_id": users[1].id,  # user1
            "invitee_id": users[2].id,  # user2
            "code": "INV123456",
            "is_used": True,
            "used_at": now - timedelta(days=15),
            "invitee_ip": "*************",
            "invitee_device": "iPhone 13",
            "opened_count": 2
        },
        # user2邀请其他用户(未使用)
        {
            "campaign_id": campaigns[0].id,
            "inviter_id": users[2].id,  # user2
            "invitee_id": None,  # 未被使用
            "code": "INV234567",
            "is_used": False,
            "used_at": None,
            "invitee_ip": None,
            "invitee_device": None,
            "opened_count": 5
        },
        # user1在另一个活动中的邀请
        {
            "campaign_id": campaigns[1].id,
            "inviter_id": users[1].id,  # user1
            "invitee_id": None,  # 未被使用
            "code": "SPR123456",
            "is_used": False,
            "used_at": None,
            "invitee_ip": None,
            "invitee_device": None,
            "opened_count": 3
        }
    ]

async def prepare_reward_records_data(campaigns: List, users: List[User], invitations: List[Invitation], reward_strategies: List) -> List[Dict[str, Any]]:
    """准备奖励记录数据"""
    import json
    now = get_utc_now_without_tzinfo()
    
    # 获取已使用的邀请
    used_invitation = None
    for inv in invitations:
        if inv.is_used:
            used_invitation = inv
            break
    
    if not used_invitation:
        logger.warning("没有找到已使用的邀请记录，将使用第一个邀请记录")
        used_invitation = invitations[0]
    
    return [
        # 邀请人的奖励(已发放)
        {
            "campaign_id": used_invitation.campaign_id,
            "invitation_id": used_invitation.id,
            "strategy_id": reward_strategies[0].id,  # 对应"邀请人奖励"策略
            "user_id": used_invitation.inviter_id,
            "trigger_event": "invitation.completed",
            "trigger_context": json.dumps({
                "inviter_id": used_invitation.inviter_id,
                "invitee_id": used_invitation.invitee_id,
                "invitation_id": used_invitation.id
            }),
            "reward_type": "cash",
            "reward_value": 50.0,
            "reward_description": "成功邀请好友注册",
            "calculation_config": json.dumps({
                "type": "fixed",
                "reward_type": "cash",
                "amount": 50.0
            }),
            "calculated_value": 50.0,
            "distribution_channel": "wallet",
            "distribution_status": "success",
            "distribution_result": json.dumps({
                "status": "success",
                "transaction_id": "tx_123456"
            }),
            "status": "issued",
            "issued_at": now - timedelta(days=14)
        },
        # 被邀请人的奖励(已发放)
        {
            "campaign_id": used_invitation.campaign_id,
            "invitation_id": used_invitation.id,
            "strategy_id": reward_strategies[1].id,  # 对应"受邀人奖励"策略
            "user_id": used_invitation.invitee_id if used_invitation.invitee_id else users[2].id,
            "trigger_event": "invitation.completed",
            "trigger_context": json.dumps({
                "inviter_id": used_invitation.inviter_id,
                "invitee_id": used_invitation.invitee_id,
                "invitation_id": used_invitation.id
            }),
            "reward_type": "cash",
            "reward_value": 20.0,
            "reward_description": "接受邀请注册",
            "calculation_config": json.dumps({
                "type": "fixed",
                "reward_type": "cash",
                "amount": 20.0
            }),
            "calculated_value": 20.0,
            "distribution_channel": "wallet",
            "distribution_status": "success",
            "distribution_result": json.dumps({
                "status": "success",
                "transaction_id": "tx_123457"
            }),
            "status": "issued",
            "issued_at": now - timedelta(days=14)
        },
        # 另一个活动中的奖励(未发放)
        {
            "campaign_id": campaigns[1].id,
            "invitation_id": None,
            "strategy_id": reward_strategies[2].id,  # 对应"春季推广邀请奖励"策略
            "user_id": users[1].id,  # user1
            "trigger_event": "invitation.completed",
            "trigger_context": json.dumps({
                "user_id": users[1].id,
                "campaign_id": campaigns[1].id
            }),
            "reward_type": "cash",
            "reward_value": 30.0,
            "reward_description": "春季活动特别奖励",
            "calculation_config": json.dumps({
                "type": "fixed",
                "reward_type": "cash",
                "amount": 30.0
            }),
            "calculated_value": 30.0,
            "distribution_channel": "wallet",
            "distribution_status": "pending",
            "distribution_result": None,
            "status": "pending",
            "issued_at": None
        }
    ]

async def bulk_create_categories_without_specs(seed_service, categories_data):
    """批量创建分类，仅传递Category模型字段，返回(分类对象列表, 分类ID到specs映射)"""
    # 剥离specs字段
    categories_to_create = []
    catid_to_specs = {}
    for cat in categories_data:
        cat_data = {k: v for k, v in cat.items() if not k.endswith('specs')}
        specs = cat.get("_specs")
        categories_to_create.append(cat_data)
        catid_to_specs[cat["slug"]] = specs
    # 批量创建分类
    categories = await seed_service.bulk_create_categories(categories_to_create)
    # 构建slug到category对象映射
    slug_to_cat = {cat.slug: cat for cat in categories}
    # 返回分类对象列表和category_id到specs的映射
    catid_specs_map = {slug_to_cat[slug].id: specs for slug, specs in catid_to_specs.items() if slug in slug_to_cat}
    return categories, catid_specs_map

@performance_monitor("种子数据初始化")
async def main():
    """主函数：初始化所有种子数据"""
    try:
        from scripts.services.seed_data_service import SeedDataService
        from scripts.services.data_provider import DataProvider

        # 获取当前环境信息
        from svc.core.config.settings import get_settings
        settings = get_settings()
        logger.info(f"当前环境: {settings.env}")
        
        async with get_session_for_script() as db:
            logger.info("开始初始化种子数据...")
            overall_start_time = time.time()
            
            # 初始化服务
            seed_service = SeedDataService(db)
            data_provider = DataProvider()
            
            # 1. 权限和角色
            step_start = time.time()
            permissions_data = data_provider.get_permissions_data()
            permissions = await seed_service.bulk_create_permissions(permissions_data)
            roles_data = data_provider.get_roles_data()
            roles = await seed_service.bulk_create_roles(roles_data, permissions)
            logger.info(f"权限和角色创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 2. 用户
            step_start = time.time()
            users_data = data_provider.get_users_data()
            users = await seed_service.bulk_create_users(users_data, roles)
            logger.info(f"用户创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 3. 订阅计划
            step_start = time.time()
            admin_user_id = users[0].id
            subscription_plans_data = data_provider.get_subscription_plans_data(admin_user_id)
            subscription_plans = await seed_service.bulk_create_subscription_plans(subscription_plans_data)
            logger.info(f"订阅计划创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 4. 营销模块数据
            step_start = time.time()
            campaigns_data = data_provider.get_campaigns_data(admin_user_id)
            campaigns = await seed_service.bulk_create_campaigns(campaigns_data)
            campaign_ids = [campaign.id for campaign in campaigns]
            
            reward_strategies_data = data_provider.get_reward_strategies_data(campaign_ids)
            reward_strategies = await seed_service.bulk_create_reward_strategies(reward_strategies_data)
            
            # 创建奖励分发渠道
            reward_channels_data = data_provider.get_reward_distribution_channels_data()
            reward_channels = await seed_service.bulk_create_reward_distribution_channels(reward_channels_data)
            
            invitations_data = await prepare_invitations_data(campaigns, users)
            invitations = await seed_service.bulk_create_invitations(invitations_data)
            
            reward_records_data = await prepare_reward_records_data(campaigns, users, invitations, reward_strategies)
            reward_records = await seed_service.bulk_create_reward_records(reward_records_data)
            logger.info(f"营销模块数据创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 5. 分类（含specs仅内存）
            step_start = time.time()
            categories_data = data_provider.get_categories_data()
            categories, catid_specs_map = await bulk_create_categories_without_specs(seed_service, categories_data)
            logger.info(f"分类批量创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 6. 规格及规格值
            step_start = time.time()
            category_spec_map = await seed_service.bulk_create_specs_and_options(catid_specs_map)
            logger.info(f"规格及规格值批量创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 7. 产品
            step_start = time.time()
            product_image_urls = data_provider.get_product_image_urls()
            products_data, albums_data, images_data = await seed_service.prepare_products_data(categories, product_image_urls)
            products, albums, images = await seed_service.bulk_create_products_with_albums(products_data, albums_data, images_data)
            logger.info(f"产品创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 8. 为每个产品生成SKU
            step_start = time.time()
            await seed_service.bulk_create_product_skus(products, category_spec_map)
            logger.info(f"产品SKU创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 9. 门店
            step_start = time.time()
            shops_data, shops_albums_data, shops_images_data = await seed_service.prepare_shops_data()
            shops, shop_albums, shop_images = await seed_service.bulk_create_shops_with_albums(shops_data, shops_albums_data, shops_images_data)
            logger.info(f"门店创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 10. 库存
            step_start = time.time()
            await create_inventories(db, products)
            logger.info(f"库存创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            # 11. Banner轮播图
            step_start = time.time()
            banner_data = data_provider.get_banner_image_urls()
            banner_albums, banner_images = await seed_service.bulk_create_banner_albums(banner_data)
            logger.info(f"Banner轮播图创建完成，耗时: {time.time() - step_start:.2f} 秒")
            
            logger.info(f"✅ 所有种子数据初始化完成！总耗时: {time.time() - overall_start_time:.2f} 秒")
    except Exception as e:
        logger.error(f"初始化数据时发生错误: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="数据库种子数据脚本 - 导入基础种子数据")
    args = parser.parse_args()

    # 运行种子数据导入
    asyncio.run(main())