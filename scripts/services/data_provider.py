"""
数据提供者类
统一管理所有种子数据的定义和验证
"""
import json
from typing import Any, Dict, List, Optional
from datetime import timedelta
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class DataProvider:
    """数据提供者，统一管理所有测试数据"""
    
    @staticmethod
    def get_permissions_data() -> List[Dict[str, str]]:
        """获取权限数据"""
        return [
            # 全局权限
            {"name": "*:*", "description": "所有权限（超级管理员）"},
            
            # 认证授权模块权限
            {"name": "user:read", "description": "查看用户信息"},
            {"name": "user:create", "description": "创建用户"},
            {"name": "user:update", "description": "更新用户信息"},
            {"name": "user:delete", "description": "删除用户"},
            {"name": "user:manage", "description": "管理用户（禁用/启用账户等）"},
            {"name": "role:read", "description": "查看角色信息"},
            {"name": "role:create", "description": "创建角色"},
            {"name": "role:update", "description": "更新角色"},
            {"name": "role:delete", "description": "删除角色"},
            {"name": "role:assign", "description": "分配角色给用户"},
            {"name": "permission:read", "description": "查看权限信息"},
            {"name": "permission:assign", "description": "给角色分配权限"},
            
            # 计费/订阅模块权限
            {"name": "subscription_plan:read", "description": "查看订阅计划"},
            {"name": "subscription_plan:create", "description": "创建订阅计划"},
            {"name": "subscription_plan:update", "description": "更新订阅计划"},
            {"name": "subscription_plan:delete", "description": "删除订阅计划"},
            {"name": "subscription:read", "description": "查看订阅信息"},
            {"name": "subscription:create", "description": "创建订阅"},
            {"name": "subscription:update", "description": "更新订阅"},
            {"name": "subscription:cancel", "description": "取消订阅"},
            {"name": "subscription:renew", "description": "续订订阅"},
            {"name": "invoice:read", "description": "查看发票"},
            {"name": "invoice:create", "description": "创建发票"},
            {"name": "invoice:update", "description": "更新发票"},
            {"name": "invoice:delete", "description": "删除发票"},
            {"name": "payment:read", "description": "查看支付信息"},
            {"name": "payment:process", "description": "处理支付"},
            {"name": "payment:refund", "description": "退款操作"},
            
            # 营销模块权限
            {"name": "campaign:read", "description": "查看营销活动"},
            {"name": "campaign:create", "description": "创建营销活动"},
            {"name": "campaign:update", "description": "更新营销活动"},
            {"name": "campaign:delete", "description": "删除营销活动"},
            {"name": "campaign:activate", "description": "激活/停用营销活动"},
            {"name": "invitation:read", "description": "查看邀请信息"},
            {"name": "invitation:create", "description": "创建邀请"},
            {"name": "invitation:delete", "description": "删除邀请"},
            {"name": "reward_strategy:read", "description": "查看奖励策略"},
            {"name": "reward_strategy:create", "description": "创建奖励策略"},
            {"name": "reward_strategy:update", "description": "更新奖励策略"},
            {"name": "reward_strategy:delete", "description": "删除奖励策略"},
            {"name": "reward_record:read", "description": "查看奖励记录"},
            {"name": "reward_record:create", "description": "创建奖励记录"},
            {"name": "reward_record:issue", "description": "发放奖励"},
            
            # 产品模块权限
            {"name": "product:read", "description": "查看产品信息"},
            {"name": "product:create", "description": "创建产品"},
            {"name": "product:update", "description": "更新产品"},
            {"name": "product:delete", "description": "删除产品"},
            {"name": "category:read", "description": "查看分类信息"},
            {"name": "category:create", "description": "创建分类"},
            {"name": "category:update", "description": "更新分类"},
            {"name": "category:delete", "description": "删除分类"},
            {"name": "inventory:read", "description": "查看库存信息"},
            {"name": "inventory:update", "description": "更新库存"},
            {"name": "spec:read", "description": "查看规格信息"},
            {"name": "spec:create", "description": "创建规格"},
            {"name": "spec:update", "description": "更新规格"},
            {"name": "spec:delete", "description": "删除规格"},
            {"name": "sku:read", "description": "查看SKU信息"},
            {"name": "sku:create", "description": "创建SKU"},
            {"name": "sku:update", "description": "更新SKU"},
            {"name": "sku:delete", "description": "删除SKU"},
            
            # 门店模块权限
            {"name": "shop:read", "description": "查看门店信息"},
            {"name": "shop:create", "description": "创建门店"},
            {"name": "shop:update", "description": "更新门店"},
            {"name": "shop:delete", "description": "删除门店"},
            
            # 图册模块权限
            {"name": "album:read", "description": "查看图册"},
            {"name": "album:create", "description": "创建图册"},
            {"name": "album:update", "description": "更新图册"},
            {"name": "album:delete", "description": "删除图册"},
            
            # 系统模块权限
            {"name": "config:read", "description": "查看系统配置"},
            {"name": "config:update", "description": "更新系统配置"},
            {"name": "audit_log:read", "description": "查看审计日志"},
            {"name": "audit_log:delete", "description": "删除审计日志"},
            {"name": "system:backup", "description": "系统备份操作"},
            {"name": "system:restore", "description": "系统恢复操作"},
            {"name": "system:maintenance", "description": "系统维护模式切换"},
            {"name": "system:monitor", "description": "系统监控访问权限"}
        ]
    
    @staticmethod
    def get_roles_data() -> List[Dict[str, Any]]:
        """获取角色数据"""
        return [
            {
                "name": "admin",
                "description": "系统管理员",
                "is_system": True,
                "permissions": ["*:*"]
            },
            {
                "name": "user",
                "description": "普通用户",
                "is_system": True,
                "permissions": [
                    "user:read", "product:read", "category:read",
                    "album:read", "shop:read"
                ]
            },
            {
                "name": "vip",
                "description": "VIP用户",
                "is_system": True,
                "permissions": [
                    "user:read", "product:read", "category:read",
                    "album:read", "shop:read", "subscription:read"
                ]
            }
        ]
    
    @staticmethod
    def get_users_data() -> List[Dict[str, Any]]:
        """获取用户数据"""
        return [
            {
                "email": "<EMAIL>",
                "password": "qinjun666",
                "username": "super",
                "fullname": "系统管理员",
                "is_superuser": True,
                "role_names": ["admin"]
            },
            {
                "email": "<EMAIL>",
                "password": "user123",
                "username": "user1",
                "fullname": "测试用户1",
                "role_names": ["user"]
            },
            {
                "email": "<EMAIL>",
                "password": "user123",
                "username": "user2",
                "fullname": "测试用户2",
                "role_names": ["user", "vip"]
            }
        ]
    
    @staticmethod
    def get_subscription_plans_data(creator_id: int) -> List[Dict[str, Any]]:
        """获取订阅计划数据"""
        return [
            {
                "name": "基础版",
                "description": "适合个人用户",
                "price": 99.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "basic",
                "max_users": 1,
                "max_storage": 1024,
                "max_projects": 3,
                "features": {"api_calls": 1000, "support": "email"},
                "user_id": creator_id
            },
            {
                "name": "专业版",
                "description": "适合小型团队",
                "price": 299.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "premium",
                "max_users": 5,
                "max_storage": 5120,
                "max_projects": 10,
                "features": {"api_calls": 5000, "support": "priority"},
                "user_id": creator_id
            },
            {
                "name": "企业版",
                "description": "适合大型企业",
                "price": 999.00,
                "currency": "CNY",
                "interval": "month",
                "tier": "enterprise",
                "max_users": 20,
                "max_storage": 20480,
                "max_projects": 50,
                "features": {"api_calls": "unlimited", "support": "24/7"},
                "user_id": creator_id
            }
        ]
    
    @staticmethod
    def get_campaigns_data(creator_id: int) -> List[Dict[str, Any]]:
        """获取营销活动数据"""
        now = get_utc_now_without_tzinfo()
        return [
            {
                "name": "邀请新用户",
                "description": "邀请新用户注册并使用我们的服务",
                "start_date": now,
                "end_date": now + timedelta(days=180),
                "status": "active",
                "creator_id": creator_id,
                "anti_abuse_strategy": "both"
            },
            {
                "name": "首次购买奖励",
                "description": "奖励首次在平台上购买的用户",
                "start_date": now,
                "end_date": now + timedelta(days=90),
                "status": "active",
                "creator_id": creator_id,
                "anti_abuse_strategy": "both"
            },
            {
                "name": "VIP会员专享",
                "description": "VIP会员专享活动与优惠",
                "start_date": now,
                "end_date": now + timedelta(days=120),
                "status": "active",
                "creator_id": creator_id,
                "anti_abuse_strategy": "both"
            }
        ]
    
    @staticmethod
    def get_reward_strategies_data(campaign_ids: List[int]) -> List[Dict[str, Any]]:
        """获取奖励策略数据"""
        if len(campaign_ids) < 3:
            raise ValueError("需要至少3个营销活动来创建奖励策略")
        
        return [
            # 活动1的策略 - 邀请人奖励
            {
                "campaign_id": campaign_ids[0],
                "name": "邀请人奖励",
                "description": "邀请好友注册成功后，邀请人获得的奖励",
                "strategy_type": "fixed",
                "trigger_config": json.dumps({
                    "type": "event",
                    "events": ["invitation.completed"]
                }),
                "trigger_events": json.dumps(["invitation.completed"]),
                "target_config": json.dumps({
                    "type": "single",
                    "user_field": "inviter_id"
                }),
                "calculation_config": json.dumps({
                    "type": "fixed",
                    "reward_type": "cash",
                    "amount": 50.0
                }),
                "constraint_config": json.dumps({
                    "constraints": [
                        {
                            "type": "user_limit",
                            "max_rewards": 5
                        }
                    ]
                }),
                "distribution_config": json.dumps({
                    "channels": ["wallet"],
                    "priority": "immediate"
                }),
                "priority": 10
            },
            # 活动1的策略 - 受邀人奖励
            {
                "campaign_id": campaign_ids[0],
                "name": "受邀人奖励",
                "description": "被邀请注册成功后，受邀人获得的奖励",
                "strategy_type": "fixed",
                "trigger_config": json.dumps({
                    "type": "event",
                    "events": ["invitation.completed"]
                }),
                "trigger_events": json.dumps(["invitation.completed"]),
                "target_config": json.dumps({
                    "type": "single",
                    "user_field": "invitee_id"
                }),
                "calculation_config": json.dumps({
                    "type": "fixed",
                    "reward_type": "cash",
                    "amount": 20.0
                }),
                "constraint_config": json.dumps({
                    "constraints": [
                        {
                            "type": "user_limit",
                            "max_rewards": 1
                        }
                    ]
                }),
                "distribution_config": json.dumps({
                    "channels": ["wallet"],
                    "priority": "immediate"
                }),
                "priority": 5
            },
            # 活动2的策略 - 春季推广邀请奖励
            {
                "campaign_id": campaign_ids[1],
                "name": "春季推广邀请奖励",
                "description": "春季推广活动邀请奖励",
                "strategy_type": "tiered",
                "trigger_config": json.dumps({
                    "type": "event",
                    "events": ["invitation.completed"]
                }),
                "trigger_events": json.dumps(["invitation.completed"]),
                "target_config": json.dumps({
                    "type": "single",
                    "user_field": "inviter_id"
                }),
                "calculation_config": json.dumps({
                    "type": "tiered",
                    "reward_type": "cash",
                    "tiers": [
                        {"threshold": 3, "reward": 50.0},
                        {"threshold": 5, "reward": 100.0},
                        {"threshold": 10, "reward": 200.0}
                    ]
                }),
                "constraint_config": json.dumps({
                    "constraints": [
                        {
                            "type": "user_limit",
                            "max_rewards": 10
                        }
                    ]
                }),
                "distribution_config": json.dumps({
                    "channels": ["wallet"],
                    "priority": "immediate"
                }),
                "priority": 10
            },
            # 活动2的策略 - 春季推广受邀奖励
            {
                "campaign_id": campaign_ids[1],
                "name": "春季推广受邀奖励",
                "description": "春季推广活动受邀奖励",
                "strategy_type": "fixed",
                "trigger_config": json.dumps({
                    "type": "event",
                    "events": ["invitation.completed"]
                }),
                "trigger_events": json.dumps(["invitation.completed"]),
                "target_config": json.dumps({
                    "type": "single",
                    "user_field": "invitee_id"
                }),
                "calculation_config": json.dumps({
                    "type": "fixed",
                    "reward_type": "cash",
                    "amount": 30.0
                }),
                "constraint_config": json.dumps({
                    "constraints": [
                        {
                            "type": "user_limit",
                            "max_rewards": 1
                        }
                    ]
                }),
                "distribution_config": json.dumps({
                    "channels": ["wallet"],
                    "priority": "immediate"
                }),
                "priority": 5
            },
            # 活动3的策略 - VIP会员专享奖励
            {
                "campaign_id": campaign_ids[2],
                "name": "VIP会员专享奖励",
                "description": "VIP专享活动奖励",
                "strategy_type": "percentage",
                "trigger_config": json.dumps({
                    "type": "event",
                    "events": ["invitation.completed"]
                }),
                "trigger_events": json.dumps(["invitation.completed"]),
                "target_config": json.dumps({
                    "type": "multiple",
                    "users_field": "user_ids"
                }),
                "calculation_config": json.dumps({
                    "type": "percentage",
                    "reward_type": "cash",
                    "base_amount": 10.0,
                    "percentage_rate": 15.0
                }),
                "constraint_config": json.dumps({
                    "constraints": [
                        {
                            "type": "user_limit",
                            "max_rewards": 3
                        }
                    ]
                }),
                "distribution_config": json.dumps({
                    "channels": ["wallet"],
                    "priority": "immediate"
                }),
                "priority": 8
            }
        ]
    
    @staticmethod
    def get_reward_distribution_channels_data() -> List[Dict[str, Any]]:
        """获取奖励分发渠道数据"""
        return [
            {
                "name": "钱包分发",
                "channel_type": "wallet",
                "description": "通过用户钱包分发奖励",
                "config": json.dumps({
                    "wallet_type": "user_wallet",
                    "auto_confirm": True,
                    "notification": True
                }),
                "is_active": True,
                "priority": 10
            },
            {
                "name": "积分分发",
                "channel_type": "points",
                "description": "通过积分系统分发奖励",
                "config": json.dumps({
                    "points_type": "user_points",
                    "auto_confirm": True,
                    "notification": True
                }),
                "is_active": True,
                "priority": 5
            },
            {
                "name": "优惠券分发",
                "channel_type": "coupon",
                "description": "通过优惠券系统分发奖励",
                "config": json.dumps({
                    "coupon_type": "discount_coupon",
                    "auto_confirm": False,
                    "notification": True
                }),
                "is_active": True,
                "priority": 3
            }
        ]
    
    @staticmethod
    def get_categories_data() -> List[Dict[str, Any]]:
        """获取分类数据"""
        data = [
            {
                "name": "安全帽",
                "description": "安全帽分类，提供各种安全帽产品",
                "slug": "safety-helmet",
                "_specs": [
                    {"name": "颜色", "options": ["红色", "白色", "黑色"]},
                    {"name": "尺寸", "options": ["S", "M", "L"]},
                    {"name": "材质", "options": ["ABS", "碳纤维"]},
                ]
            },
            {
                "name": "反光衣",
                "description": "反光衣分类，提供各种反光衣产品",
                "slug": "reflective-vest",
                "_specs": [
                    {"name": "颜色", "options": ["黄色", "橙色"]},
                    {"name": "尺寸", "options": ["M", "L", "XL"]},
                ]
            }
        ]

        # 轻量校验：必填字段与名称唯一性
        required_fields = {"name", "slug"}
        names: set = set()
        slugs: set = set()
        for idx, item in enumerate(data, 1):
            missing = required_fields - set(item.keys())
            if missing:
                raise ValueError(f"分类数据第{idx}项缺少必填字段: {','.join(missing)}")
            n = item["name"].strip()
            s = item["slug"].strip()
            if n in names:
                raise ValueError(f"分类名称重复: {n}")
            if s in slugs:
                raise ValueError(f"分类slug重复: {s}")
            names.add(n)
            slugs.add(s)
        return data
    
    @staticmethod
    def get_product_image_urls() -> List[Dict[str, Any]]:
        """获取产品图片URL数据"""
        return [
            {
                "name": "反光衣",
                "urls": [
                    "http://res.yqbaijiu.com/20250626/9687fd13f22e41a795cbfad364726c61.png",
                    "http://res.yqbaijiu.com/20250626/********************************.png",
                ]
            },
            {
                "name": "安全帽",
                "urls": [
                    "http://res.yqbaijiu.com/20250626/c1db7d903cba4ef69780226fc806726a.png",
                    "http://res.yqbaijiu.com/20250626/041043e139424cb495f0bf9883117935.png",
                    "http://res.yqbaijiu.com/20250626/2bfcd3d75bb24f33892165ccba2d288e.png",
                    "http://res.yqbaijiu.com/20250626/b22092daafab42a4a793485880a24499.png",
                    "http://res.yqbaijiu.com/20250626/64cd99e7752544e4907685f6403af05f.png",
                ]
            }
        ]

    @staticmethod
    def get_banner_image_urls() -> List[Dict[str, Any]]:
        """获取banner轮播图图片URL数据"""
        return [
            {
                "title": "专业安全帽 品质保障",
                "description": "国标认证安全帽，轻便舒适，防护等级高，工地必备首选",
                "image_url": "http://res.yqbaijiu.com/20250626/c1db7d903cba4ef69780226fc806726a.png",
                "link_url": "/products?category=safety-helmets&feature=new",
                "sort_order": 1
            },
            {
                "title": "夏季清凉安全帽 限时特惠",
                "description": "透气网眼设计，夏季施工不闷热，限时8折优惠，数量有限",
                "image_url": "http://res.yqbaijiu.com/20250626/041043e139424cb495f0bf9883117935.png",
                "link_url": "/products?category=safety-helmets&promotion=summer",
                "sort_order": 2
            },
            {
                "title": "工地安全套装 一站式采购",
                "description": "安全帽+反光衣+安全鞋，套装优惠价，省心又省钱",
                "image_url": "http://res.yqbaijiu.com/20250626/2bfcd3d75bb24f33892165ccba2d288e.png",
                "link_url": "/products?category=safety-kits&bundle=true",
                "sort_order": 3
            },
            {
                "title": "高等级安全帽 工地标配",
                "description": "ABS材质，抗冲击性强，符合GB2811标准，工地安全首选",
                "image_url": "http://res.yqbaijiu.com/20250626/b22092daafab42a4a793485880a24499.png",
                "link_url": "/products?category=safety-helmets&grade=high",
                "sort_order": 4
            },
            {
                "title": "反光安全帽 夜间作业专用",
                "description": "内置LED灯带，夜间作业更安全，高亮度反光条，360度可见",
                "image_url": "http://res.yqbaijiu.com/20250626/64cd99e7752544e4907685f6403af05f.png",
                "link_url": "/products?category=safety-helmets&feature=reflective",
                "sort_order": 5
            }
        ]
