"""
种子数据服务类
提供高效的批量数据创建功能，基于现有的仓库模式和服务架构
"""
import json
import logging
import random
import time
from typing import Any, Dict, List, Optional, Tuple, Callable

from sqlalchemy import insert, select
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.repositories.album import AlbumRepository
from svc.apps.albums.repositories.album_image import AlbumImageRepository
from svc.apps.albums.schemas.album import AlbumCreate
from svc.apps.albums.schemas.album_image import AlbumImageCreate
from svc.apps.auth.models import Permission, Role, User, user_role
from svc.apps.auth.repositories import RoleRepository, UserRepository
from svc.apps.billing.models.subscription_plan import SubscriptionPlan
from svc.apps.billing.repositories import SubscriptionPlanRepository
from svc.apps.marketing.models.campaign import Campaign, CampaignStatus, AntiAbuseStrategy
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.models.reward import RewardRecord, RewardStrategy, RewardType, RewardDistributionChannel
from svc.apps.marketing.repositories import CampaignRepository, InvitationRepository, RewardRecordRepository, RewardStrategyRepository
from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.repositories.inventory import InventoryRepository
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.products.repositories.sku import ProductSKURepository
from svc.apps.products.repositories.spec import SpecOptionRepository, SpecRepository
from svc.apps.products.schemas.spec import SpecCreate, SpecOptionCreate
from svc.apps.shops.models.shop import ShopStatus
from svc.apps.shops.repositories.shop import ShopRepository
from svc.apps.shops.schemas.shop import ShopCreate
from svc.core.security.password import get_password_hash
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

logger = logging.getLogger(__name__)


def build_name_index(items: List[Any], name_attr: str = "name") -> Dict[str, Any]:
    """根据对象的名称属性构建映射，用于名称到对象的快速查找。

    - 重名时记录警告，但以首个为准，避免覆盖
    """
    name_to_item: Dict[str, Any] = {}
    for item in items or []:
        name_value = getattr(item, name_attr, None)
        if not name_value:
            logger.warning("对象缺少名称属性，跳过名称索引构建")
            continue
        if name_value in name_to_item:
            logger.warning(f"检测到重复名称: {name_value}，将使用首次出现的对象")
            continue
        name_to_item[name_value] = item
    return name_to_item


class SeedDataService(BaseService, CacheMixin, ErrorResultMixin, BatchOperationMixin):
    """种子数据服务，提供高效的批量数据创建功能"""
    
    def __init__(self, db: AsyncSession):
        super().__init__()
        self.db = db
        self._repositories = {}
        self._errors: List[Dict[str, str]] = []

    def _record_error(self, entity: str, key: str, reason: str) -> None:
        self._errors.append({"entity": entity, "key": key, "reason": reason})

    def _flush_error_summary(self, stage: str, max_items: int = 10) -> None:
        if not self._errors:
            return
        total = len(self._errors)
        preview = self._errors[:max_items]
        logger.warning(
            f"阶段[{stage}] 错误汇总: 共{total}条，示例: "
            + "; ".join([f"{e['entity']}({e['key']}): {e['reason']}" for e in preview])
        )
        self._errors.clear()
        
    def _get_repository(self, repo_class):
        """获取或创建仓库实例，实现仓库复用"""
        repo_name = repo_class.__name__
        if repo_name not in self._repositories:
            self._repositories[repo_name] = repo_class(self.db)
        return self._repositories[repo_name]
    
    async def bulk_create_permissions(self, permissions_data: List[Dict[str, Any]]) -> List[Permission]:
        """批量创建权限"""
        logger.info(f"批量创建 {len(permissions_data)} 个权限...")

        from sqlalchemy import select

        # 检查是否已存在
        result = await self.db.execute(select(Permission).limit(1))
        existing = result.scalars().first()
        if existing:
            logger.info("权限数据已存在，跳过创建")
            # 返回所有现有权限
            result = await self.db.execute(select(Permission))
            return result.scalars().all()

        # 创建临时仓库类用于批量操作
        class PermissionRepository:
            def __init__(self, db):
                self.db = db
                self.model = Permission

            async def bulk_create(self, data_list, **kwargs):
                from svc.core.repositories.base import BaseRepository
                base_repo = BaseRepository(self.db, self.model)
                return await base_repo.bulk_create(data_list, **kwargs)

        # 使用批量创建
        repo = PermissionRepository(self.db)
        created_count = await repo.bulk_create(permissions_data)

        # 返回创建的权限
        result = await self.db.execute(select(Permission).order_by(Permission.id))
        permissions = result.scalars().all()

        logger.info(f"成功批量创建 {created_count} 个权限")
        return permissions
    
    async def bulk_create_roles(self, roles_data: List[Dict[str, Any]], permissions: List[Permission]) -> List[Role]:
        """批量创建角色"""
        logger.info(f"批量创建 {len(roles_data)} 个角色...")
        
        # 检查是否已存在
        result = await self.db.execute(select(Role).where(Role.name == "admin"))
        existing = result.scalars().first()
        if existing:
            logger.info("角色数据已存在，跳过创建")
            result = await self.db.execute(select(Role))
            return result.scalars().all()
        
        # 创建权限名称到权限对象的映射
        permission_map = {perm.name: perm for perm in permissions}

        # 创建角色（需要处理权限关系，不能用纯bulk insert）
        created_roles = []
        for role_data in roles_data:
            role_permission_names = role_data.pop("permissions", [])
            role = Role(**role_data)
            
            # 将权限名称转换为权限对象
            role_permission_objects = []
            for perm_name in role_permission_names:
                if perm_name in permission_map:
                    role_permission_objects.append(permission_map[perm_name])
                else:
                    logger.warning(f"权限 '{perm_name}' 不存在，跳过")

            role.permissions = role_permission_objects
            self.db.add(role)
            created_roles.append(role)
        
        await self.db.commit()
        logger.info(f"成功批量创建 {len(created_roles)} 个角色")
        return created_roles
    
    async def bulk_create_categories(self, categories_data: List[Dict[str, Any]]) -> List:
        """批量创建分类"""
        logger.info(f"批量创建 {len(categories_data)} 个分类...")
        
        category_repo = self._get_repository(CategoryRepository)
        
        # 检查已存在的分类
        existing_categories, _ = await category_repo.get_paginated(filters={"status": "active"}, page_size=1000)
        existing_names = {cat.name for cat in existing_categories}
        
        # 过滤出需要创建的分类
        to_create = [data for data in categories_data if data['name'] not in existing_names]
        
        if not to_create:
            logger.info("所有分类已存在，跳过创建")
            return existing_categories
        
        # 使用批量创建
        created_count = await category_repo.bulk_create(to_create)
        logger.info(f"成功批量创建 {created_count} 个分类")
        
        # 返回所有分类
        all_categories, _ = await category_repo.get_paginated(filters={"status": "active"}, page_size=1000)
        return all_categories
    
    async def bulk_create_users(self, users_data: List[Dict[str, Any]], roles: List[Role]) -> List[User]:
        """批量创建用户"""
        logger.info(f"批量创建 {len(users_data)} 个用户...")
        
        # 检查是否已存在
        result = await self.db.execute(select(User).where(User.email == "<EMAIL>"))
        existing = result.scalars().first()
        if existing:
            logger.info("用户数据已存在，跳过创建")
            result = await self.db.execute(select(User))
            return result.scalars().all()
        
        # 创建角色名称到角色对象的映射
        role_map = {role.name: role for role in roles}
        
        users = []
        for user_data in users_data:
            # 检查用户是否已存在
            email = user_data["email"]
            result = await self.db.execute(select(User).where(User.email == email))
            existing_user = result.scalars().first()
            user_data["hashed_password"] = get_password_hash(user_data["password"])
            if existing_user:
                logger.info(f"用户 {email} 已存在，跳过创建")
                users.append(existing_user)
                continue
            
            # 创建新用户
            role_names = user_data.pop("role_names", [])
            user = await UserRepository(db=self.db).create(user_data)
            
            # 设置用户角色关系
            for role_name in role_names:
                if role_name in role_map:
                    stmt = insert(user_role).values(user_id=user.id, role_id=role_map[role_name].id)
                    await self.db.execute(stmt)
            
            logger.info(f"成功创建用户: {email}")
            users.append(user)
        
        await self.db.commit()
        logger.info(f"用户创建过程完成，共有 {len(users)} 个用户")
        return users
    
    async def bulk_create_subscription_plans(self, plans_data: List[Dict[str, Any]]) -> List[SubscriptionPlan]:
        """批量创建订阅计划"""
        logger.info(f"批量创建 {len(plans_data)} 个订阅计划...")
        
        # 检查是否已存在
        result = await self.db.execute(select(SubscriptionPlan).where(SubscriptionPlan.name == "基础版"))
        existing = result.scalars().first()
        if existing:
            logger.info("订阅计划数据已存在，跳过创建")
            result = await self.db.execute(select(SubscriptionPlan))
            return result.scalars().all()
        
        plans = []
        plan_repo = SubscriptionPlanRepository(db=self.db)
        for idx, plan_data in enumerate(plans_data, 1):
            try:
                plan = await plan_repo.create(plan_data)
                logger.info(f"创建订阅计划 {idx}/{len(plans_data)}: {plan.name} (ID: {plan.id})")
                plans.append(plan)
            except Exception as e:
                logger.error(f"创建订阅计划 '{plan_data['name']}' 失败: {str(e)}")
                raise
        
        await self.db.commit()
        logger.info(f"成功创建了{len(plans)}个订阅计划")
        return plans
    
    async def bulk_create_campaigns(self, campaigns_data: List[Dict[str, Any]]) -> List[Campaign]:
        """批量创建营销活动"""
        logger.info(f"批量创建 {len(campaigns_data)} 个营销活动...")
        
        # 检查是否已存在
        campaign_repo = CampaignRepository(db=self.db)
        campaigns, total = await campaign_repo.get_paginated(page_size=1000)
        if total > 0:
            logger.info(f"营销活动已存在（共{total}个），跳过创建")
            return campaigns
        
        created_campaigns = []
        for campaign_data in campaigns_data:
            campaign = Campaign(**campaign_data)
            self.db.add(campaign)
            await self.db.flush()
            created_campaigns.append(campaign)
            logger.info(f"创建营销活动成功: {campaign.name}, ID: {campaign.id}")
        
        await self.db.commit()
        logger.info(f"成功创建 {len(created_campaigns)} 个营销活动")
        return created_campaigns
    
    async def bulk_create_reward_strategies(self, strategies_data: List[Dict[str, Any]]) -> List[RewardStrategy]:
        """批量创建奖励策略"""
        logger.info(f"批量创建 {len(strategies_data)} 个奖励策略...")
        
        # 检查是否已存在
        result = await self.db.execute(select(RewardStrategy).where(RewardStrategy.name == "邀请人奖励"))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励策略数据已存在，跳过创建")
            result = await self.db.execute(select(RewardStrategy))
            return result.scalars().all()
        
        reward_strategy_repo = RewardStrategyRepository(db=self.db)
        created_strategies = []
        
        for idx, strategy_data in enumerate(strategies_data, 1):
            try:
                strategy = await reward_strategy_repo.create(strategy_data)
                logger.info(f"创建奖励策略 {idx}/{len(strategies_data)}: {strategy.name} (ID: {strategy.id})")
                created_strategies.append(strategy)
            except Exception as e:
                logger.error(f"创建奖励策略 '{strategy_data['name']}' 失败: {str(e)}")
                raise
        
        await self.db.commit()
        logger.info(f"成功创建了{len(created_strategies)}个奖励策略")
        return created_strategies
    
    async def bulk_create_invitations(self, invitations_data: List[Dict[str, Any]]) -> List[Invitation]:
        """批量创建邀请记录"""
        logger.info(f"批量创建 {len(invitations_data)} 个邀请记录...")
        
        # 检查是否已存在
        result = await self.db.execute(select(Invitation).where(Invitation.code == "INV123456"))
        existing = result.scalars().first()
        if existing:
            logger.info("邀请记录数据已存在，跳过创建")
            result = await self.db.execute(select(Invitation))
            return result.scalars().all()
        
        created_invitations = []
        for idx, invitation_data in enumerate(invitations_data, 1):
            try:
                invitation = Invitation(**invitation_data)
                self.db.add(invitation)
                await self.db.flush()
                logger.info(f"创建邀请记录 {idx}/{len(invitations_data)}: {invitation.code} (ID: {invitation.id})")
                created_invitations.append(invitation)
            except Exception as e:
                logger.error(f"创建邀请记录 #{idx} 失败: {str(e)}")
                raise
        
        await self.db.commit()
        logger.info(f"成功创建了{len(created_invitations)}个邀请记录")
        return created_invitations
    
    async def bulk_create_reward_records(self, records_data: List[Dict[str, Any]]) -> List[RewardRecord]:
        """批量创建奖励记录"""
        logger.info(f"批量创建 {len(records_data)} 个奖励记录...")
        
        # 检查是否已存在
        if records_data:
            result = await self.db.execute(select(RewardRecord).limit(1))
            existing = result.scalars().first()
            if existing:
                logger.info("奖励记录数据已存在，跳过创建")
                result = await self.db.execute(select(RewardRecord))
                return result.scalars().all()
        
        created_records = []
        for idx, record_data in enumerate(records_data, 1):
            try:
                record = RewardRecord(**record_data)
                self.db.add(record)
                await self.db.flush()
                logger.info(f"创建奖励记录 {idx}/{len(records_data)}: {record.reward_description} (ID: {record.id})")
                created_records.append(record)
            except Exception as e:
                logger.error(f"创建奖励记录 #{idx} 失败: {str(e)}")
                raise
        
        await self.db.commit()
        logger.info(f"成功创建了{len(created_records)}个奖励记录")
        return created_records
    
    async def bulk_create_reward_distribution_channels(self, channels_data: List[Dict[str, Any]]) -> List[RewardDistributionChannel]:
        """批量创建奖励分发渠道"""
        logger.info(f"批量创建 {len(channels_data)} 个奖励分发渠道...")
        
        # 检查是否已存在
        result = await self.db.execute(select(RewardDistributionChannel).where(RewardDistributionChannel.name == "钱包分发"))
        existing = result.scalars().first()
        if existing:
            logger.info("奖励分发渠道数据已存在，跳过创建")
            result = await self.db.execute(select(RewardDistributionChannel))
            return result.scalars().all()
        
        created_channels = []
        for idx, channel_data in enumerate(channels_data, 1):
            try:
                channel = RewardDistributionChannel(**channel_data)
                self.db.add(channel)
                await self.db.flush()
                logger.info(f"创建奖励分发渠道 {idx}/{len(channels_data)}: {channel.name} (ID: {channel.id})")
                created_channels.append(channel)
            except Exception as e:
                logger.error(f"创建奖励分发渠道 '{channel_data['name']}' 失败: {str(e)}")
                raise
        
        await self.db.commit()
        logger.info(f"成功创建了{len(created_channels)}个奖励分发渠道")
        return created_channels
    
    async def bulk_create_specs_and_options(self, catid_specs_map: Dict[int, List[Dict[str, Any]]]) -> Dict[int, List]:
        """全局去重创建规格，分类与规格多对多关联"""
        spec_repo = SpecRepository(self.db)
        option_repo = SpecOptionRepository(self.db)
        category_repo = CategoryRepository(self.db)
        category_spec_map = {}
        spec_name_map = {}  # name -> spec对象
        
        for cat_id, cat_specs in catid_specs_map.items():
            if not cat_specs:
                continue
            category = await category_repo.get_by_id(cat_id)
            if not category:
                continue
            created_specs = []
            for idx, spec_data in enumerate(cat_specs):
                spec_name = spec_data["name"]
                # 先查找全局是否已存在该规格
                if spec_name in spec_name_map:
                    spec = spec_name_map[spec_name]
                else:
                    spec = await spec_repo.get_one(name=spec_name)
                    if not spec:
                        # 新建规格
                        spec = await spec_repo.create(SpecCreate(
                            name=spec_name,
                            description=spec_data.get("description") or f"全局规格：{spec_name}",
                            sort_order=idx+1,
                            is_active=True,
                            options=[]
                        ))
                        # 只为新建的规格创建规格值
                        for opt in spec_data["options"]:
                            await option_repo.create(SpecOptionCreate(
                                spec_id=spec.id,
                                value=opt
                            ))
                    spec_name_map[spec_name] = spec
                # 建立多对多关联
                if category not in spec.categories:
                    spec.categories.append(category)
                    self.db.add(spec)
                created_specs.append(spec)
            category_spec_map[cat_id] = created_specs
        await self.db.commit()
        return category_spec_map
    
    async def bulk_create_product_skus(self, products: List, category_spec_map: Dict[int, List]) -> None:
        """为每个产品生成本分类下所有规格组合的SKU"""
        import random
        from itertools import product as iter_product

        sku_repo = ProductSKURepository(self.db)
        option_repo = SpecOptionRepository(self.db)
        
        for prod in products:
            cat_id = prod.category_id
            specs = category_spec_map.get(cat_id, [])
            if not specs:
                continue

            # 检查是否已有SKU
            existing_skus, total = await sku_repo.get_paginated(filters={"product_id": prod.id})
            if total > 0:
                logger.info(f"商品 {prod.name} 已存在SKU，跳过SKU创建")
                continue

            # 获取所有规格的所有option id
            option_lists = []
            for spec in specs:
                options, total = await option_repo.get_paginated(filters={"spec_id": spec.id})
                if total > 0:
                    option_ids = [opt.id for opt in options]
                    option_lists.append(option_ids)
            if not option_lists:
                continue

            skus_created = 0
            for option_comb in iter_product(*option_lists):
                sku_code = f"P{prod.id}-{'-'.join(map(str, option_comb))}"
                image_url = f"https://picsum.photos/400/400?random={prod.id}-{skus_created}"
                
                from svc.apps.products.schemas.sku import ProductSKUCreate
                sku_data = ProductSKUCreate(
                    product_id=prod.id,
                    sku=sku_code,
                    price=int(prod.price + random.randint(-1000, 2000)),
                    stock_quantity=random.randint(10, 100),
                    status="active",
                    sort_order=skus_created,
                    image_url=image_url,
                    spec_option_ids=list(option_comb)
                )
                await sku_repo.create(sku_data.model_dump())
                skus_created += 1

            logger.info(f"成功为商品 {prod.name} 创建 {skus_created} 个SKU")
        await self.db.commit()
    
    async def bulk_create_products_with_albums(
        self, 
        products_data: List[Dict[str, Any]], 
        albums_data: List[Dict[str, Any]],
        images_data: List[Dict[str, Any]]
    ) -> Tuple[List, List, List]:
        """批量创建产品及其关联的图册和图片"""
        logger.info(f"批量创建 {len(products_data)} 个产品及其图册...")
        
        product_repo = self._get_repository(ProductRepository)
        album_repo = self._get_repository(AlbumRepository)
        album_image_repo = self._get_repository(AlbumImageRepository)
        
        # 检查是否已存在产品
        existing_products, total = await product_repo.get_paginated(page_size=1000)
        if total > 0:
            logger.info("产品数据已存在，跳过创建")
            return existing_products, [], []
        
        # 批量创建图册（需要立刻获取ID）
        albums = await album_repo.bulk_create(albums_data, return_objects=True)
        logger.info(f"批量创建了 {len(albums)} 个图册")
        album_id_map = {album.name: album.id for album in albums}
        
        # 更新图片数据中的album_id
        valid_images_data = []
        for img_data in images_data:
            album_name = img_data.pop('album_name', None)
            if album_name and album_name in album_id_map:
                img_data['album_id'] = album_id_map[album_name]
                valid_images_data.append(img_data)
            else:
                logger.warning(f"跳过图片，找不到对应的图册: {album_name}")

        images_data = valid_images_data
        
        # 批量创建图片（需要对象以便设置封面）
        created_images = await album_image_repo.bulk_create(images_data, return_objects=True)
        logger.info(f"批量创建了 {len(created_images)} 个图片")

        # 设置每个图册的封面（首个 is_cover=True 的图片）
        if created_images:
            album_id_to_cover: Dict[int, int] = {}
            for img in created_images:
                if getattr(img, "is_cover", False) and getattr(img, "album_id", None):
                    # 仅首次设置
                    album_id_to_cover.setdefault(img.album_id, img.id)
            if album_id_to_cover:
                id_to_album = {alb.id: alb for alb in albums}
                for alb_id, cover_id in album_id_to_cover.items():
                    album_obj = id_to_album.get(alb_id)
                    if album_obj and getattr(album_obj, "cover_image_id", None) != cover_id:
                        await album_repo.update(album_obj, {"cover_image_id": cover_id})
        
        # 批量创建产品（需要关联album_id，按名称映射，而非顺序）
        for product_data in products_data:
            expected_album_name = f"{product_data['name']}图册"
            album_id = album_id_map.get(expected_album_name)
            if album_id:
                product_data['album_id'] = album_id
            else:
                logger.warning(f"未找到产品图册: {expected_album_name}")
                self._record_error("product_album", expected_album_name, "未匹配到图册")
        
        created_products_count = await product_repo.bulk_create(products_data)
        logger.info(f"批量创建了 {created_products_count} 个产品")
        
        # 返回创建的数据
        products, _ = await product_repo.get_paginated(page_size=1000)
        images, _ = await album_image_repo.get_paginated(page_size=1000)
        
        # 错误摘要
        self._flush_error_summary("products")
        return products, albums, images
    
    async def bulk_create_shops_with_albums(
        self,
        shops_data: List[Dict[str, Any]],
        albums_data: List[Dict[str, Any]],
        images_data: List[Dict[str, Any]]
    ) -> Tuple[List, List, List]:
        """批量创建门店及其关联的图册和图片"""
        logger.info(f"批量创建 {len(shops_data)} 个门店及其图册...")
        
        shop_repo = self._get_repository(ShopRepository)
        album_repo = self._get_repository(AlbumRepository)
        album_image_repo = self._get_repository(AlbumImageRepository)
        
        # 检查是否已存在门店
        existing_shops, total = await shop_repo.get_paginated(page_size=1)
        if total >= len(shops_data):
            logger.info("门店数据已存在，跳过创建")
            return existing_shops, [], []
        
        # 批量创建门店图册，返回创建的对象
        shop_albums = await album_repo.bulk_create(albums_data, return_objects=True)
        logger.info(f"批量创建了 {len(shop_albums)} 个门店图册")
        logger.info(f"门店图册名称: {[album.name for album in shop_albums]}")
        
        # 创建图册名称到图册对象的映射
        album_map = {album.name: album for album in shop_albums}
        
        # 先创建门店（不包含album_id）
        shops_without_album = []
        for shop_data in shops_data:
            shop_copy = shop_data.copy()
            shop_copy.pop('album_id', None)  # 移除album_id字段
            shops_without_album.append(shop_copy)
        
        # 批量创建门店
        created_shops_count = await shop_repo.bulk_create(shops_without_album)
        logger.info(f"批量创建了 {created_shops_count} 个门店")
        
        # 获取创建的门店并更新album_id
        all_shops, _ = await shop_repo.get_paginated(page_size=100)
        for i, shop in enumerate(all_shops):
            expected_album_name = f"{shop.name}图册"
            if expected_album_name in album_map:
                await shop_repo.update(shop, {"album_id": album_map[expected_album_name].id})
                logger.info(f"为门店 {shop.name} 分配图册: {expected_album_name}")
            else:
                logger.warning(f"未找到门店 {shop.name} 对应的图册: {expected_album_name}")
                self._record_error("shop_album", expected_album_name, "未匹配到图册")
        
        # 更新图片数据中的album_id
        for image_data in images_data:
            album_name = image_data.pop('album_name', None)
            if album_name and album_name in album_map:
                image_data['album_id'] = album_map[album_name].id
            else:
                # 如果没有找到对应的图册，跳过这个图片
                logger.warning(f"未找到图册: {album_name}")
                self._record_error("shop_image", str(album_name), "未匹配到图册")
                continue
        
        # 过滤掉没有album_id的图片
        valid_images = [img for img in images_data if img.get('album_id') is not None]
        logger.info(f"有效图片数量: {len(valid_images)} / {len(images_data)}")
        
        if not valid_images:
            logger.warning("没有有效的图片数据，跳过图片创建")
            created_images_count = 0
        else:
            # 批量创建门店图片（返回对象以设置封面）
            created_shop_images = await album_image_repo.bulk_create(valid_images, return_objects=True)
            logger.info(f"批量创建了 {len(created_shop_images)} 个门店图片")

            # 设置门店图册封面
            if created_shop_images:
                album_id_to_cover: Dict[int, int] = {}
                for img in created_shop_images:
                    if getattr(img, "is_cover", False) and getattr(img, "album_id", None):
                        album_id_to_cover.setdefault(img.album_id, img.id)
                if album_id_to_cover:
                    for alb_id, cover_id in album_id_to_cover.items():
                        album_obj = album_map.get(next((name for name, a in album_map.items() if a.id == alb_id), ""))
                        # 如果通过名称没拿到，用ID回退
                        if not album_obj:
                            album_obj = next((a for a in album_map.values() if a.id == alb_id), None)
                        if album_obj and getattr(album_obj, "cover_image_id", None) != cover_id:
                            await album_repo.update(album_obj, {"cover_image_id": cover_id})
        
        # 返回创建的数据
        all_shops, _ = await shop_repo.get_paginated(page_size=100)
        all_images, _ = await album_image_repo.get_paginated(page_size=100)
        
        # 错误摘要
        self._flush_error_summary("shops")
        return all_shops, shop_albums, all_images

    async def prepare_products_data(
        self,
        categories: List,
        product_image_urls: Optional[List[Dict[str, Any]]] = None,
    ) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """准备产品、图册和图片数据

        - 如果提供了 product_image_urls，则每个分类下产品数量 = 该分类对应 URL 数量
        - 否则回退为每类 5 个示例产品
        """
        products_data = []
        albums_data = []
        images_data = []

        # 构建分类名 -> 图片URL列表 的映射（可选）
        urls_by_category_name: Dict[str, List[str]] = {}
        if product_image_urls:
            try:
                for entry in product_image_urls:
                    name = str(entry.get("name", "")).strip()
                    urls = entry.get("urls") or []
                    if name and isinstance(urls, list) and urls:
                        urls_by_category_name[name] = [str(u) for u in urls if isinstance(u, str) and u]
            except Exception:
                # 宽容处理，出错则回退默认
                urls_by_category_name = {}

        # PNG透明底图片库（回退用）
        fallback_png_urls = [
            "http://res.yqbaijiu.com/20250626/dc43232763b24acf8012fdbe392277d0.jpg",
            "http://res.yqbaijiu.com/20250626/80953d93b119496a9b70684ed12c9732.jpg",
            "http://res.yqbaijiu.com/20250626/693fa52621204022b05b7e92baeba619.jpg",
            "http://res.yqbaijiu.com/20250626/dce92a01ccf64421939b9c7ae8572e69.jpg",
        ]

        for idx, cat in enumerate(categories):
            category_name = cat.name
            category_urls = urls_by_category_name.get(category_name, [])
            num_products = len(category_urls) if category_urls else 5

            for i in range(num_products):
                product_name = f"{category_name}{i+1}"
                sku = f"{category_name[:2].upper()}{i+1:02d}"

                # 准备图册数据
                album_data = {
                    "name": f"{product_name}图册",
                    "description": f"{product_name}的主图册",
                    "tags": [category_name],
                    "status": "active",
                    "sort_order": idx*5+i+1,
                    "meta_data": {"scene": "product"}
                }
                albums_data.append(album_data)

                # 准备图片数据：若提供了按分类的URL，则每个产品用一张对应URL作为封面
                if category_urls:
                    url = category_urls[i]
                    image_data = {
                        "album_name": f"{product_name}图册",
                        "url": url,
                        "file_name": url.split("/")[-1],
                        "file_size": 0,
                        "width": 800,
                        "height": 800,
                        "mime_type": "image/png" if url.lower().endswith(".png") else "image/jpeg",
                        "is_cover": True,
                        "sort_order": 1,
                        "status": "active",
                        "meta_data": {"scene": "product"}
                    }
                    images_data.append(image_data)
                else:
                    # 回退：每个产品放1张示例图
                    url = random.choice(fallback_png_urls)
                    image_data = {
                        "album_name": f"{product_name}图册",
                        "url": url,
                        "file_name": url.split("/")[-1],
                        "file_size": 0,
                        "width": 800,
                        "height": 800,
                        "mime_type": "image/jpeg",
                        "is_cover": True,
                        "sort_order": 1,
                        "status": "active",
                        "meta_data": {"scene": "product"}
                    }
                    images_data.append(image_data)

                # 准备产品数据
                product_data = {
                    "name": product_name,
                    "description": f"{category_name}高品质防护，适合多场景使用。",
                    "short_description": f"优质{category_name}，安全舒适。",
                    "sku": sku,
                    "barcode": f"{sku}BAR{i+1:03d}",
                    "category_id": cat.id,
                    "price": 199.0 + idx*20 + i*5,
                    "cost_price": 120.0 + idx*10,
                    "market_price": 299.0 + idx*30,
                    "currency": "CNY",
                    "is_featured": (i == 0),
                    "is_digital": False,
                    "track_inventory": True,
                    "stock_quantity": 50 + i*10,
                    "min_stock_level": 5,
                    "max_stock_level": 200,
                    "weight": 0.45 + idx*0.05,
                    "dimensions": {"length": 25+idx, "width": 20+idx, "height": 15+idx},
                    "attributes": {"类型": category_name},
                    "seo_title": f"{product_name} - 专业{category_name}商城",
                    "seo_description": f"{category_name}，安全舒适，适合骑行、施工等多场景。",
                    "seo_keywords": f"{category_name},安全,防护",
                    "rich_description": f'<p>{category_name}，高品质防护，适合多场景使用。</p>'
                }
                products_data.append(product_data)

        return products_data, albums_data, images_data

    async def prepare_shops_data(self) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """准备门店、图册和图片数据"""
        shops_data = []
        albums_data = []
        images_data = []

        # 门店数据
        shop_names = ["北京朝阳店", "上海浦东店", "广州天河店", "深圳南山店"]
        for i, name in enumerate(shop_names):
            shop_data = {
                "name": name,
                "description": f"{name} - 专业安全装备销售门店",
                "address_line1": f"{name}详细地址",
                "address_line2": None,
                "city": f"{name.split('店')[0]}",
                "state_province": "中国",
                "postal_code": f"1000{i+1:03d}",
                "country": "CN",
                "phone_number": f"400-{1000+i:04d}",
                "email": f"shop{i+1}@example.com",
                "website": f"https://shop{i+1}.example.com",
                "opening_hours": {"Mon-Sun": "09:00-18:00"},
                "status": ShopStatus.OPEN,
                "is_franchise": False,
                "latitude": str(30.0 + i * 0.1),
                "longitude": str(120.0 + i * 0.1),
                "extra_info": {"floor_area_sqm": 100 + i * 10, "has_parking": bool(i % 2)},
                "album_id": None  # 稍后设置
            }
            shops_data.append(shop_data)

            # 对应的图册数据
            album_data = {
                "name": f"{name}图册",
                "description": f"{name}门店展示图册",
                "tags": ["shop", "store"],
                "status": "active",
                "sort_order": i + 1,
                "meta_data": {"type": "shop_gallery"}
            }
            albums_data.append(album_data)

            # 对应的图片数据
            image_data = {
                "album_name": f"{name}图册",
                "url": f"https://img.freepik.com/free-photo/modern-store-front_23-2149191363.jpg",
                "file_name": f"shop_{i+1}.jpg",
                "file_size": 0,
                "width": 800,
                "height": 600,
                "mime_type": "image/jpeg",
                "is_cover": True,
                "sort_order": 1,
                "status": "active",
                "meta_data": {"scene": "shop"}
            }
            images_data.append(image_data)

        return shops_data, albums_data, images_data

    async def bulk_create_banner_albums(self, banner_data: List[Dict[str, Any]]) -> Tuple[List, List]:
        """批量创建banner轮播图图册和图片
        
        Args:
            banner_data: banner图片数据列表
            
        Returns:
            Tuple[List, List]: (创建的图册列表, 创建的图片列表)
        """
        try:
            album_repo = self._get_repository(AlbumRepository)
            album_image_repo = self._get_repository(AlbumImageRepository)
            
            logger.info(f"开始创建banner图册，banner数据数量: {len(banner_data)}")
            
            # 检查是否已存在banner图册
            existing_albums, total = await album_repo.get_paginated(
                filters={"status": "active"},
                page_num=1,
                page_size=10
            )
            
            if total > 0:
                logger.info(f"Banner图册已存在（共{total}个），跳过创建")
                # 获取现有图册中的图片
                existing_images = []
                for album in existing_albums:
                    if album.images:
                        existing_images.extend(album.images)
                logger.info(f"现有banner图片数量: {len(existing_images)}")
                return existing_albums, existing_images
            
            # 准备图册数据
            album_data = {
                "name": "首页轮播图",
                "description": "首页banner轮播图展示",
                "tags": ["banner", "homepage"],
                "status": "active",
                "sort_order": 1,
                "meta_data": {"type": "banner", "auto_play": True}
            }
            
            logger.info(f"准备创建图册数据: {album_data}")
            
            # 创建图册
            albums = await album_repo.bulk_create([album_data], return_objects=True)
            if not albums:
                logger.error("创建banner图册失败")
                return [], []
            
            album = albums[0]
            logger.info(f"创建banner图册成功: {album.name}, ID: {album.id}")
            
            # 准备图片数据
            images_data = []
            for i, banner in enumerate(banner_data):
                image_data = {
                    "album_id": album.id,
                    "url": banner["image_url"],
                    "file_name": banner["image_url"].split("/")[-1],
                    "file_size": 0,
                    "width": 1200,
                    "height": 400,
                    "mime_type": "image/jpeg",
                    "is_cover": False,
                    "sort_order": banner["sort_order"],
                    "status": "active",
                    "meta_data": {
                        "title": banner["title"],
                        "description": banner["description"],
                        "link_url": banner["link_url"],
                        "scene": "banner"
                    }
                }
                images_data.append(image_data)
                logger.info(f"准备图片 {i+1}: {banner['title']}")
            
            logger.info(f"准备创建 {len(images_data)} 张banner图片")
            
            # 创建图片
            created_images = await album_image_repo.bulk_create(images_data, return_objects=True)
            logger.info(f"创建了 {len(created_images)} 张banner图片")
            
            # 设置第一张图片为封面
            if created_images:
                first_image = created_images[0]
                await album_repo.update(album, {"cover_image_id": first_image.id})
                logger.info(f"设置banner图册封面: {first_image.file_name}")
            
            # 提交事务
            await self.db.commit()
            logger.info("Banner图册创建完成，事务已提交")
            
            return albums, created_images
            
        except Exception as e:
            logger.error(f"创建banner图册时发生错误: {str(e)}", exc_info=True)
            await self.db.rollback()
            raise
