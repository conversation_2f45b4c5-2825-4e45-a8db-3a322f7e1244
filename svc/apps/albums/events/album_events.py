"""
图册事件处理器
"""
import logging
from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event

logger = logging.getLogger(__name__)

class AlbumEventHandler:
    """图册事件处理器类"""

    @staticmethod
    @local_handler.register(event_name="album:created")
    async def handle_album_created(event: Event):
        try:
            logger.info(f"处理图册创建事件: {event}")
            album_data = event[1]
            logger.info(f"图册已创建: ID={album_data.get('id')}, 名称={album_data.get('name')}")
            # 可扩展：发送通知、更新缓存等
        except Exception as e:
            logger.error(f"处理图册创建事件失败: {str(e)}", exc_info=True)

    @staticmethod
    @local_handler.register(event_name="album:updated")
    async def handle_album_updated(event: Event):
        try:
            logger.info(f"处理图册更新事件: {event}")
            album_data = event[1]
            logger.info(f"图册已更新: ID={album_data.get('id')}, 名称={album_data.get('name')}")
            # 可扩展：清除缓存、发送通知等
        except Exception as e:
            logger.error(f"处理图册更新事件失败: {str(e)}", exc_info=True)

    @staticmethod
    @local_handler.register(event_name="album:deleted")
    async def handle_album_deleted(event: Event):
        try:
            logger.info(f"处理图册删除事件: {event}")
            album_data = event[1]
            logger.info(f"图册已删除: ID={album_data.get('id')}")
            # 可扩展：清除缓存、发送通知等
        except Exception as e:
            logger.error(f"处理图册删除事件失败: {str(e)}", exc_info=True) 