"""
图册模型，定义图册的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Integer, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from typing import List, Optional, Dict

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from .album_image import AlbumImage

# 图册状态枚举
class AlbumStatus(str, enum.Enum):
    ACTIVE = "active"      # 启用
    INACTIVE = "inactive"  # 禁用
    DELETED = "deleted"    # 已删除

class Album(Base, ResourceMixin):
    """
    图册模型，定义图册的基本属性和规则
    """
    __tablename__ = "albums"
    __resource_type__ = "album"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="图册名称")
    description = Column(Text, nullable=True, comment="图册描述")
    tags = Column(DatabaseCompatibleJSON, default=[], nullable=False, comment="标签（分组/分类/标记）")
    cover_image_id = Column(BigInteger, nullable=True, comment="封面图片ID")
    status = Column(String(20), nullable=False, default=AlbumStatus.ACTIVE, comment="图册状态")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    meta_data = Column(DatabaseCompatibleJSON, default={}, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")

    # 关系
    images = relationship(
        "AlbumImage",
        back_populates="album",
        cascade="all, delete-orphan",
        lazy="selectin",
        foreign_keys=[AlbumImage.album_id]
    )
    cover_image = relationship(
        "AlbumImage",
        primaryjoin="foreign(Album.cover_image_id) == AlbumImage.id",
        uselist=False,
        post_update=True
    )

    def is_active(self) -> bool:
        """判断图册是否启用"""
        return self.status == AlbumStatus.ACTIVE

    def to_dict(self) -> Dict:
        """将图册对象转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "tags": self.tags,
            "cover_image_id": self.cover_image_id,
            "status": self.status,
            "sort_order": self.sort_order,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        } 