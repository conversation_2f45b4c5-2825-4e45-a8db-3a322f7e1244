"""
图册数据访问层。
负责图册模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.albums.models.album import Album, AlbumStatus
from svc.apps.albums.schemas.album import AlbumCreate, AlbumUpdate
from svc.core.repositories import BaseRepository


class AlbumRepository(BaseRepository[Album, AlbumCreate, AlbumUpdate]):
    """图册仓库类，提供图册数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化图册仓库"""
        super().__init__(db, Album)

    def _get_default_load_options(self) -> List[Any]:
        """获取图册模型的默认关系加载选项
        
        默认加载：
        - images: 图册图片（selectin关系）
        - cover_image: 封面图片（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Album.images),
            selectinload(Album.cover_image)
        ]

    async def get_albums(
        self,
        page_num: int = 1,
        page_size: int = 50,
        load_options: Optional[List[Any]] = None,
        **kwargs
    ) -> Tuple[List[Album], int]:
        """获取图册列表（分页）
        
        标准的图册查询方法，支持各种过滤条件
        
        Args:
            page_num: 页码
            page_size: 每页大小
            load_options: 关系加载选项
            **kwargs: 过滤条件
            
        Returns:
            Tuple[List[Album], int]: 图册列表和总数
        """
        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            load_options=load_options,
            **kwargs
        )

    async def get_album_with_full_relations(self, album_id: int) -> Optional[Album]:
        """获取图册详情并加载所有关系数据"""
        load_options = [
            selectinload(Album.images),
            selectinload(Album.cover_image)
        ]
        return await self.get_by_id(album_id, load_options=load_options)