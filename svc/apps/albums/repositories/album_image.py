"""
图册图片数据访问层。
负责图册图片模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, asc, func, or_

from svc.core.repositories import BaseRepository
from svc.apps.albums.models.album_image import AlbumImage, AlbumImageStatus
from svc.apps.albums.schemas.album_image import AlbumImageCreate, AlbumImageUpdate

class AlbumImageRepository(BaseRepository[AlbumImage, AlbumImageCreate, AlbumImageUpdate]):
    """图册图片仓库类，提供图片数据访问方法"""
    def __init__(self, db: AsyncSession):
        super().__init__(db, AlbumImage)

