from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.albums.dependencies import get_album_service
from svc.apps.albums.schemas.album import (AlbumCreate, AlbumListResponse,
                                           AlbumResponse, AlbumUpdate,
                                           GetAlbumsParams,AlbumBatchUpdateRequest,
                                          AlbumBatchDeleteRequest,
                                          AlbumBatchUpdateResponse,
                                          AlbumBatchDeleteResponse,
                                          BannerListResponse) 
from svc.apps.albums.services.album import AlbumService
from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.core.exceptions.route_error_handler import (ALBUM_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams


router = APIRouter(
    tags=["图册管理"]
)

@router.get("/admin", response_model=Result[AlbumListResponse])
@handle_route_errors()
async def admin_list_albums(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="图册状态"),
    tags: Optional[str] = Query(None, description="标签（逗号分隔）"),
    search_term: Optional[str] = Query(None, description="搜索关键词"),
    order_by: Optional[str] = Query("sort_order", description="排序字段"),
    order_direction: Optional[str] = Query("asc", description="排序方向: asc/desc"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:read"))
) -> Result[AlbumListResponse]:
    """获取图册列表 (管理端)"""
    params_obj = GetAlbumsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        tags=tags.split(",") if tags else None,
        search_term=search_term,
        order_by=order_by,
        order_direction=order_direction
    )
    return await album_service.get_albums(params=params_obj)

@router.post("/admin", response_model=Result[Dict[str, Any]], status_code=status.HTTP_201_CREATED)
@handle_route_errors()
async def admin_create_album(
    album_data: AlbumCreate,
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:create")),
) -> Result:
    """创建图册 (管理端)"""
    return await album_service.create_album(params=album_data)

# 批量操作路由 - 必须放在单个操作路由之前
@router.put("/admin/batch", response_model=Result[AlbumBatchUpdateResponse])
@handle_route_errors()
async def admin_batch_update_albums(
    request: AlbumBatchUpdateRequest,
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:update")),
) -> Result[AlbumBatchUpdateResponse]:
    """批量更新图册 (管理端)
    
    批量更新多个图册的状态、排序等信息
    """
    return await album_service.batch_update_albums(request=request)

@router.delete("/admin/batch", response_model=Result[AlbumBatchDeleteResponse])
@handle_route_errors()
async def admin_batch_delete_albums(
    request: AlbumBatchDeleteRequest,
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("album:delete")),
) -> Result[AlbumBatchDeleteResponse]:
    """批量删除图册 (管理端)
    
    批量删除多个图册
    """
    return await album_service.batch_delete_albums(request=request)

# 单个操作路由 - 放在批量操作路由之后
@router.get("/admin/{album_id}", response_model=Result[AlbumResponse])
@handle_route_errors()
async def admin_get_album_details(
    album_id: int = Path(..., description="图册ID"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "read")),
) -> Result[AlbumResponse]:
    """获取图册详情 (管理端)"""
    return await album_service.get_album(album_id=album_id)

@router.put("/admin/{album_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_update_album(
    album_in: AlbumUpdate,
    album_id: int = Path(..., description="图册ID"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "update")),
) -> Result:
    """更新图册 (管理端)"""
    return await album_service.update_album(album_id=album_id, params=album_in)

@router.delete("/admin/{album_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors()
async def admin_delete_album(
    album_id: int = Path(..., description="图册ID"),
    soft_delete: bool = Query(True, description="是否软删除", alias="softDelete"),
    album_service: AlbumService = Depends(get_album_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("album", "delete")),
) -> Result:
    """删除图册 (管理端)"""
    return await album_service.delete_album(album_id=album_id, soft_delete=soft_delete)


# === 用户端接口 ===

@router.get("/banner", response_model=Result[BannerListResponse])
@handle_route_errors()
async def get_home_banners(
    limit: int = Query(10, ge=1, le=20, description="返回轮播图数量限制"),
    album_service: AlbumService = Depends(get_album_service)
) -> Result[BannerListResponse]:
    """获取首页轮播图 (用户端)
    
    获取标记为banner标签的图册中的图片作为首页轮播图
    """
    return await album_service.get_banners(limit=limit)