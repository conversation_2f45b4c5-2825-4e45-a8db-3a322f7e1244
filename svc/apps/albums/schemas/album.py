from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.models.base import CamelCaseModel
from svc.apps.albums.schemas.album_image import AlbumImageResponse

from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest,BatchUpdateResponse,BatchDeleteResponse

# 分页基类（可复用产品模块的PaginatedResponse）
from svc.core.schemas.base import PaginatedResponse

class AlbumBase(CamelCaseModel):
    """图册基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI GT-AIR II 产品图册",
                "description": "SHOEI GT-AIR II 全盔摩托车头盔官方产品图片集",
                "tags": ["motorcycle", "helmet", "shoei", "product"],
                "cover_image_id": 1,
                "status": "active",
                "sort_order": 1,
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet"
                }
            }
        }
    )
    name: str = Field(..., max_length=100, description="相册名称")
    description: Optional[str] = Field(None, description="相册描述")
    tags: List[str] = Field(default=[], description="标签（分组/分类/标记）")
    cover_image_id: Optional[int] = Field(default=None, description="封面图片ID")
    status: str = Field(default="active", description="图册状态")
    sort_order: int = Field(default=0, description="排序顺序")
    meta_data: Dict[str, Any] = Field(default={}, description="元数据")
    images: List[AlbumImageResponse] = Field(default=[], description="相册图片列表")

class AlbumCreate(AlbumBase):
    """图册创建模型"""
    pass

class AlbumUpdate(CamelCaseModel):
    """图册更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI GT-AIR II 产品图册（升级版）",
                "description": "SHOEI GT-AIR II 全盔摩托车头盔官方产品图片集，包含更多细节展示",
                "tags": ["motorcycle", "helmet", "shoei", "product", "premium"],
                "cover_image_id": 2,
                "status": "active",
                "sort_order": 2,
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet",
                    "image_count": 8
                }
            }
        }
    )
    name: Optional[str] = Field(None, max_length=100, description="相册名称")
    description: Optional[str] = Field(None, description="相册描述")
    tags: Optional[List[str]] = Field(default=None, description="标签")
    cover_image_id: Optional[int] = Field(default=None, description="封面图片ID")
    status: Optional[str] = Field(default=None, description="图册状态")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class AlbumResponse(AlbumBase):
    """图册响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "SHOEI GT-AIR II 产品图册",
                "description": "SHOEI GT-AIR II 全盔摩托车头盔官方产品图片集",
                "tags": ["motorcycle", "helmet", "shoei", "product"],
                "cover_image_id": 1,
                "status": "active",
                "sort_order": 1,
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet"
                },
                "images": [
                    {
                        "id": 1,
                        "url": "https://cdn.example.com/albums/shoei-gtair2-1.jpg",
                        "alt_text": "SHOEI GT-AIR II 正面图",
                        "sort_order": 1
                    },
                    {
                        "id": 2,
                        "url": "https://cdn.example.com/albums/shoei-gtair2-2.jpg",
                        "alt_text": "SHOEI GT-AIR II 侧面图",
                        "sort_order": 2
                    }
                ],
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00",
                "deleted_at": None
            }
        }
    )
    id: int = Field(..., description="相册ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class AlbumListResponse(PaginatedResponse[AlbumResponse]):
    """图册列表响应模型"""
    pass

class GetAlbumsParams(BaseModel):
    """获取图册列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="图册状态")
    tags: Optional[List[str]] = Field(default=None, description="标签筛选")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc")

class UserAlbumResponse(BaseModel):
    """用户端精简图册响应模型"""
    id: int
    name: str
    images: List[str] = Field([], description="相册图片列表")

class AlbumBatchUpdate(AlbumUpdate):
    """图册批量更新模型
    
    定义允许批量更新的字段，继承自AlbumUpdate
    """
    pass

class AlbumBatchUpdateRequest(BatchUpdateRequest[AlbumBatchUpdate]):
    """图册批量更新请求模型
    
    包含要更新的图册ID列表和更新数据
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "sort_order": 10
                }
            }
        }
    )

class AlbumBatchUpdateResponse(BatchUpdateResponse):
    """图册批量更新响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )

class DeleteAlbumParams(BaseModel):
    """删除图册参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "album_id": 1,
                "soft_delete": True
            }
        }
    )
    
    album_id: int = Field(..., description="图册ID")
    soft_delete: bool = Field(default=True, description="是否软删除")

class AlbumBatchDeleteRequest(BatchDeleteRequest):
    """图册批量删除请求模型
    
    包含要删除的图册ID列表
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class AlbumBatchDeleteResponse(BatchDeleteResponse):
    """图册批量删除响应模型"""
    pass


# === Banner用户端接口Schema ===

class BannerResponse(CamelCaseModel):
    """首页轮播图响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "title": "新品上市",
                "image_url": "https://oss.example.com/banner1.jpg",
                "link_url": "https://example.com/product/1",
                "sort_order": 1,
                "description": "新品上市活动"
            }
        }
    )
    id: int = Field(..., description="轮播图ID")
    title: str = Field(..., description="轮播图标题")
    image_url: str = Field(..., description="轮播图片URL")
    link_url: Optional[str] = Field(None, description="跳转链接")
    sort_order: int = Field(..., description="排序顺序")
    description: Optional[str] = Field(None, description="轮播图描述")

class BannerListResponse(CamelCaseModel):
    """首页轮播图列表响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "banners": [
                    {
                        "id": 1,
                        "title": "新品上市",
                        "image_url": "https://oss.example.com/banner1.jpg",
                        "link_url": "https://example.com/product/1",
                        "sort_order": 1,
                        "description": "新品上市活动"
                    }
                ],
                "total": 1
            }
        }
    )
    banners: List[BannerResponse] = Field(..., description="轮播图列表")
    total: int = Field(..., description="轮播图总数")

