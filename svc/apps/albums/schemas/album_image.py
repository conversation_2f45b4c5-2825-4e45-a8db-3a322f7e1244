from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest, BatchDeleteResponse, BatchUpdateResponse

class AlbumImageBase(CamelCaseModel):
    """图册图片基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "album_id": 1,
                "url": "https://cdn.example.com/albums/shoei-gtair2-1.jpg",
                "file_name": "shoei-gtair2-front.jpg",
                "file_size": 245760,
                "width": 1200,
                "height": 800,
                "mime_type": "image/jpeg",
                "is_cover": True,
                "sort_order": 1,
                "status": "active",
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet",
                    "image_type": "front_view"
                }
            }
        }
    )
    album_id: int = Field(..., description="所属图册ID")
    url: str = Field(..., description="云存储图片URL")
    file_name: Optional[str] = Field(default=None, description="文件名")
    file_size: Optional[int] = Field(default=None, description="文件大小（字节）")
    width: Optional[int] = Field(default=None, description="图片宽度")
    height: Optional[int] = Field(default=None, description="图片高度")
    mime_type: Optional[str] = Field(default=None, description="图片类型")
    is_cover: bool = Field(default=False, description="是否为封面")
    sort_order: int = Field(default=0, description="排序顺序")
    status: str = Field(default="active", description="图片状态")
    meta_data: Dict[str, Any] = Field(default={}, description="元数据")

class AlbumImageCreate(AlbumImageBase):
    """图册图片创建模型"""
    pass

class AlbumImageUpdate(CamelCaseModel):
    """图册图片更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "file_name": "shoei-gtair2-side-view.jpg",
                "is_cover": False,
                "sort_order": 2,
                "status": "active",
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet",
                    "image_type": "side_view"
                }
            }
        }
    )
    file_name: Optional[str] = Field(default=None, description="文件名")
    is_cover: Optional[bool] = Field(default=None, description="是否为封面")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")
    status: Optional[str] = Field(default=None, description="图片状态")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class AlbumImageResponse(AlbumImageBase):
    """图册图片响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "album_id": 1,
                "url": "https://cdn.example.com/albums/shoei-gtair2-1.jpg",
                "file_name": "shoei-gtair2-front.jpg",
                "file_size": 245760,
                "width": 1200,
                "height": 800,
                "mime_type": "image/jpeg",
                "is_cover": True,
                "sort_order": 1,
                "status": "active",
                "meta_data": {
                    "scene": "product",
                    "brand": "SHOEI",
                    "product_type": "motorcycle_helmet",
                    "image_type": "front_view"
                },
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00",
                "deleted_at": None
            }
        }
    )
    id: int = Field(description="图片ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class AlbumImageListResponse(PaginatedResponse[AlbumImageResponse]):
    """图册图片列表响应模型"""
    pass

class GetAlbumImagesParams(BaseModel):
    """获取图册图片列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    album_id: Optional[int] = Field(default=None, description="图册ID")
    status: Optional[str] = Field(default=None, description="图片状态")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc") 

class DeleteAlbumImageParams(BaseModel):
    """删除图册图片参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "image_id": 1,
                "soft_delete": True
            }
        }
    )
    
    image_id: int = Field(..., description="图片ID")
    soft_delete: bool = Field(default=True, description="是否软删除")


# === 批量操作Schema ===

class AlbumImageBatchUpdate(CamelCaseModel):
    """相册图片批量更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    is_cover: Optional[bool] = Field(None, description="是否为封面")
    sort_order: Optional[int] = Field(None, description="排序顺序")
    status: Optional[str] = Field(None, description="图片状态")

class AlbumImageBatchUpdateRequest(BatchUpdateRequest[AlbumImageBatchUpdate]):
    """相册图片批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {"status": "active"}
            }
        }
    )

class AlbumImageBatchUpdateResponse(BatchUpdateResponse):
    """相册图片批量更新响应模型"""
    pass

class AlbumImageBatchDeleteRequest(BatchDeleteRequest):
    """相册图片批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class AlbumImageBatchDeleteResponse(BatchDeleteResponse):
    """相册图片批量删除响应模型"""
    pass 