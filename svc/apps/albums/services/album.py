from typing import List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.albums.models.album import Album, AlbumStatus
from svc.apps.albums.repositories.album import AlbumRepository
from svc.apps.albums.schemas.album import (AlbumBatchDeleteRequest,
                                           AlbumBatchDeleteResponse,
                                           AlbumBatchUpdateRequest,
                                           AlbumBatchUpdateResponse,
                                           AlbumCreate, AlbumListResponse,
                                           AlbumResponse, AlbumUpdate,
                                           BannerListResponse, BannerResponse,
                                           GetAlbumsParams)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

# 缓存配置
CACHE_TTL = 3600

class AlbumService(BaseService, BatchOperationMixin):
    """图册服务类，提供图册的创建、查询和管理功能"""

    def __init__(self, album_repo: AlbumRepository, redis: Optional[Redis] = None, **kwargs):
        config = ServiceConfig(
            resource_type="album",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(album_repo, config, redis, **kwargs)
        self.album_repo = album_repo

    async def get_resource_by_id(self, album_id: int) -> Optional[Album]:
        return await self.album_repo.get_by_id(album_id)

    async def get_album(self, album_id: int) -> Result[AlbumResponse]:
        try:
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            response = AlbumResponse.model_validate(album.to_dict())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取图册失败: {str(e)}"
            )

    async def get_albums(self, params: GetAlbumsParams) -> Result[AlbumListResponse]:
        try:
            page_num = params.page_num
            page_size = params.page_size

            # 构建过滤条件
            filters = {}
            if params.status:
                filters["status"] = params.status
            if params.tags:
                # 处理标签过滤
                for tag in params.tags:
                    filters[f"tags__contains"] = tag
            if params.search_term:
                # 搜索功能通过 repository 的 search 方法处理
                albums = await self.album_repo.search(
                    term=params.search_term,
                    fields=["name", "description"],
                    limit=page_size
                )
                total = len(albums)
            else:
                # 使用标准的 get_albums 方法
                albums, total = await self.album_repo.get_albums(
                    page_num=page_num,
                    page_size=page_size,
                    order_by=params.order_by,
                    order_direction=params.order_direction,
                    **filters
                )
            responses = [AlbumResponse.model_validate(a.to_dict()) for a in albums]
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            paginated = AlbumListResponse(
                items=responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取图册列表失败: {str(e)}"
            )

    async def create_album(self, params: AlbumCreate) -> Result[AlbumResponse]:
        try:
            album = await self.album_repo.create(params)
            response = AlbumResponse.model_validate(album.to_dict())
            dispatch("album:created", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建图册失败: {str(e)}"
            )

    async def update_album(self, album_id: int, params: AlbumUpdate) -> Result[AlbumResponse]:
        try:
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            update_data = params.model_dump(exclude_unset=True)
            album = await self.album_repo.update(album, data=update_data)
            response = AlbumResponse.model_validate(album.to_dict())
            dispatch("album:updated", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新图册失败: {str(e)}"
            )

    async def delete_album(self, album_id: int, soft_delete: bool = True) -> Result:
        """删除图册
        
        Args:
            album_id: 图册ID
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result: 删除结果
        """
        try:
            self.logger.info(f"删除图册: id={album_id}, soft_delete={soft_delete}")
            album = await self.get_resource_by_id(album_id)
            if not album:
                return self.resource_not_found_result(album_id)
            
            if soft_delete:
                # 软删除：更新状态为DELETED
                update_data = {"status": "deleted"}
                await self.album_repo.update(album, data=update_data)
                self.logger.info(f"图册软删除成功: id={album_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.album_repo.delete(album)
                self.logger.info(f"图册硬删除成功: id={album_id}")
            
            dispatch("album:deleted", payload={"id": album_id, "soft_delete": soft_delete})
            return self.create_success_result({"id": album_id, "soft_delete": soft_delete})
        except Exception as e:
            self.logger.error(f"删除图册失败: id={album_id}, soft_delete={soft_delete}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"删除图册失败: {str(e)}"
            )
            
    async def batch_update_albums(self, request: AlbumBatchUpdateRequest) -> Result[AlbumBatchUpdateResponse]:
        """批量更新图册
        
        Args:
            request: 批量更新请求
            
        Returns:
            Result: 批量更新结果
        """
        # 提取更新数据
        resource_ids = request.resource_ids
        update_data = request.update_data.model_dump(exclude_unset=True)
        
        # 调用批量更新方法，直接返回结果，避免双重包装
        return await self.batch_update_resources(
            resource_ids=resource_ids,
            update_data=update_data,
            repository=self.album_repo,
            resource_type=self.resource_type,
            cache_key_generator=lambda id: f"album:{id}"
        )
    
    async def batch_delete_albums(self, request: AlbumBatchDeleteRequest) -> Result[AlbumBatchDeleteResponse]:
        """批量删除图册
        
        Args:
            request: 批量删除请求
            
        Returns:
            Result: 批量删除结果
        """
        # 提取资源ID
        resource_ids = request.resource_ids
        
        # 调用批量删除方法，直接返回结果，避免双重包装
        return await self.batch_delete_resources(
            resource_ids=resource_ids,
            soft_delete=request.soft_delete,
            repository=self.album_repo,
            resource_type=self.resource_type,
            cache_key_generator=lambda id: f"album:{id}"
        )

    async def get_banners(self, limit: int = 10) -> Result[BannerListResponse]:
        """获取首页轮播图
        
        Args:
            limit: 返回的轮播图数量限制，默认10张
            
        Returns:
            Result: 轮播图列表结果
        """
        try:
            # 缓存键
            cache_key = f"banners:home:{limit}"
            
            # 尝试从缓存获取
            cached_result = await self.get_cached_resource(cache_key, lambda data: BannerListResponse.model_validate(data))
            if cached_result:
                return self.create_success_result(cached_result)
            
            # 获取包含banner标签的图册
            albums, _ = await self.album_repo.get_albums(
                page_num=1,
                page_size=50,  # 获取更多图册，然后筛选
                status=AlbumStatus.ACTIVE,
                tags__contains="banner",  # 筛选banner标签
                order_by="sort_order",
                order_direction="asc"
            )
            
            banners = []
            for album in albums:
                # 获取图册中的图片
                if album.images:
                    # 按sort_order排序图片
                    sorted_images = sorted(album.images, key=lambda x: x.sort_order)
                    
                    for image in sorted_images:
                        if image.status == "active" and len(banners) < limit:
                            # 从图片的meta_data中获取title和description
                            image_meta = image.meta_data or {}
                            album_meta = album.meta_data or {}
                            
                            # 优先使用图片本身的title和description，如果没有则使用图册的作为默认值
                            title = image_meta.get("title") or album.name
                            description = image_meta.get("description") or album.description
                            link_url = image_meta.get("link_url") or album_meta.get("link_url")
                            
                            banner = BannerResponse(
                                id=image.id,
                                title=title,
                                image_url=image.url,
                                link_url=link_url,
                                sort_order=image.sort_order,
                                description=description
                            )
                            banners.append(banner)
                            
                            if len(banners) >= limit:
                                break
                
                if len(banners) >= limit:
                    break
            
            # 按sort_order排序
            banners.sort(key=lambda x: x.sort_order)
            
            result = BannerListResponse(
                banners=banners,
                total=len(banners)
            )
            
            # 缓存结果，设置较长的缓存时间（1小时）
            await self.cache_resource(cache_key, result, expire=3600)
            
            return self.create_success_result(result)
            
        except Exception as e:
            self.logger.error(f"获取首页轮播图失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取首页轮播图失败: {str(e)}"
            )