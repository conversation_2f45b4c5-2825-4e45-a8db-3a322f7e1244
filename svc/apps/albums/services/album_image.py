from typing import Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.albums.models.album_image import AlbumImage
from svc.apps.albums.repositories.album_image import AlbumImageRepository
from svc.apps.albums.schemas.album_image import (AlbumImageCreate,
                                                 AlbumImageListResponse,
                                                 AlbumImageResponse,
                                                 AlbumImageUpdate,
                                                 GetAlbumImagesParams)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

CACHE_TTL = 1800

class AlbumImageService(BaseService, BatchOperationMixin):
    """图册图片服务类，提供图片的创建、查询和管理功能"""

    def __init__(self, image_repo: AlbumImageRepository, redis: Optional[Redis] = None, **kwargs):
        config = ServiceConfig(
            resource_type="album_image",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(image_repo, config, redis, **kwargs)
        self.image_repo = image_repo

    async def get_resource_by_id(self, image_id: int) -> Optional[AlbumImage]:
        return await self.image_repo.get_by_id(image_id)

    async def get_album_image(self, image_id: int) -> Result[AlbumImageResponse]:
        try:
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            response = AlbumImageResponse.model_validate(image.to_dict())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取图片失败: {str(e)}"
            )

    async def get_album_images(self, params: GetAlbumImagesParams) -> Result[AlbumImageListResponse]:
        try:
            page_num = params.page_num
            page_size = params.page_size
            
            # 构建过滤条件
            filters = {}
            if params.album_id:
                filters["album_id"] = params.album_id
            if params.status:
                filters["status"] = params.status
                
            if params.search_term:
                # 使用搜索方法
                images = await self.image_repo.search(
                    term=params.search_term,
                    fields=["file_name", "url"],
                    limit=page_size,
                    **filters
                )
                total = len(images)
            else:
                # 使用标准分页方法
                images, total = await self.image_repo.get_paginated(
                    page_num=page_num,
                    page_size=page_size,
                    order_by=params.order_by,
                    order_direction=params.order_direction,
                    **filters
                )
            responses = [AlbumImageResponse.model_validate(i.to_dict()) for i in images]
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            paginated = AlbumImageListResponse(
                items=responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取图片列表失败: {str(e)}"
            )

    async def create_album_image(self, params: AlbumImageCreate) -> Result[AlbumImageResponse]:
        try:
            image = await self.image_repo.create(**params.model_dump())
            response = AlbumImageResponse.model_validate(image.to_dict())
            dispatch("album_image:uploaded", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"上传图片失败: {str(e)}"
            )

    async def update_album_image(self, image_id: int, params: AlbumImageUpdate) -> Result[AlbumImageResponse]:
        try:
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            update_data = params.model_dump(exclude_unset=True)
            image = await self.image_repo.update(image, data=update_data)
            response = AlbumImageResponse.model_validate(image.to_dict())
            dispatch("album_image:updated", payload=response.model_dump())
            return self.create_success_result(response)
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新图片失败: {str(e)}"
            )

    async def delete_album_image(self, image_id: int, soft_delete: bool = True) -> Result:
        """删除图册图片
        
        Args:
            image_id: 图片ID
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result: 删除结果
        """
        try:
            self.logger.info(f"删除图册图片: id={image_id}, soft_delete={soft_delete}")
            
            image = await self.get_resource_by_id(image_id)
            if not image:
                return self.resource_not_found_result(image_id)
            
            if soft_delete:
                # 软删除：更新状态为DELETED
                update_data = {"status": "deleted"}
                await self.image_repo.update(image, data=update_data)
                self.logger.info(f"图册图片软删除成功: id={image_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.image_repo.delete(image)
                self.logger.info(f"图册图片硬删除成功: id={image_id}")
            
            dispatch("album_image:deleted", payload={"id": image_id, "soft_delete": soft_delete})
            return self.create_success_result({"id": image_id, "soft_delete": soft_delete})
        except Exception as e:
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"删除图片失败: {str(e)}"
            ) 