"""
认证模块
包含用户认证、授权和管理功能
"""

# 导出ORM模型
from svc.apps.auth.models import User, Role, Permission, user_role

# 导出Schema模型
from svc.apps.auth.schemas import (
    # 基础模型
    
    # 认证相关模型
    AuthCredentials, TokenData, TokenPayload,
    RefreshTokenRequest, LoginRequest, LoginContext,
    LoginParams, PasswordResetRequest,
    PasswordResetParams,
    
    # 用户相关模型
    UserBase, UserCreate, UserUpdate, UserResponse, UserWithRoles,
    UserBatchUpdateRequest, UserBatchUpdateResponse, UserBatchDeleteRequest, UserBatchDeleteResponse,
    RoleBase, RoleCreate, RoleUpdate, RoleResponse,
    RoleBatchUpdateRequest, RoleBatchUpdateResponse, RoleBatchDeleteRequest, RoleBatchDeleteResponse,
    
    # 操作参数和结果
    GetUserParams, GetUsersParams, CreateUserParams, UpdateUserParams,
    DeleteUserParams, AssignRoleParams, RemoveRoleParams,
)

# 导出服务类
from svc.apps.auth.services import (
    AuthService,
    UserService,
    RoleService,
)

# 不再从 dependencies 重导出依赖函数，避免导入耦合

# 导出工具函数
from svc.core.exceptions.route_error_handler import handle_route_errors, AUTH_ERROR_MAPPING
from svc.core.exceptions.error_codes import ErrorCode

# 导出事件
from svc.apps.auth.events import (
    user_handlers,
    auth_handlers,
    role_handlers
)

__all__ = [
    # 模型
    "User", "Role", "Permission", "user_role",
    
    # Schema

    "AuthCredentials", "TokenData", "TokenPayload",
    "RefreshTokenRequest", "LoginRequest", "LoginContext",
    "LoginParams", "PasswordResetRequest",
    "PasswordResetParams",
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", "UserWithRoles",
    "RoleBase", "RoleCreate", "RoleUpdate", "RoleResponse",
    "GetUserParams", "GetUsersParams", "CreateUserParams", "UpdateUserParams",
    "DeleteUserParams", "AssignRoleParams",
    "RemoveRoleParams",
    "UserBatchUpdateRequest", "UserBatchUpdateResponse", "UserBatchDeleteRequest", "UserBatchDeleteResponse",
    "RoleBatchUpdateRequest", "RoleBatchUpdateResponse", "RoleBatchDeleteRequest", "RoleBatchDeleteResponse",
    
    # 服务类
    "AuthService", "UserService", "RoleService",
    
    # 错误处理
     "ErrorCode","handle_route_errors",

    # 事件
    "user_handlers", "auth_handlers", "role_handlers"
]


