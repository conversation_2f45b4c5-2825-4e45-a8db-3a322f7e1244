"""
提供身份验证服务的依赖注入
"""

from functools import lru_cache
from typing import Annotated, Any, Dict, Optional

from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.repositories import (RoleRepository, UserRepository,
                                        WechatUserRepository)
from svc.apps.auth.services import AuthService, RoleService, UserService
from svc.core.cache.redis import get_redis
from svc.core.config.settings import get_settings
from svc.core.database import get_session_for_route
from svc.core.dependencies.auth import auth_dependencies
from svc.core.security import TokenService

# 获取设置
settings = get_settings()


"""认证依赖项模块（移除模块级资源注册副作用）。"""


# 导出auth_dependencies中的依赖项
get_current_user = auth_dependencies.current_user()
get_current_active_user = auth_dependencies.current_active_user()
get_current_superuser = auth_dependencies.current_superuser()
has_permission = auth_dependencies.has_permission
has_role = auth_dependencies.has_role
resource_permission = auth_dependencies.resource_permission


# 服务实例配置字典
@lru_cache
def get_service_config() -> Dict[str, Any]:
    """获取服务配置字典
    
    返回一个配置字典，用于缓存服务实例
    
    Returns:
        Dict[str, Any]: 配置字典
    """
    return {
        "token_service": None,  # 由get_token_service初始化
        "auth_service": None,   # 由get_auth_service初始化
        "user_service": None,   # 由get_user_service初始化
        "role_service": None,   # 由get_role_service初始化
        "wechat_service": None  # 由get_wechat_auth_service初始化
    }

# 全局配置字典
config = get_service_config()


# 仓库依赖项
async def get_user_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> UserRepository:
    """获取用户仓库实例
    
    Args:
        db: 数据库会话
        
    Returns:
        UserRepository: 用户仓库实例
    """
    # 仓库在使用时会接收db参数，所以这里只返回仓库实例
    return UserRepository(db=db)


async def get_role_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RoleRepository:
    """获取角色仓库实例
    
    Args:
        db: 数据库会话
        
    Returns:
        RoleRepository: 角色仓库实例
    """
    # 仓库在使用时会接收db参数，所以这里只返回仓库实例
    return RoleRepository(db=db)


async def get_wechat_user_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> WechatUserRepository:
    """获取微信用户仓库实例
    
    Args:
        db: 数据库会话
        
    Returns:
        WechatUserRepository: 微信用户仓库实例
    """
    return WechatUserRepository(db=db)


# Token服务依赖项
@lru_cache
def get_token_service() -> TokenService:
    """获取令牌服务实例
    
    令牌服务被缓存为单例，每次调用返回相同实例
    
    Returns:
        TokenService: 令牌服务实例
    """
    return TokenService(
        secret_key=settings.secret_key,
        algorithm=settings.algorithm,
        access_token_ttl=settings.access_token_expire_minutes * 60,
        refresh_token_ttl=settings.refresh_token_expire_minutes * 60
    )


# 服务依赖项
async def get_auth_service(
    redis: Optional[Redis] = Depends(get_redis),
    token_service: TokenService = Depends(get_token_service),
    user_repo: UserRepository = Depends(get_user_repository),
    role_repo: RoleRepository = Depends(get_role_repository)
) -> AuthService:
    """获取身份验证服务实例
    
    第一次调用时创建新实例，后续调用更新实例中的依赖并返回
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        token_service: 令牌服务
        user_repo: 用户仓库实例
        role_repo: 角色仓库实例
        
    Returns:
        AuthService: 身份验证服务实例
    """
    if config["auth_service"] is None:
        config["auth_service"] = AuthService(
            user_repo=user_repo,
            token_service=token_service,
            role_repo=role_repo,
            redis=redis,
            token_expire_minutes=settings.access_token_expire_minutes
        )
    else:
        # 更新依赖项
        config["auth_service"].redis = redis
        config["auth_service"].token_service = token_service
        config["auth_service"].user_repo = user_repo
        config["auth_service"].role_repo = role_repo
    
    return config["auth_service"]


async def get_user_service(
    redis: Optional[Redis] = Depends(get_redis),
    user_repo: UserRepository = Depends(get_user_repository),
    role_repo: RoleRepository = Depends(get_role_repository)
) -> UserService:
    """获取用户服务实例
    
    第一次调用时创建新实例，后续调用更新实例中的依赖并返回
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        user_repo: 用户仓库实例
        role_repo: 角色仓库实例
        
    Returns:
        UserService: 用户服务实例
    """
    if config["user_service"] is None:
        config["user_service"] = UserService(
            user_repo=user_repo,
            role_repo=role_repo,
            redis=redis
        )
    else:
        # 更新依赖项
        config["user_service"].redis = redis
        config["user_service"].user_repo = user_repo
        config["user_service"].role_repo = role_repo
    
    return config["user_service"]


async def get_role_service(
    redis: Optional[Redis] = Depends(get_redis),
    role_repo: RoleRepository = Depends(get_role_repository),
    user_repo: UserRepository = Depends(get_user_repository)
) -> RoleService:
    """获取角色服务实例
    
    第一次调用时创建新实例，后续调用更新实例中的依赖并返回
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        role_repo: 角色仓库实例
        user_repo: 用户仓库实例
        
    Returns:
        RoleService: 角色服务实例
    """
    if config["role_service"] is None:
        config["role_service"] = RoleService(
            role_repo=role_repo,
            user_repo=user_repo,
            redis=redis
        )
    else:
        # 更新依赖项
        config["role_service"].redis = redis
        config["role_service"].role_repo = role_repo
        config["role_service"].user_repo = user_repo
    
    return config["role_service"]


async def get_wechat_auth_service(
    db: AsyncSession = Depends(get_session_for_route),
    redis: Optional[Redis] = Depends(get_redis),
    token_service: TokenService = Depends(get_token_service),
    # user_repo: UserRepository = Depends(get_user_repository),
    # role_repo: RoleRepository = Depends(get_role_repository)
):
    """获取微信认证服务实例

    第一次调用时创建新实例，后续调用更新实例中的依赖并返回

    Args:
        db: 数据库会话
        redis: Redis客户端
        token_service: 令牌服务
        user_repo: 用户仓库实例
        role_repo: 角色仓库实例

    Returns:
        WechatAuthService: 微信认证服务实例
    """
    # 延迟导入避免循环导入
    from svc.core.services.wechat import WechatAuthService

    if not settings.wechat_enabled:
        raise ValueError("WeChat登录未启用，请在配置中启用")

    if config["wechat_service"] is None:
        config["wechat_service"] = WechatAuthService(
            db=db,
            redis=redis,
            token_service=token_service,
        )
    else:
        # 更新数据库会话和Redis客户端
        config["wechat_service"].db = db
        config["wechat_service"].redis = redis
        config["wechat_service"].token_service = token_service
     
    
    return config["wechat_service"]


# 保留为应用启动期显式调用# 保留为应用启动期显式调用