"""
角色模块的事件处理器 (已重构支持 DI)
提供处理角色相关事件的功能，实现关注点分离和松耦合设计
"""

import logging
from typing import Any, Dict, List, Optional

# Import DI mechanism
from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from fastapi_events.typing import Event

from svc.apps.auth.dependencies import get_role_service, get_user_service
# Import Services required by handlers
from svc.apps.auth.services.role import RoleService
from svc.apps.auth.services.user import \
    UserService  # Assuming UserService exists
from svc.core.events.event_names import (AUTH_ROLE_CREATED, AUTH_ROLE_DELETED,
                                         AUTH_ROLE_UPDATED,
                                         AUTH_USER_ROLE_ASSIGNED,
                                         AUTH_USER_ROLE_REMOVED,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_USER_NOTIFICATION_REQUESTED)
# from fastapi_events.handlers.local import local_handler
from svc.core.events.local_handlers import local_handler

# Remove direct db/redis dependencies
# from svc.core.database.session import get_session
# from redis.asyncio import Redis
# from sqlalchemy.ext.asyncio import AsyncSession

# Keep Repo import if needed by providers, but not directly in handlers
# from svc.apps.auth.repositories import UserRepository



logger = logging.getLogger(__name__)

@local_handler.register(event_name=AUTH_ROLE_CREATED)
async def handle_role_created(
    event: Event
):
    """处理角色创建事件"""
    event_name,payload = event
    role_id = payload.get("role_id")
    role_name = payload.get("role_name")
    created_by = payload.get("created_by_user_id")
    permissions = payload.get("permissions")
    ip_address = payload.get("ip_address") # Assuming this might be passed
    user_agent = payload.get("user_agent") # Assuming this might be passed
    
    if not role_id or not role_name:
        logger.error("处理角色创建事件失败: 缺少 role_id 或 role_name")
        return

    logger.info(f"新角色创建: role_id={role_id}, role_name={role_name}")

    try:
        
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": created_by,
            "action": "role_created",
            "resource_type": "role",
            "resource_id": role_id,
            "metadata": {
                "role_name": role_name,
                "permissions": permissions or [],
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })
    except Exception as e:
        logger.error(f"处理角色创建事件失败: role_id={role_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_ROLE_UPDATED)
async def handle_role_updated(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Inject UserService
):
    """处理角色更新事件，记录审计并在权限变更时通知受影响用户 (使用 DI)"""
    event_name,payload = event
    role_id = payload.get("role_id")
    role_name = payload.get("role_name")
    updated_fields = payload.get("updated_fields", [])
    updated_by = payload.get("updated_by_user_id")
    permissions = payload.get("permissions") # Updated permissions list
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not role_id or not role_name:
        logger.error("处理角色更新事件失败: 缺少 role_id 或 role_name")
        return

    logger.info(f"角色信息更新: role_id={role_id}, role_name={role_name}, 更新字段={updated_fields}")

    try:
       
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": updated_by,
            "action": "role_updated",
            "resource_type": "role",
            "resource_id": role_id,
            "metadata": {
                "role_name": role_name,
                "updated_fields": updated_fields,
                "permissions": permissions, # Include updated permissions
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

        # 如果角色权限发生变化，需要通知相关用户
        if "permissions" in updated_fields:
            affected_users_result = await user_service.get_users_by_role_id(role_id)
            affected_user_ids = [user.id for user in affected_users_result.data] if affected_users_result.is_success else []

            if affected_user_ids:
                logger.info(f"角色权限变更，开始通知 {len(affected_user_ids)} 个受影响用户")
                for user_id in affected_user_ids:
                    # 触发用户权限变更通知
                    # Replace internal event dispatch
                    # await event_bus.emit(
                    #     EventType.USER_NOTIFICATION_REQUESTED,
                    #     user_id=user_id,
                    #     notification_type="permissions_changed",
                    #     channel="in_app",
                    #     data={
                    #         "user_id": user_id,
                    #         "role_id": role_id,
                    #         "role_name": role_name,
                    #         "permissions_changed": True
                    #     }
                    # )
                    dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
                        "user_id": user_id,
                        "notification_type": "permissions_changed",
                        "channel": "in_app", # Or other channels
                        "data": {
                            "user_id": user_id,
                            "role_id": role_id,
                            "role_name": role_name,
                            "permissions_changed": True,
                            "new_permissions": permissions # Provide the new permissions
                        }
                    })
            else:
                logger.info("角色权限变更，但无用户拥有此角色")

    except Exception as e:
        logger.error(f"处理角色更新事件失败: role_id={role_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_ROLE_DELETED)
async def handle_role_deleted(
    event: Event,
    role_service: RoleService = Depends(get_role_service), # Inject RoleService
):
    """处理角色删除事件，记录审计、清除缓存并通知（尝试）受影响用户 (使用 DI)"""
    event_name,payload = event
    role_id = payload.get("role_id")
    role_name = payload.get("role_name")
    deleted_by = payload.get("deleted_by_user_id")
    previously_affected_user_ids = payload.get("previously_affected_user_ids") or [] # Assume passed in payload
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not role_id or not role_name:
        logger.error("处理角色删除事件失败: 缺少 role_id 或 role_name")
        return
        
    logger.info(f"角色被删除: role_id={role_id}, role_name={role_name}")

    try:
        affected_user_ids = previously_affected_user_ids
        if not affected_user_ids:
            logger.warning(f"未提供或无法确定角色 {role_id} 删除前关联的用户ID列表，将跳过通知")

        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": deleted_by,
            "action": "role_deleted",
            "resource_type": "role",
            "resource_id": role_id,
            "metadata": {
                "role_name": role_name,
                "affected_user_count": len(affected_user_ids),
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

        # 清除角色缓存 (使用注入的 RoleService)
        try:
            await role_service.invalidate_resource_cache(role_id)
            logger.info(f"已调用角色缓存失效处理: role_id={role_id}")
        except Exception as cache_exc:
            logger.error(f"调用角色缓存失效处理失败: role_id={role_id}, error={cache_exc}", exc_info=True)

        # 如果能确定受影响的用户，通知他们
        if affected_user_ids:
            logger.info(f"角色删除，开始通知 {len(affected_user_ids)} 个受影响用户")
            for user_id in affected_user_ids:
                # 触发用户角色移除通知
                # Replace internal event dispatch
                # await event_bus.emit(
                #     EventType.USER_NOTIFICATION_REQUESTED,
                #     user_id=user_id,
                #     notification_type="role_removed",
                #     channel="in_app",
                #     data={
                #         "user_id": user_id,
                #         "role_id": role_id,
                #         "role_name": role_name
                #     }
                # )
                dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
                    "user_id": user_id,
                    "notification_type": "role_removed",
                    "channel": "in_app",
                    "data": {
                        "user_id": user_id,
                        "role_id": role_id,
                        "role_name": role_name
                    }
                })

    except Exception as e:
        logger.error(f"处理角色删除事件失败: role_id={role_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_ROLE_ASSIGNED)
async def handle_role_assigned(
    event: Event,
):
    """处理角色分配事件"""
    event_name,payload = event
    role_id = payload.get("role_id")
    user_id = payload.get("user_id")
    assigned_by = payload.get("assigned_by_user_id")
    role_name = payload.get("role_name") # Assuming role name is passed
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not role_id or not user_id:
        logger.error("处理角色分配事件失败: 缺少 role_id 或 user_id")
        return
        
    logger.info(f"角色分配: role_id={role_id}, user_id={user_id}")

    try:
        effective_role_name = role_name or f"角色 {role_id}"

        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": assigned_by, # The user performing the action
            "action": "role_assigned",
            "resource_type": "user", # The resource being modified is the user
            "resource_id": user_id,
            "metadata": {
                "role_id": role_id,
                "role_name": effective_role_name,
                "assigned_to_user_id": user_id,
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

    except Exception as e:
        logger.error(f"处理角色分配事件失败: role_id={role_id}, user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_ROLE_REMOVED)
async def handle_role_removed(
    event: Event,
):
    """处理角色移除事件"""
    event_name,payload = event
    role_id = payload.get("role_id")
    user_id = payload.get("user_id")
    removed_by = payload.get("removed_by_user_id")
    role_name = payload.get("role_name") # Assuming role name is passed
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not role_id or not user_id:
        logger.error("处理角色移除事件失败: 缺少 role_id 或 user_id")
        return

    logger.info(f"角色移除: role_id={role_id}, user_id={user_id}")

    try:
        effective_role_name = role_name or f"角色 {role_id}"

        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": removed_by, # The user performing the action
            "action": "role_removed",
            "resource_type": "user", # The resource being modified is the user
            "resource_id": user_id,
            "metadata": {
                "role_id": role_id,
                "role_name": effective_role_name,
                "removed_from_user_id": user_id,
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })

    except Exception as e:
        logger.error(f"处理角色移除事件失败: role_id={role_id}, user_id={user_id}, 错误={str(e)}", exc_info=True)
