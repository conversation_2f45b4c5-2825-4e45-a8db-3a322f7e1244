"""权限模块的ORM模型定义。

提供权限相关的数据库模型，包括：
- 权限基本信息
- 权限与角色关系
"""

# 标准库导入
from typing import Optional, List
from datetime import datetime

# 第三方库导入
from sqlalchemy import Column, String, DateTime, Integer, Table, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import TYPE_CHECKING

# 项目内部导入
from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 角色-权限关联表
role_permission_table = Table(
    "role_permissions",
    Base.metadata,
    Column("role_id", Integer, ForeignKey("roles.id", ondelete="CASCADE"), primary_key=True),
    Column("permission_id", Integer, ForeignKey("permissions.id", ondelete="CASCADE"), primary_key=True),
    Column("created_at", DateTime, default=get_utc_now_without_tzinfo, nullable=False),
    Column("updated_at", DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo, nullable=False),
    # 添加索引以提升查询性能
    Index("idx_role_permission_role_id", "role_id"),
    Index("idx_role_permission_permission_id", "permission_id"),
    # 添加唯一约束防止重复分配
    UniqueConstraint("role_id", "permission_id", name="uq_role_permission")
)

class Permission(Base, ResourceMixin):
    """权限模型"""
    
    __tablename__ = "permissions"
    __resource_type__ = "permission"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(String(255))
    
    # 关联关系
    if TYPE_CHECKING:
        from .role import Role  # noqa: F401

    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=role_permission_table,
        back_populates="permissions",
        lazy="selectin"
    )
    
    def __repr__(self) -> str:
        return f"<Permission {self.name}>" 