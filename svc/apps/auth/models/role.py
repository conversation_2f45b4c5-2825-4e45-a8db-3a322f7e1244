"""角色模块的ORM模型定义。

提供角色相关的数据库模型，包括：
- 角色基本信息
- 角色权限管理
- 角色与用户关系
"""

# 标准库导入
from typing import Optional, List, Dict, Any
from datetime import datetime

# 第三方库导入
from sqlalchemy import Boolean, String, DateTime, Integer
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import TYPE_CHECKING

# 项目内部导入
from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from .user_role import user_role

class Role(Base, ResourceMixin):
    """角色模型"""
    
    __tablename__ = "roles"
    __resource_type__ = "role"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(50), unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(String(255))
    is_system: Mapped[bool] = mapped_column(Boolean, default=False)  # 是否为系统角色
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=get_utc_now_without_tzinfo
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=get_utc_now_without_tzinfo,
        onupdate=get_utc_now_without_tzinfo
    )
    
    # 软删除字段
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间（软删除）"
    )
    
    # 关联关系
    if TYPE_CHECKING:
        from .user import User  # noqa: F401
        from .permission import Permission  # noqa: F401

    users: Mapped[List["User"]] = relationship(
        "User",
        secondary=user_role,
        back_populates="roles",
        lazy="selectin"  # 改为selectin，保持与User模型的一致性
    )
    
    permissions: Mapped[List["Permission"]] = relationship(
        "Permission",
        secondary="role_permissions",
        back_populates="roles",
        lazy="selectin"
    )
    
    def __repr__(self) -> str:
        return f"<Role {self.name}>"
    
    def has_permission(self, permission: str) -> bool:
        """
        检查角色是否有指定权限
        
        Args:
            permission: 权限名称
            
        Returns:
            bool: 角色是否拥有该权限
        """
        return any(p.name == permission for p in self.permissions)
    
    def to_dict(self, include_permissions: bool = True) -> Dict[str, Any]:
        """
        角色对象转字典
        
        Args:
            include_permissions: 是否包含权限列表，默认为True
            
        Returns:
            Dict[str, Any]: 角色的字典表示
        """
        result = {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_system": self.is_system,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
        }
        
        if include_permissions:
            result["permissions"] = [p.name for p in self.permissions]
            
        return result 