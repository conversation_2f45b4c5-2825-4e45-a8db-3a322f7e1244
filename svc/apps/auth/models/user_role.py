"""
关联表模块。
定义了用户、角色等实体之间的关联关系表。
"""
from datetime import datetime

from sqlalchemy import Column, ForeignKey, Table, DateTime, Integer, Index, UniqueConstraint
from svc.core.database import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 用户角色关联表
user_role = Table(
    "user_role",
    Base.metadata,
    Column(
        "user_id",
        Integer,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "role_id",
        Integer,
        ForeignKey("roles.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "created_at",
        DateTime,
        default=get_utc_now_without_tzinfo,
        nullable=False,
    ),
    Column(
        "updated_at",
        DateTime,
        default=get_utc_now_without_tzinfo,
        onupdate=get_utc_now_without_tzinfo,
        nullable=False,
    ),
    # 添加索引以提升查询性能
    Index("idx_user_role_user_id", "user_id"),
    Index("idx_user_role_role_id", "role_id"),
    # 添加唯一约束防止重复分配
    UniqueConstraint("user_id", "role_id", name="uq_user_role"),
    extend_existing=True,  # 允许表定义扩展
)

