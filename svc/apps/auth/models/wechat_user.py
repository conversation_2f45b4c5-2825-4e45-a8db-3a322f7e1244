"""
微信用户模型模块。
"""
from typing import Optional, Any, Dict
from datetime import datetime

from sqlalchemy import <PERSON>umn, String, Integer, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class WechatUser(Base, ResourceMixin):
    """微信用户模型"""
    __tablename__ = "wechat_users"
    __resource_type__ = "wechat_user"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    openid: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    unionid: Mapped[Optional[str]] = mapped_column(String(50), unique=True, index=True, nullable=True)
    user_id: Mapped[Optional[int]] = mapped_column(Inte<PERSON>, ForeignKey("users.id"), nullable=True)
    from_code: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # 微信用户信息
    nickname: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    avatar_url: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    gender: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 0: 未知, 1: 男, 2: 女
    country: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    province: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    city: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)

    
    # 会话信息
    session_key: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # 状态
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=False),
        default=get_utc_now_without_tzinfo,
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=False),
        default=get_utc_now_without_tzinfo,
        onupdate=get_utc_now_without_tzinfo,
        nullable=False
    )
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=False), nullable=True)
    
    # 关联用户
    user = relationship("User", back_populates="wechat_users" )
    
    def to_dict(self) -> Dict[str, Any]:
        """将微信用户对象转换为字典，用于JSON序列化和缓存
        
        Returns:
            Dict[str, Any]: 微信用户的字典表示
        """
        return {
            "id": self.id,
            "openid": self.openid,
            "unionid": self.unionid,
            "user_id": self.user_id,
            "nickname": self.nickname,
            "avatar_url": self.avatar_url,
            "gender": self.gender,
            "country": self.country,
            "province": self.province,
            "city": self.city,
            "language": self.language,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
        }
    
    async def update(self, db: AsyncSession, **kwargs) -> None:
        """更新微信用户信息"""
        for key, value in kwargs.items():
            setattr(self, key, value)
        await db.commit()
        await db.refresh(self)
    
    async def refresh(self, db: AsyncSession) -> None:
        """刷新微信用户信息"""
        await db.refresh(self)
        return self 

    async def save(self, db: AsyncSession) -> None:
        """保存微信用户到数据库
        
        Args:
            db: 数据库会话
        """
        db.add(self)
        await db.commit()
        await db.refresh(self)
    
    async def update_last_login(self, db: AsyncSession) -> None:
        """更新最后登录时间
        
        Args:
            db: 数据库会话
        """
        self.last_login = get_utc_now_without_tzinfo()
        await self.save(db)
    
    @classmethod
    async def get_by_id(cls, db: AsyncSession, id: int) -> Optional["WechatUser"]:
        """通过ID获取微信用户
        
        Args:
            db: 数据库会话
            id: 微信用户ID
            
        Returns:
            Optional[WechatUser]: 微信用户对象，不存在时返回None
        """
        result = await db.execute(
            select(cls).where(cls.id == id)
        )
        return result.scalars().first()
    
    @classmethod
    async def get_by_openid(cls, db: AsyncSession, openid: str) -> Optional["WechatUser"]:
        """通过openid获取微信用户
        
        Args:
            db: 数据库会话
            openid: 微信用户的openid
            
        Returns:
            Optional[WechatUser]: 微信用户对象，不存在时返回None
        """
        result = await db.execute(
            select(cls).where(cls.openid == openid)
        )
        return result.scalars().first()
    
    @classmethod
    async def get_by_unionid(cls, db: AsyncSession, unionid: str) -> Optional["WechatUser"]:
        """通过unionid获取微信用户
        
        Args:
            db: 数据库会话
            unionid: 微信用户的unionid
            
        Returns:
            Optional[WechatUser]: 微信用户对象，不存在时返回None
        """
        if not unionid:
            return None
            
        result = await db.execute(
            select(cls).where(cls.unionid == unionid)
        )
        return result.scalars().first()
    
    @classmethod
    async def get_by_user_id(cls, db: AsyncSession, user_id: int) -> Optional["WechatUser"]:
        """通过用户ID获取微信用户
        
        Args:
            db: 数据库会话
            user_id: 系统用户ID
            
        Returns:
            Optional[WechatUser]: 微信用户对象，不存在时返回None
        """
        result = await db.execute(
            select(cls).where(cls.user_id == user_id)
        )
        return result.scalars().first() 