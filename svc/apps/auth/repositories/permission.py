"""
权限仓库。
提供基于基类的标准数据访问，避免手写 SQL。
"""

from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.repositories import BaseRepository
from svc.apps.auth.models.permission import Permission
from svc.apps.auth.schemas.permission import PermissionCreate, PermissionUpdate


class PermissionRepository(BaseRepository[Permission, PermissionCreate, PermissionUpdate]):
    def __init__(self, db: AsyncSession):
        super().__init__(db, Permission)




