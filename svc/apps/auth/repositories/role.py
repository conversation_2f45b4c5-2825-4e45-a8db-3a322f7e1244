"""
角色数据访问层。
负责角色模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Dict, Any, Set, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from svc.apps.auth.models.role import Role
from svc.apps.auth.models.permission import Permission
from svc.apps.auth.models.user_role import user_role
from svc.apps.auth.models.user import User 
from svc.apps.auth.schemas.role import RoleCreate, RoleUpdate
from svc.core.repositories.base import BaseRepository


class RoleRepository(BaseRepository[Role, RoleCreate, RoleUpdate]):
    """角色仓库类，提供角色数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化角色仓库"""
        super().__init__(model=Role,db=db)

    def _get_default_load_options(self) -> List[Any]:
        """获取角色模型的默认关系加载选项
        
        默认加载：
        - permissions: 关联的权限（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Role.permissions)
        ]
    
    async def get_by_name(self, name: str, load_options: Optional[List[Any]] = None) -> Optional[Role]:
        """
        通过名称获取角色
        
        Args:
            name: 角色名称
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[Role]: 角色对象，如果不存在则返回None
        """
        return await self.get_one(name=name, load_options=load_options)
    
    async def get_user_roles(
        self, 
        user_id: int, 
        page_num: int = 1, 
        page_size: int = 100,
        search_term: str = "",
        order_by: str = "created_at",
        order_direction: str = "desc",
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Role], int]:
        """
        获取用户的所有角色
        
        Args:
            user_id: 用户ID
            page_num: 页码
            page_size: 每页大小
            search_term: 搜索关键词
            order_by: 排序字段
            order_direction: 排序方向
            load_options: SQLAlchemy加载选项
        Returns:
            Tuple[List[Role], int]: 角色列表和总数
        """
        filters = {"users__has": user_id}
        adv_filters: Dict[str, Any] = {}
        if search_term:
            adv_filters["name__ilike"] = f"%{search_term}%"
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction,
            filters=adv_filters or None,
            load_options=load_options,
            **filters,
        )
        return items, (total or 0)
    

        result = await self.get_one(name=name)
        return result is not None
    
    async def get_permissions(self, role: Role) -> List[Permission]:
        """
        获取角色的所有权限
        
        Args:
            role: 角色对象
            
        Returns:
            List[Permission]: 权限列表
        """
        # 使用selectinload预加载关系，避免lazy loading问题
        
        # 重新获取角色并预加载权限关系
        role_with_permissions = await self.get_by_id(
            role.id, 
            load_options=[selectinload(Role.permissions)]
        )
        
        if role_with_permissions and hasattr(role_with_permissions, 'permissions'):
            return list(role_with_permissions.permissions)
        
        return []
    
    async def get_permission_names(self, role: Role) -> Set[str]:
        """
        获取角色的所有权限名称
        
        Args:
            db: 数据库会话
            role: 角色对象
            
        Returns:
            Set[str]: 权限名称集合
        """
        permissions = await self.get_permissions(role)
        return {p.name for p in permissions}
    
    async def add_permission(self, role: Role, permission: Permission) -> None:
        """
        为角色添加权限
        
        Args:
            db: 数据库会话
            role: 角色对象
            permission: 权限对象
        """
        # 使用基类关系管理，安全地检查关系是否存在
        if not await self.has_relation(role, "permissions", permission):
            await self.add_relation(role, "permissions", permission)
    
    async def remove_permission(self, role: Role, permission: Permission) -> None:
        """
        移除角色权限
        
        Args:
            db: 数据库会话
            role: 角色对象
            permission: 权限对象
        """
        await self.remove_relation(role, "permissions", permission)
    
    async def clear_permissions(self, role: Role) -> None:
        """
        清除角色的所有权限
        
        Args:
            db: 数据库会话
            role: 角色对象
        """
        await self.clear_relations(role, "permissions")
    

    

    
    async def get_role_with_permissions(self, role_id: int) -> Optional[Dict[str, Any]]:
        """
        获取角色及其权限信息
        
        Args:
            db: 数据库会话
            role_id: 角色ID
            
        Returns:
            Optional[Dict[str, Any]]: 角色及其权限信息的字典，如果角色不存在则返回None
        """
        # 获取角色
        role = await self.get_by_id(role_id)
        if not role:
            return None
        
        # 获取权限
        permissions = await self.get_permissions(role)
        permission_names = [p.name for p in permissions]
        
        # 构建结果字典
        result = {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "is_system": role.is_system,
            "created_at": role.created_at.isoformat() if role.created_at else None,
            "updated_at": role.updated_at.isoformat() if role.updated_at else None,
            "permissions": permission_names
        }
        
        return result
    
    @classmethod
    def get_default_roles(cls) -> List[Dict[str, Any]]:
        """
        获取默认角色配置
        
        Returns:
            List[Dict[str, Any]]: 默认角色列表
        """
        return [
            {
                "name": "admin",
                "description": "系统管理员",
                "is_system": True,
                "permissions": ["*:*"]  # 所有权限
            },
            {
                "name": "user",
                "description": "普通用户",
                "is_system": True,
                "permissions": [
                    "get:/api/v1/users/me",
                    "put:/api/v1/users/me",
                    "get:/api/v1/users/{id}"
                ]
            },
            {
                "name": "guest",
                "description": "访客",
                "is_system": True,
                "permissions": [
                    "get:/api/v1/users/{id}"
                ]
            }
        ] 