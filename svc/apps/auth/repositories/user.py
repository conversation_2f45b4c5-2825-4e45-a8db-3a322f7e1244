"""
用户数据访问层。
负责用户模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Set, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import func
from sqlalchemy.orm import selectinload

from svc.apps.auth.models.role import Role
from svc.apps.auth.models.user import User
from svc.apps.auth.models.user_role import user_role
from svc.apps.auth.schemas.user import UserCreate, UserUpdate
# 导入数据库异常处理
from svc.core.database import (RecordNotFoundError, UniqueConstraintError,
                               handle_repository_errors)
from svc.core.repositories.base import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """用户仓库类，提供用户数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化用户仓库"""
        super().__init__(model=User,db=db)

    def _get_default_load_options(self) -> List[Any]:
        """获取用户模型的默认关系加载选项
        
        默认加载：
        - roles: 关联的角色（selectin关系）
        - wechat_users: 关联的微信用户（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(User.roles),  # 添加roles关系加载
            selectinload(User.wechat_users)
        ]
    
    async def get_by_email(self, email: str, load_options: Optional[List[Any]] = None) -> Optional[User]:
        """
        通过邮箱获取用户
        
        Args:
            email: 用户邮箱
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        return await self.get_one(email=email, load_options=load_options)
    
    async def get_by_username(self, username: str, load_options: Optional[List[Any]] = None) -> Optional[User]:
        """
        通过用户名获取用户
        
        Args:
            username: 用户名
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[User]: 用户对象，如果不存在则返回None
        """
        return await self.get_one(username=username, load_options=load_options)
    
    # get_by_id 直接使用父类BaseRepository.get_by_id方法
    
    # delete方法可以直接使用BaseRepository.delete
    
    async def get_users(
        self,
        *,
        page_num: int = 1,
        page_size: int = 100,
        is_active: Optional[bool] = None,
        search_term: Optional[str] = None,
        order_by: str = "id",
        order_direction: str = "asc",
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[User], int]:
        """
        获取用户列表和总数
        
        Args:
            page_num: 页码
            page_size: 每页大小
            is_active: 是否筛选活跃用户
            search_term: 搜索关键词
            order_by: 排序字段
            order_direction: 排序方向
            load_options: SQLAlchemy加载选项
            
        Returns:
            Tuple[List[User], int]: 用户列表和总数
        """
        # 统一通过基类分页能力实现
        simple_filters: Dict[str, Any] = {}
        if is_active is not None:
            simple_filters["is_active"] = is_active

        adv_filters: Dict[str, Any] = {}
        if search_term:
            adv_filters["__or__"] = {
                "username__ilike": f"%{search_term}%",
                "email__ilike": f"%{search_term}%",
                "fullname__ilike": f"%{search_term}%",
            }

        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction,
            filters=adv_filters or None,
            load_options=load_options,
            **simple_filters,
        )
        return items, total
    

    

    
    
    async def update_last_login(self, user: User) -> User:
        """
        更新用户最后登录时间
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            User: 更新后的用户对象
        """
        # 这里需要保留，因为这是特定的业务逻辑
        # 实现更新最后登录时间的逻辑
        return await self.update(user, {"last_login": get_utc_now_without_tzinfo()})
    
    # 以下方法属于业务层职责，建议在 UserService 中实现
    # 保持仓库层仅负责数据访问与关系读写
    
