"""
微信用户数据访问层。
负责微信用户模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.models.wechat_user import WechatUser
from svc.core.repositories.base import BaseRepository


class WechatUserRepository(BaseRepository[WechatUser, None, None]):
    """微信用户仓库类，提供微信用户数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化微信用户仓库"""
        super().__init__(db, WechatUser)
    

