"""
用户管理API路由
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        get_current_superuser,
                                        get_user_service)
from svc.apps.auth.models.user import User
from svc.apps.auth.schemas import (AssignRoleParams, CreateUserParams,
                                   DeleteUserParams, GetUsersParams,
                                   RemoveRoleParams, RoleBase,
                                   UpdateUserParams, UserCreate,
                                   UserListResponse, UserResponse, UserUpdate,
                                   UserWithRoles, UserBatchUpdateRequest,
                                   UserBatchUpdateResponse, UserBatchDeleteRequest,
                                   UserBatchDeleteResponse)
from svc.apps.auth.services import RoleService, UserService
from svc.core.database import with_transaction
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.exceptions.route_error_handler import (AUTH_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

router = APIRouter(tags=["用户管理"])

# === 客户端路由 (Current User - /users/me/*) ===

@router.get("/me", response_model=Result[Dict[str, Any]]) # Path: /users/me - Return detailed info for current user
@handle_route_errors(AUTH_ERROR_MAPPING)
async def get_my_profile(
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[Dict[str, Any]]:
    """获取当前用户的详细资料（包含角色）"""
    user_result = await user_service.get_user(current_user.id)
    return user_result

@router.put("/me", response_model=Result[Dict[str, Any]]) # Path: /users/me (Update current user)
@with_transaction()
@handle_route_errors(AUTH_ERROR_MAPPING)
async def update_my_profile(
    user_in: UserUpdate, # Schema for user self-update (might differ from admin update)
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[Dict[str, Any]]:
    """更新当前用户的个人资料"""
    # Ensure user cannot update certain fields like is_active, is_superuser, roles via this endpoint
    update_params = UpdateUserParams(
        user_id=current_user.id,
        user_data=user_in
    )
    # Service layer should enforce restrictions on updatable fields for non-admins
    return await user_service.update_user(update_params, allowed_by_admin=False)


# === 管理端路由 (Admin Routes - /users/admin/*) ===

@router.get("/admin/", response_model=Result[UserListResponse]) # Path: /users/admin/
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_list_users(
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
    params: PageParams = Depends(),
) -> Result[UserListResponse]:
    """获取用户列表 (管理端)"""
    params_obj = GetUsersParams(
        page_num=params.page_num,
        page_size=params.page_size,
        order_by="created_at",
        order_direction="desc"
    )
    return await user_service.get_users(params_obj)

@router.get("/admin/details/{user_id}", response_model=Result[Dict[str, Any]]) # Path: /users/admin/details/{user_id}
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_get_user_details(
    user_id: int = Path(..., description="用户ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
    ) -> Result[Dict[str, Any]]:
    """获取任意用户的详细资料（包含角色） (管理端)"""
    user_result = await user_service.get_user(user_id)
    return user_result

@router.post("/admin/", response_model=Result[Dict[str, Any]]) # Path: /users/admin/
@with_transaction(auto_commit=True)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_create_user(
    user_in: UserCreate, # Admin might have a different UserCreate schema (e.g., setting roles)
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[Dict[str, Any]]:
    """创建新用户 (管理端)"""
    create_params = CreateUserParams(
        user_data=user_in,
        send_welcome_email=False # Or make configurable
    )
    return await user_service.create_user(create_params)

# === 批量操作路由 ===

@router.put("/admin/batch", response_model=Result[UserBatchUpdateResponse])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_batch_update_users(
    request: UserBatchUpdateRequest,
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser),
) -> Result[UserBatchUpdateResponse]:
    """批量更新用户 (管理端)
    
    批量更新多个用户的基本信息、状态等
    """
    return await user_service.batch_update_users(request=request)

@router.delete("/admin/batch", response_model=Result[UserBatchDeleteResponse])
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_batch_delete_users(
    request: UserBatchDeleteRequest,
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser),
) -> Result[UserBatchDeleteResponse]:
    """批量删除用户 (管理端)
    
    批量删除多个用户
    """
    return await user_service.batch_delete_users(request=request)

@router.put("/admin/{user_id}", response_model=Result[Dict[str, Any]]) # Path: /users/admin/{user_id}
@with_transaction(auto_commit=True)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_update_user(
    user_in: UserUpdate, # Admin might have a different UserUpdate schema
    user_id: int = Path(..., description="用户ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[Dict[str, Any]]:
    """更新任意用户信息 (管理端)"""
    update_params = UpdateUserParams(
        user_id=user_id,
        user_data=user_in
    )
    # Allow admin to update sensitive fields
    return await user_service.update_user(update_params, allowed_by_admin=True)

@router.delete("/admin/{user_id}", response_model=Result[Dict[str, Any]]) # Path: /users/admin/{user_id}
@with_transaction()
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_delete_user(
    user_id: int = Path(..., description="用户ID"),
    soft_delete: bool = Query(True, description="是否软删除", alias="softDelete"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[Dict[str, Any]]:
    """删除用户 (管理端)"""
    delete_params = DeleteUserParams(
        user_id=user_id,
        executor_id=current_user.id,
        soft_delete=soft_delete
    )
    return await user_service.delete_user(delete_params)

@router.post("/admin/assign-role/{user_id}/{role_id}", response_model=Result[Dict[str, Any]]) # Path: /users/admin/assign-role/{user_id}/{role_id}
@with_transaction(auto_commit=True)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_assign_role_to_user(
    user_id: int = Path(..., description="用户ID"),
    role_id: int = Path(..., description="角色ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[Dict[str, Any]]:
    """为用户分配角色 (管理端)"""
    assign_params = AssignRoleParams(
        user_id=user_id,
        role_id=role_id,
        executor_id=current_user.id
    )
    return await user_service.assign_role(assign_params)

@router.delete("/admin/remove-role/{user_id}/{role_id}", response_model=Result[Dict[str, Any]]) # Path: /users/admin/remove-role/{user_id}/{role_id}
@with_transaction(auto_commit=True)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def admin_remove_role_from_user(
    user_id: int = Path(..., description="用户ID"),
    role_id: int = Path(..., description="角色ID"),
    user_service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_superuser), # Or check permission
) -> Result[Dict[str, Any]]:
    """从用户移除角色 (管理端)"""
    remove_params = RemoveRoleParams(
        user_id=user_id,
        role_id=role_id,
        executor_id=current_user.id
    )
    return await user_service.remove_role(remove_params)



