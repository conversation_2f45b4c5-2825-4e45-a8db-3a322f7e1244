"""
认证模块的所有模型定义
包含认证、用户、角色和微信相关模型
"""

# 导出认证相关模型
from svc.apps.auth.schemas.auth import (
    AuthCredentials,
    LoginParams,
    TokenData,
    TokenPayload,
    PasswordResetRequest,
    PasswordResetParams,
    RefreshTokenRequest,
    LoginRequest,
    LoginContext,
    PasswordResetResponseData,
    WechatResponse
)

# 导出用户相关模型
from svc.apps.auth.schemas.user import (
    UserProtocol,
    UserType,
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserWithRoles,
    GetUserParams,
    GetUsersParams,
    CreateUserParams,
    UpdateUserParams,   
    DeleteUserParams,
    AssignRoleParams,
    RemoveRoleParams,
    UserListResponse,
    UserBatchUpdateRequest,
    UserBatchUpdateResponse,
    UserBatchDeleteRequest,
    UserBatchDeleteResponse
)

# 导出角色相关模型
from svc.apps.auth.schemas.role import (
    RoleProtocol,
    RoleBase,
    RoleCreate,
    RoleUpdate,
    RoleResponse,
    RoleListResponse,
    RoleBatchUpdateRequest,
    RoleBatchUpdateResponse,
    RoleBatchDeleteRequest,
    RoleBatchDeleteResponse
)

# 导出微信相关模型
from svc.apps.auth.schemas.wechat import (
    WxCode2SessionResponse,
    WechatUserBase,
    WechatUserInfo,
    WechatUserResponse,
    WechatLoginRequest,
    WechatBindRequest,
    WechatLoginParams,
    WechatUserUpdateRequest,
    WechatUserBatchUpdateRequest,
    WechatUserBatchUpdateResponse,
    WechatUserBatchDeleteRequest,
    WechatUserBatchDeleteResponse
)

__all__ = [
    # 认证相关
    "AuthCredentials",
    "LoginParams",
    "TokenData",
    "TokenPayload",
    "RefreshTokenRequest",
    "LoginRequest",
    "LoginContext",
    "PasswordResetRequest",
    "PasswordResetParams",
    "PasswordResetResponseData",
    "WechatResponse",
    
    # 用户相关
    "UserProtocol",
    "UserType",
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserWithRoles",
    "GetUserParams",
    "GetUsersParams",
    "CreateUserParams",
    "UpdateUserParams",
    "DeleteUserParams",
    "AssignRoleParams",
    "RemoveRoleParams",
    "UserListResponse",
    "UserBatchUpdateRequest",
    "UserBatchUpdateResponse",
    "UserBatchDeleteRequest",
    "UserBatchDeleteResponse"
    
    # 角色相关
    "RoleProtocol",
    "RoleBase",
    "RoleCreate",
    "RoleUpdate",
    "RoleResponse",
    "RoleListResponse",
    "RoleBatchUpdateRequest",
    "RoleBatchUpdateResponse",
    "RoleBatchDeleteRequest",
    "RoleBatchDeleteResponse"
    
    # 微信相关
    "WxCode2SessionResponse",
    "WechatUserBase",
    "WechatUserInfo",
    "WechatUserResponse",
    "WechatLoginRequest",
    "WechatBindRequest",
    "WechatLoginParams",
    "WechatUserUpdateRequest",
    "WechatUserBatchUpdateRequest",
    "WechatUserBatchUpdateResponse",
    "WechatUserBatchDeleteRequest",
    "WechatUserBatchDeleteResponse"
]
