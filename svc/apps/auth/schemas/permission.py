"""
权限相关的Schema定义
"""
from typing import Optional
from pydantic import BaseModel, Field


class PermissionBase(BaseModel):
    """权限基础Schema"""
    name: str = Field(..., description="权限名称", max_length=100)
    description: Optional[str] = Field(None, description="权限描述", max_length=500)


class PermissionCreate(PermissionBase):
    """创建权限Schema"""
    pass


class PermissionUpdate(BaseModel):
    """更新权限Schema"""
    name: Optional[str] = Field(None, description="权限名称", max_length=100)
    description: Optional[str] = Field(None, description="权限描述", max_length=500)


class PermissionResponse(PermissionBase):
    """权限响应Schema"""
    id: int = Field(..., description="权限ID")
    
    class Config:
        from_attributes = True
