# -------------------- 角色模型 --------------------

from datetime import datetime
from typing import List, Optional, Protocol
from pydantic import BaseModel, ConfigDict, Field, field_validator
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest, BatchDeleteResponse, BatchUpdateResponse
from svc.core.models.base import CamelCaseModel

# 类型定义
class RoleProtocol(Protocol):
    """
    角色模型协议，定义角色对象必须实现的属性和方法
    """
    id: int
    name: str
    description: Optional[str]
    permissions: List[str]

class RoleBase(CamelCaseModel):
    """角色基本信息"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "helmet_manager",
                "description": "头盔产品管理员角色"
            }
        }
    )
    
    name: str = Field(..., max_length=100, description="角色名称")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")


class RoleCreate(RoleBase):
    """角色创建请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "helmet_manager",
                "description": "头盔产品管理员角色",
                "permissions": ["product:read", "product:write", "inventory:read"]
            }
        }
    )
    
    permissions: List[str] = Field([], description="权限名称列表")


class RoleUpdate(CamelCaseModel):
    """角色更新请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "senior_helmet_manager",
                "description": "高级头盔产品管理员角色",
                "permissions": ["product:read", "product:write", "inventory:read", "inventory:write"]
            }
        }
    )
    
    name: Optional[str] = Field(None, max_length=100, description="角色名称")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")
    permissions: Optional[List[str]] = Field(None, description="权限名称列表")


class RoleResponse(RoleBase):
    """角色响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "helmet_manager",
                "description": "头盔产品管理员角色",
                "permissions": ["product:read", "product:write", "inventory:read"],
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00"
            }
        }
    )
    
    id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    permissions: List[str] = Field(default=[], description="权限列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    @field_validator("permissions", mode="before")
    @classmethod
    def convert_permissions_dict_to_list(cls, v):
        """将权限字典转换为列表"""
        if isinstance(v, dict):
            return [k for k, v in v.items() if v]
        return v


class RoleQueryParams(BaseModel):
    """角色查询参数"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
    )
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")
    search_term: str = Field(default="", description="搜索关键词")
    order_by: str = Field(default="created_at", description="排序字段")
    order_direction: str = Field(default="desc", description="排序方向: asc/desc")


class RoleListResponse(PaginatedResponse[RoleResponse]):
    """角色列表响应模型，继承自分页基类"""
    pass # 字段已在 PaginatedResponse 中定义


# === 批量操作Schema ===

class RoleBatchUpdate(BaseModel):
    """角色批量更新模型
    
    定义允许批量更新的字段，仅包含安全的字段
    """
    model_config = ConfigDict(str_strip_whitespace=True)
    
    description: Optional[str] = Field(None, max_length=500, description="角色描述")

class RoleBatchUpdateRequest(BatchUpdateRequest[RoleBatchUpdate]):
    """角色批量更新请求模型
    
    包含要更新的角色ID列表和更新数据
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "description": "更新后的角色描述"
                }
            }
        }
    )

class RoleBatchUpdateResponse(BatchUpdateResponse):
    """角色批量更新响应模型"""
    pass

class RoleBatchDeleteRequest(BatchDeleteRequest):
    """角色批量删除请求模型
    
    包含要删除的角色ID列表
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class RoleBatchDeleteResponse(BatchDeleteResponse):
    """角色批量删除响应模型"""
    pass
