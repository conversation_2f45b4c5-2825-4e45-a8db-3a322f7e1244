"""
计费系统业务规则常量定义

这个模块定义了计费系统中所有可配置的业务规则和常量。
"""

from datetime import timedelta


class SubscriptionRules:
    """订阅相关业务规则"""
    
    # 订阅创建规则
    MAX_SUBSCRIPTIONS_PER_USER = 1  # 每个用户最大订阅数量
    ALLOW_MULTIPLE_DRAFT_SUBSCRIPTIONS = False  # 是否允许多个草稿订阅
    
    # 试用期规则
    DEFAULT_TRIAL_PERIOD_DAYS = 7  # 默认试用期天数
    MAX_TRIAL_PERIOD_DAYS = 30     # 最大试用期天数
    ALLOW_TRIAL_EXTENSION = False   # 是否允许延长试用期
    TRIAL_EXTENSION_MAX_DAYS = 7    # 试用期最大延长天数
    
    # 取消订阅规则
    ALLOW_IMMEDIATE_CANCELLATION = True  # 是否允许立即取消
    CANCELLATION_COOLDOWN_DAYS = 30      # 取消订阅冷却期（天）
    MAX_CANCELLATIONS_PER_MONTH = 3      # 每月最大取消次数
    
    # 计划变更规则
    ALLOW_PLAN_CHANGE_IN_TRIAL = True    # 试用期是否允许变更计划
    ALLOW_DOWNGRADE_IN_FIRST_MONTH = False  # 首月是否允许降级
    PLAN_CHANGE_COOLDOWN_DAYS = 7        # 计划变更冷却期（天）
    
    # 退款规则
    REFUND_WINDOW_DAYS = 30              # 退款窗口期（天）
    ALLOW_PARTIAL_REFUND = True          # 是否允许部分退款
    MIN_REFUND_AMOUNT = 0.01             # 最小退款金额
    
    # 逾期处理规则
    GRACE_PERIOD_DAYS = 3                # 宽限期（天）
    MAX_RETRY_ATTEMPTS = 3               # 最大重试次数
    RETRY_INTERVAL_HOURS = 24            # 重试间隔（小时）
    AUTO_CANCEL_AFTER_DAYS = 30          # 逾期多少天后自动取消


class BillingRules:
    """计费相关业务规则"""
    
    # 计费周期规则
    SUPPORTED_INTERVALS = ["month", "year", "week", "day"]  # 支持的计费周期
    DEFAULT_INTERVAL = "month"                              # 默认计费周期
    
    # 按比例计费规则
    ENABLE_PRORATION = True              # 是否启用按比例计费
    PRORATION_PRECISION = 2              # 按比例计费精度（小数位）
    MIN_PRORATION_AMOUNT = 0.01          # 最小按比例金额
    
    # 账单生成规则
    INVOICE_DUE_DAYS = 7                 # 账单到期天数
    AUTO_GENERATE_INVOICE = True         # 是否自动生成账单
    INVOICE_RETRY_ATTEMPTS = 3           # 账单生成重试次数
    
    # 支付规则
    PAYMENT_TIMEOUT_MINUTES = 30         # 支付超时时间（分钟）
    AUTO_RETRY_FAILED_PAYMENTS = True    # 是否自动重试失败的支付
    PAYMENT_RETRY_INTERVALS = [1, 3, 7]  # 支付重试间隔（天）


class CacheRules:
    """缓存相关规则"""
    
    # 缓存TTL设置
    SUBSCRIPTION_CACHE_TTL = 300         # 订阅缓存TTL（秒）
    PLAN_CACHE_TTL = 3600               # 计划缓存TTL（秒）
    USER_SUBSCRIPTION_CACHE_TTL = 300    # 用户订阅缓存TTL（秒）
    
    # 缓存键前缀
    SUBSCRIPTION_CACHE_PREFIX = "billing:subscription"
    PLAN_CACHE_PREFIX = "billing:plan"
    USER_SUBSCRIPTION_CACHE_PREFIX = "billing:user_subscription"
    INVOICE_CACHE_PREFIX = "billing:invoice"


class ValidationRules:
    """验证相关规则"""
    
    # 字段验证规则
    MIN_PLAN_PRICE = 0.01               # 最小计划价格
    MAX_PLAN_PRICE = 99999.99           # 最大计划价格
    MIN_PLAN_NAME_LENGTH = 2            # 计划名称最小长度
    MAX_PLAN_NAME_LENGTH = 100          # 计划名称最大长度
    
    # 日期验证规则
    MAX_FUTURE_DATE_YEARS = 5           # 最大未来日期年数
    MIN_SUBSCRIPTION_DURATION_DAYS = 1   # 最小订阅持续时间（天）
    
    # 用户验证规则
    REQUIRE_PAYMENT_METHOD = True        # 是否要求支付方式
    REQUIRE_EMAIL_VERIFICATION = False   # 是否要求邮箱验证


class NotificationRules:
    """通知相关规则"""
    
    # 通知时机
    NOTIFY_TRIAL_ENDING_DAYS = [3, 1]    # 试用期结束前通知天数
    NOTIFY_SUBSCRIPTION_EXPIRING_DAYS = [7, 3, 1]  # 订阅到期前通知天数
    NOTIFY_PAYMENT_FAILED = True         # 支付失败是否通知
    NOTIFY_PLAN_CHANGED = True           # 计划变更是否通知
    
    # 通知方式
    EMAIL_NOTIFICATIONS = True           # 是否发送邮件通知
    SMS_NOTIFICATIONS = False            # 是否发送短信通知
    IN_APP_NOTIFICATIONS = True          # 是否发送应用内通知


class PerformanceRules:
    """性能相关规则"""
    
    # 批量操作限制
    MAX_BATCH_SIZE = 100                 # 最大批量操作大小
    BATCH_TIMEOUT_SECONDS = 300          # 批量操作超时时间（秒）
    
    # 查询限制
    MAX_QUERY_LIMIT = 1000              # 最大查询限制
    DEFAULT_PAGE_SIZE = 20               # 默认分页大小
    MAX_PAGE_SIZE = 100                  # 最大分页大小
    
    # 并发控制
    MAX_CONCURRENT_OPERATIONS = 10       # 最大并发操作数
    OPERATION_TIMEOUT_SECONDS = 60       # 操作超时时间（秒）


class AuditRules:
    """审计相关规则"""
    
    # 审计日志
    ENABLE_AUDIT_LOG = True              # 是否启用审计日志
    AUDIT_LOG_RETENTION_DAYS = 365       # 审计日志保留天数
    
    # 敏感操作记录
    LOG_SUBSCRIPTION_CREATION = True     # 记录订阅创建
    LOG_SUBSCRIPTION_CANCELLATION = True # 记录订阅取消
    LOG_PLAN_CHANGES = True              # 记录计划变更
    LOG_PAYMENT_OPERATIONS = True        # 记录支付操作
    LOG_REFUND_OPERATIONS = True         # 记录退款操作
    
    # 数据保护
    MASK_SENSITIVE_DATA = True           # 是否屏蔽敏感数据
    SENSITIVE_FIELDS = [                 # 敏感字段列表
        "payment_method_id",
        "card_number",
        "bank_account"
    ]
