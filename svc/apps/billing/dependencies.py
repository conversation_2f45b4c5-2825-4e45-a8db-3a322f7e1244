"""
依赖注入模块
提供结算服务的依赖项和配置函数。
"""
from fastapi import Depends
from redis import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.repositories import (InvoiceRepository,
                                           PaymentRepository,
                                           SubscriptionPlanRepository,
                                           SubscriptionRepository)
from svc.apps.billing.services.invoice import InvoiceService
from svc.apps.billing.services.payment import PaymentService
from svc.apps.billing.services.subscription import SubscriptionService
from svc.apps.billing.services.subscription_plan import SubscriptionPlanService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route
"""结算依赖项模块（不再负责资源注册）。"""


# 可选：如果有资源类型需要注册到权限系统
def setup_billing_dependencies():
    """
    保留占位：仅用于依赖组装，不做资源注册。
    """
    pass


async def get_subscription_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> SubscriptionRepository:
    """
    提供SubscriptionRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        SubscriptionRepository: 订阅仓库实例
    """
    return SubscriptionRepository(db)


async def get_subscription_plan_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> SubscriptionRepository:
    """
    提供SubscriptionRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        SubscriptionRepository: 订阅仓库实例
    """
    return SubscriptionPlanRepository(db)


async def get_invoice_repository(
        db: AsyncSession = Depends(get_session_for_route)
) -> InvoiceRepository:
    """
    提供BillingRepository实例的依赖
    
    Returns:
        BillingRepository: 计费模块统一仓库实例
    """
    return InvoiceRepository(db)

async def get_payment_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> PaymentRepository:
    """
    提供PaymentRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:
        PaymentRepository: 支付仓库实例
    """
    return PaymentRepository(db)

async def get_subscription_plan_service(
    redis: Redis = Depends(get_redis),
    plan_repo:SubscriptionPlanRepository=Depends(get_subscription_plan_repository)
) -> SubscriptionPlanService:
    """
    提供SubscriptionPlanService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        SubscriptionPlanService: 订阅计划服务实例
    """
    return SubscriptionPlanService(redis,plan_repo=plan_repo)


async def get_subscription_service(
    redis: Redis = Depends(get_redis),
    subscription_repo: SubscriptionRepository = Depends(get_subscription_repository),
    plan_repo:SubscriptionPlanRepository=Depends(get_subscription_plan_repository),
    invoice_repo: InvoiceRepository = Depends(get_invoice_repository)
) -> SubscriptionService:
    """
    提供SubscriptionService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        subscription_repo: 订阅仓库实例
        plan_repo: 订阅计划仓库实例
        invoice_repo: 账单仓库实例
        
    Returns:
        SubscriptionService: 订阅服务实例
    """
    service = SubscriptionService(
        redis=redis,
        subscription_repo=subscription_repo,
        subscription_plan_repo=plan_repo,
        invoice_repo=invoice_repo
    )
    return service


async def get_invoice_service(
    redis: Redis = Depends(get_redis),
    invoice_repo=Depends(get_invoice_repository)
) -> InvoiceService:
    """
    提供InvoiceService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        InvoiceService: 发票服务实例
    """
    return InvoiceService(redis=redis,repository=invoice_repo)


async def get_payment_service(
    redis: Redis = Depends(get_redis),
    payment_repo=Depends(get_payment_repository)
) -> PaymentService:
    """获取支付服务实例
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        PaymentService: 支付服务实例
    """
    return PaymentService(repository=payment_repo, redis=redis)


# 保留为应用启动期显式调用