"""
账单相关的后台任务
"""

import logging
from typing import Any, Dict

from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event

# 依赖项
from svc.apps.billing.dependencies import get_invoice_service, get_subscription_service
from svc.apps.billing.services.invoice import InvoiceService
from svc.apps.billing.services.subscription import SubscriptionService
from svc.core.events.event_names import (BILLING_INVOICE_CREATED,
                                         BILLING_INVOICE_PAID,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_NOTIFICATION_SEND_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)

logger = logging.getLogger(__name__)


@local_handler.register(event_name=BILLING_INVOICE_CREATED)
async def handle_invoice_created(
    event: Event,
    invoice_service: InvoiceService = Depends(get_invoice_service) # Inject if needed
):
    """处理账单创建事件 (本地): 记录审计、更新统计等。"""
    event_name, payload = event
    invoice_id = payload.get('invoice_id')
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id') # 需要从 subscription_id 获取 user_id
    amount = payload.get('amount')

    if not invoice_id:
        logger.error("处理账单创建事件失败: 缺少 invoice_id")
        return

    logger.info(f"[Local Event] 账单已创建: id={invoice_id}, subscription_id={subscription_id}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id, # 需要确定操作者 ID
                "action": "invoice_created",
                "resource_type": "invoice",
                "resource_id": invoice_id,
                "metadata": {
                    "subscription_id": subscription_id,
                    "amount": amount,
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for invoice created {invoice_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新订阅的账单总数
        if subscription_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription",
                    "entity_id": subscription_id,
                    "metric_type": "invoices_total_count",
                    "increment_value": 1,
                    "metadata": {"invoice_id": invoice_id}
                }
            )
        # 更新用户的未支付账单数 (假设创建时是 unpaid)
        if user_id:
             dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "invoices_unpaid_count",
                    "increment_value": 1,
                    "metadata": {"invoice_id": invoice_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for invoice created {invoice_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_INVOICE_PAID)
async def handle_invoice_paid(
    event: Event,
    invoice_service: InvoiceService = Depends(get_invoice_service),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理账单支付成功事件 (本地): 更新订阅状态、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    invoice_id = payload.get('invoice_id')
    payment_id = payload.get('payment_id')
    user_id = payload.get('user_id') # 需要从 invoice_id 获取 user_id
    subscription_id = payload.get('subscription_id') # 需要从 invoice_id 获取
    amount = payload.get('amount')

    if not invoice_id:
        logger.error("处理账单支付成功事件失败: 缺少 invoice_id")
        return

    logger.info(f"[Local Event] 账单已支付: id={invoice_id}, payment_id={payment_id}")

    # 1. 更新订阅状态 (后台任务)
    try:
        if subscription_id:
            await subscription_service._update_subscription_on_payment(subscription_id, paid_invoice_id=invoice_id)
            logger.info(f"订阅状态已更新: subscription_id={subscription_id}, paid_invoice_id={invoice_id}")
    except Exception as e:
        logger.error(f"Failed to update subscription status for invoice paid {invoice_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 减少用户的未支付账单数
        if user_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "invoices_unpaid_count",
                    "increment_value": -1,
                    "metadata": {"invoice_id": invoice_id}
                }
            )
            # 增加用户的已支付账单数
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "invoices_paid_count",
                    "increment_value": 1,
                    "metadata": {"invoice_id": invoice_id}
                }
            )
        # 更新订阅的已支付账单数
        if subscription_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription",
                    "entity_id": subscription_id,
                    "metric_type": "invoices_paid_count",
                    "increment_value": 1,
                    "metadata": {"invoice_id": invoice_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for invoice paid {invoice_id}: {e}", exc_info=True)

    # 3. 发送支付成功通知 (后台任务)
    try:
        if user_id:
            dispatch(
                SYSTEM_NOTIFICATION_SEND_REQUESTED,
                payload={
                    "recipient_user_id": user_id,
                    "title": "账单支付成功",
                    "message": f"您的账单 (ID: {invoice_id}) 已成功支付，金额: {amount}。",
                    "channel": "email" # 或其他渠道
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch notification for invoice paid {invoice_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={
                'resource_type': 'invoice',
                'resource_id': invoice_id
            }
        )
        if user_id:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={
                    'resource_type': 'user_invoices',
                    'user_id': user_id
                }
            )
        if subscription_id:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={
                    'resource_type': 'subscription_invoices',
                    'subscription_id': subscription_id
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for invoice paid {invoice_id}: {e}", exc_info=True)

# 注意: BILLING_SUBSCRIPTION_CANCELED 事件应在 subscription_handlers.py 中处理