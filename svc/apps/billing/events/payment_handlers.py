"""
支付相关的后台任务
"""

import logging
from typing import Any, Dict

from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event

# 依赖项
from svc.apps.billing.dependencies import (get_invoice_service,
                                           get_payment_service)
from svc.apps.billing.services.invoice import InvoiceService
from svc.apps.billing.services.payment import PaymentService
from svc.core.events.event_names import BILLING_INVOICE_PAID  # 支付成功时触发
from svc.core.events.event_names import BILLING_PAYMENT_RECEIVED  # 假设对应支付成功
from svc.core.events.event_names import (BILLING_PAYMENT_CREATED,
                                         BILLING_PAYMENT_FAILED,
                                         BILLING_PAYMENT_REFUNDED,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_NOTIFICATION_SEND_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)

logger = logging.getLogger(__name__)


@local_handler.register(event_name=BILLING_PAYMENT_CREATED)
async def handle_payment_created(
    event: Event,
    payment_service: PaymentService = Depends(get_payment_service) # Inject if needed
):
    """处理支付记录创建事件 (本地): 记录审计、更新统计。"""
    event_name, payload = event
    payment_id = payload.get('payment_id')
    invoice_id = payload.get('invoice_id')
    user_id = payload.get('user_id') # 需要从 invoice_id 获取 user_id
    amount = payload.get('amount')
    status = payload.get('status') # 初始状态，通常是 pending

    if not payment_id:
        logger.error("处理支付记录创建事件失败: 缺少 payment_id")
        return

    logger.info(f"[Local Event] 支付记录已创建: id={payment_id}, invoice_id={invoice_id}, status={status}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id, # 需要确定操作者 ID
                "action": "payment_created",
                "resource_type": "payment",
                "resource_id": payment_id,
                "metadata": {
                    "invoice_id": invoice_id,
                    "amount": amount,
                    "status": status
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for payment created {payment_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新发票的支付尝试次数
        if invoice_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "invoice",
                    "entity_id": invoice_id,
                    "metric_type": "payment_attempts_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id}
                }
            )
        # 更新用户的待处理支付数
        if user_id and status == 'pending':
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_pending_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for payment created {payment_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_PAYMENT_RECEIVED) # 假设 RECEIVED 代表成功
async def handle_payment_succeeded(
    event: Event,
    payment_service: PaymentService = Depends(get_payment_service),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """处理支付成功事件 (本地): 触发账单支付、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    payment_id = payload.get('payment_id')
    invoice_id = payload.get('invoice_id')
    user_id = payload.get('user_id') # 需要从 invoice_id 获取
    amount = payload.get('amount')
    payment_method = payload.get('payment_method')

    if not payment_id or not invoice_id:
        logger.error("处理支付成功事件失败: 缺少 payment_id 或 invoice_id")
        return

    logger.info(f"[Local Event] 支付成功: payment_id={payment_id}, invoice_id={invoice_id}")

    # 1. 触发账单支付事件 (重要!)
    try:
        dispatch(
            BILLING_INVOICE_PAID,
            payload={
                "invoice_id": invoice_id,
                "payment_id": payment_id,
                "user_id": user_id, # 传递 user_id
                "amount": amount,
                "payment_method": payment_method
                # 可能需要传递 subscription_id, 从 invoice 获取
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch invoice paid event for payment {payment_id}: {e}", exc_info=True)
        # Consider retry or marking payment as failed if this dispatch fails critically

    # 2. 更新统计 (后台任务)
    try:
        # 减少用户的待处理支付数
        if user_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_pending_count",
                    "increment_value": -1,
                    "metadata": {"payment_id": payment_id}
                }
            )
            # 增加用户的成功支付数
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_succeeded_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id}
                }
            )
        # 更新发票的成功支付数
        if invoice_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "invoice",
                    "entity_id": invoice_id,
                    "metric_type": "payments_succeeded_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for payment succeeded {payment_id}: {e}", exc_info=True)

    # 3. 发送支付成功通知 (已在 invoice_handlers 处理，此处可选)
    # try:
    #     if user_id:
    #         dispatch(SYSTEM_NOTIFICATION_SEND_REQUESTED, ...)
    # except Exception as e:
    #     logger.error(f"Failed to dispatch notification for payment succeeded {payment_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'payment', 'resource_id': payment_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'invoice', 'resource_id': invoice_id}
        )
        if user_id:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'user_payments', 'user_id': user_id}
            )
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'user_invoices', 'user_id': user_id}
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for payment succeeded {payment_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_PAYMENT_FAILED)
async def handle_payment_failed(
    event: Event,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """处理支付失败事件 (本地): 更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    payment_id = payload.get('payment_id')
    invoice_id = payload.get('invoice_id')
    user_id = payload.get('user_id') # 需要从 invoice_id 获取
    reason = payload.get('reason', 'Unknown error')
    error_code = payload.get('error_code')

    if not payment_id:
        logger.error("处理支付失败事件失败: 缺少 payment_id")
        return

    logger.warning(f"[Local Event] 支付失败: payment_id={payment_id}, invoice_id={invoice_id}, reason={reason}")

    # 1. 更新统计 (后台任务)
    try:
        # 减少用户的待处理支付数
        if user_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_pending_count",
                    "increment_value": -1,
                    "metadata": {"payment_id": payment_id}
                }
            )
            # 增加用户的失败支付数
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_failed_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id, "reason": reason}
                }
            )
        # 更新发票的失败支付数
        if invoice_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "invoice",
                    "entity_id": invoice_id,
                    "metric_type": "payments_failed_count",
                    "increment_value": 1,
                    "metadata": {"payment_id": payment_id, "reason": reason}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for payment failed {payment_id}: {e}", exc_info=True)

    # 2. 发送支付失败通知 (后台任务)
    try:
        if user_id:
            dispatch(
                SYSTEM_NOTIFICATION_SEND_REQUESTED,
                payload={
                    "recipient_user_id": user_id,
                    "title": "支付失败",
                    "message": f"您的支付 (ID: {payment_id}) 失败了。原因: {reason}。请检查您的支付信息或联系客服。",
                    "channel": "email" # 或其他渠道
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch notification for payment failed {payment_id}: {e}", exc_info=True)

    # 3. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'payment', 'resource_id': payment_id}
        )
        # Invoice cache might not need invalidation on failure, depends on logic
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for payment failed {payment_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_PAYMENT_REFUNDED)
async def handle_payment_refunded(
    event: Event,
    payment_service: PaymentService = Depends(get_payment_service)
):
    """处理支付退款事件 (本地): 更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    payment_id = payload.get('payment_id')
    original_payment_id = payload.get('original_payment_id') # ID of the payment being refunded
    refund_id = payload.get('refund_id') # ID of the refund transaction itself
    invoice_id = payload.get('invoice_id') # Invoice associated with original payment
    user_id = payload.get('user_id') # User associated with original payment
    amount = payload.get('amount') # Refund amount
    reason = payload.get('reason')

    if not original_payment_id:
        logger.error("处理支付退款事件失败: 缺少 original_payment_id")
        return

    logger.info(f"[Local Event] 支付已退款: original_payment_id={original_payment_id}, refund_id={refund_id}, amount={amount}")

    # 1. 更新统计 (后台任务)
    try:
        # 增加用户的退款次数/金额
        if user_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_refunded_count",
                    "increment_value": 1,
                    "metadata": {"original_payment_id": original_payment_id, "refund_id": refund_id}
                }
            )
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "payments_refunded_amount",
                    "increment_value": amount, # Assuming amount is numeric
                    "metadata": {"original_payment_id": original_payment_id, "refund_id": refund_id}
                }
            )
        # 更新发票的退款状态/金额 (可能需要更复杂的逻辑)
        # ... depends on how refunds affect invoice status
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for payment refunded {original_payment_id}: {e}", exc_info=True)

    # 2. 发送退款通知 (后台任务)
    try:
        if user_id:
            dispatch(
                SYSTEM_NOTIFICATION_SEND_REQUESTED,
                payload={
                    "recipient_user_id": user_id,
                    "title": "退款处理成功",
                    "message": f"您的支付 (原始ID: {original_payment_id}) 已成功退款 {amount}。退款原因: {reason or 'N/A'}。",
                    "channel": "email"
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch notification for payment refunded {original_payment_id}: {e}", exc_info=True)

    # 3. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'payment', 'resource_id': original_payment_id}
        )
        if refund_id: # If refund has its own record
             dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'payment', 'resource_id': refund_id}
            )
        if invoice_id:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'invoice', 'resource_id': invoice_id}
            )
        if user_id:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'user_payments', 'user_id': user_id}
            )
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'user_invoices', 'user_id': user_id}
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for payment refunded {original_payment_id}: {e}", exc_info=True)