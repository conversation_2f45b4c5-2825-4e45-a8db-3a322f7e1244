"""
订阅相关的后台任务
"""

import logging
from typing import Dict, Any
from fastapi import Depends

from svc.core.events.event_names import (
    BILLING_REFUND_REQUESTED,
    BILLING_SUBSCRIPTION_CREATED,
    BILLING_SUBSCRIPTION_CANCELED,
    BILLING_SUBSCRIPTION_DOWNGRADED,
    BILLING_SUBSCRIPTION_RENEWED,
    BILLING_SUBSCRIPTION_PAUSED,
    BILLING_SUBSCRIPTION_RESUMED,
    BILLING_SUBSCRIPTION_REACTIVATED,
    BILLING_SUBSCRIPTION_PLAN_CHANGED,
    BILLING_SUBSCRIPTION_STATUS_UPDATED,
    BILLING_SUBSCRIPTION_UPGRADED,
    SYSTEM_AUDIT_LOG_RECORDED,
    SYSTEM_STATS_UPDATE_REQUESTED,
    SYSTEM_NOTIFICATION_SEND_REQUESTED,
    SYSTEM_CACHE_INVALIDATION_REQUESTED,
    BILLING_INVOICE_CREATED # 可能在续订或升级时创建
)
from fastapi_events.dispatcher import dispatch
from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event

# 依赖项 (如果需要服务实例)
from svc.apps.billing.dependencies import get_subscription_service
from svc.apps.billing.services.subscription import SubscriptionService

logger = logging.getLogger(__name__)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_CREATED)
async def handle_subscription_created(
    event: Event,
    # subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅创建事件 (本地): 记录审计、更新统计、发送欢迎通知。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    plan_id = payload.get('plan_id')
    status = payload.get('status') # e.g., 'active', 'trialing'
    plan_name = payload.get('plan_name')

    if not subscription_id or not user_id:
        logger.error("处理订阅创建事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已创建: id={subscription_id}, user_id={user_id}, status={status}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id, # 操作者通常是用户自己
                "action": "subscription_created",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "plan_id": plan_id,
                    "status": status,
                    "plan_name": plan_name
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription created {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新用户订阅总数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "subscriptions_total_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id, "plan_id": plan_id}
            }
        )
        # 更新计划的订阅总数
        if plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": plan_id,
                    "metric_type": "subscriptions_active_count", # Or total count?
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
        # 更新系统订阅总数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_total_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        if status == 'active':
             dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_active_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
        elif status == 'trialing':
             dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_trialing_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription created {subscription_id}: {e}", exc_info=True)

    # 3. 发送欢迎/激活通知 (后台任务)
    try:
        title = f"欢迎订阅 {plan_name}!"
        message = f"感谢您订阅 {plan_name}。您的订阅 (ID: {subscription_id}) 已{'激活' if status == 'active' else '开始试用'}。"
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": title,
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch welcome notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription created {subscription_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_CANCELED)
async def handle_subscription_canceled(
    event: Event,
    # subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅取消事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    plan_id = payload.get('plan_id')
    reason = payload.get('reason')
    cancel_at_period_end = payload.get('cancel_at_period_end', False)

    if not subscription_id or not user_id:
        logger.error("处理订阅取消事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已取消: id={subscription_id}, user_id={user_id}, at_period_end={cancel_at_period_end}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id, # 操作者
                "action": "subscription_canceled",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "plan_id": plan_id,
                    "reason": reason,
                    "cancel_at_period_end": cancel_at_period_end
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription canceled {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 减少活跃订阅数 (如果立即取消)
        if not cancel_at_period_end:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_active_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
            # 增加取消订阅数
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_canceled_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
            if plan_id:
                dispatch(
                    SYSTEM_STATS_UPDATE_REQUESTED,
                    payload={
                        "entity_type": "subscription_plan",
                        "entity_id": plan_id,
                        "metric_type": "subscriptions_active_count",
                        "increment_value": -1,
                        "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                    }
                )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription canceled {subscription_id}: {e}", exc_info=True)

    # 3. 发送取消确认通知 (后台任务)
    try:
        title = "订阅已取消"
        message = f"您的订阅 (ID: {subscription_id}) 已被取消。"
        if cancel_at_period_end:
            # 需要获取 period_end 时间
            message += f" 访问权限将持续到当前周期结束。"
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": title,
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cancellation notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription canceled {subscription_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_RENEWED)
async def handle_subscription_renewed(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅续订事件 (本地): 记录审计、更新统计、发送通知、自动生成周期账单。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    plan_id = payload.get('plan_id')
    new_period_start = payload.get('new_period_start')
    new_period_end = payload.get('new_period_end')
    invoice_id = payload.get('invoice_id') # 关联的续订账单 ID

    if not subscription_id or not user_id:
        logger.error("处理订阅续订事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已续订: id={subscription_id}, user_id={user_id}, new_end={new_period_end}")

    # 1. 自动生成周期账单 (后台任务)
    try:
        # 延迟生成周期账单，确保订阅状态已更新
        await subscription_service._generate_periodic_invoice(subscription_id)
        logger.info(f"周期账单生成完成: subscription_id={subscription_id}")
    except Exception as e:
        logger.error(f"Failed to generate periodic invoice for subscription renewed {subscription_id}: {e}", exc_info=True)

    # 2. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": None, # 通常由系统自动触发
                "action": "subscription_renewed",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "user_id": user_id,
                    "plan_id": plan_id,
                    "new_period_start": new_period_start,
                    "new_period_end": new_period_end,
                    "invoice_id": invoice_id
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription renewed {subscription_id}: {e}", exc_info=True)

    # 3. 更新统计 (后台任务)
    try:
        # 更新续订次数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "subscription",
                "entity_id": subscription_id,
                "metric_type": "renewals_count",
                "increment_value": 1,
                "metadata": {"invoice_id": invoice_id}
            }
        )
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "subscription_renewals_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription renewed {subscription_id}: {e}", exc_info=True)

    # 4. 发送续订成功通知 (后台任务)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅续订成功",
                "message": f"您的订阅 (ID: {subscription_id}) 已成功续订，新的有效期至 {new_period_end}。",
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch renewal notification for subscription {subscription_id}: {e}", exc_info=True)

    # 5. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription renewed {subscription_id}: {e}", exc_info=True)

# --- 其他订阅事件处理器 (PAUSED, RESUMED, REACTIVATED, PLAN_CHANGED, UPGRADED) ---
# 实现方式类似，主要关注审计、统计、通知和缓存清理

@local_handler.register(event_name=BILLING_SUBSCRIPTION_PAUSED)
async def handle_subscription_paused(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅暂停事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    pause_reason = payload.get('reason')

    if not subscription_id or not user_id:
        logger.error("处理订阅暂停事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已暂停: id={subscription_id}, user_id={user_id}, reason={pause_reason}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_paused",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {"reason": pause_reason}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription paused {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 减少活跃订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_active_count",
                "increment_value": -1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        # 增加暂停订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_paused_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription paused {subscription_id}: {e}", exc_info=True)

    # 3. 发送暂停通知 (后台任务)
    try:
        message = f"您的订阅 (ID: {subscription_id}) 已被暂停。"
        if pause_reason:
            message += f" 暂停原因：{pause_reason}"
        
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅已暂停",
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch pause notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription paused {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_RESUMED)
async def handle_subscription_resumed(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅恢复事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')

    if not subscription_id or not user_id:
        logger.error("处理订阅恢复事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已恢复: id={subscription_id}, user_id={user_id}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_resumed",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription resumed {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 增加活跃订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_active_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        # 减少暂停订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_paused_count",
                "increment_value": -1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription resumed {subscription_id}: {e}", exc_info=True)

    # 3. 发送恢复通知 (后台任务)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅已恢复",
                "message": f"您的订阅 (ID: {subscription_id}) 已成功恢复，服务已重新激活。",
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch resume notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription resumed {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_REACTIVATED)
async def handle_subscription_reactivated(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅重新激活事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')

    if not subscription_id or not user_id:
        logger.error("处理订阅重新激活事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已重新激活: id={subscription_id}, user_id={user_id}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_reactivated",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription reactivated {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 增加活跃订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_active_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        # 减少取消订阅数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscriptions_canceled_count",
                "increment_value": -1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription reactivated {subscription_id}: {e}", exc_info=True)

    # 3. 发送重新激活通知 (后台任务)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅已重新激活",
                "message": f"您的订阅 (ID: {subscription_id}) 已成功重新激活，欢迎回来！",
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch reactivation notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription reactivated {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_PLAN_CHANGED)
async def handle_subscription_plan_changed(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅计划变更事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    old_plan_id = payload.get('old_plan_id')
    new_plan_id = payload.get('new_plan_id')
    old_plan_name = payload.get('old_plan_name')
    new_plan_name = payload.get('new_plan_name')

    if not subscription_id or not user_id:
        logger.error("处理订阅计划变更事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅计划已变更: id={subscription_id}, {old_plan_name} -> {new_plan_name}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_plan_changed",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "old_plan_id": old_plan_id,
                    "new_plan_id": new_plan_id,
                    "old_plan_name": old_plan_name,
                    "new_plan_name": new_plan_name
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription plan changed {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新计划变更次数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "subscription",
                "entity_id": subscription_id,
                "metric_type": "plan_changes_count",
                "increment_value": 1,
                "metadata": {}
            }
        )
        # 更新计划统计
        if old_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": old_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
        if new_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": new_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription plan changed {subscription_id}: {e}", exc_info=True)

    # 3. 发送计划变更通知 (后台任务)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅计划已变更",
                "message": f"您的订阅计划已成功变更：{old_plan_name} → {new_plan_name}",
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch plan change notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription plan changed {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_UPGRADED)
async def handle_subscription_upgraded(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅升级事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    old_plan_id = payload.get('old_plan_id')
    new_plan_id = payload.get('new_plan_id')
    old_plan_name = payload.get('old_plan_name')
    new_plan_name = payload.get('new_plan_name')
    old_plan_price = payload.get('old_plan_price')
    new_plan_price = payload.get('new_plan_price')
    invoice_id = payload.get('invoice_id') # 差价账单 ID

    if not subscription_id or not user_id:
        logger.error("处理订阅升级事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已升级: id={subscription_id}, user_id={user_id}, {old_plan_name} -> {new_plan_name}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_upgraded",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "old_plan_id": old_plan_id,
                    "new_plan_id": new_plan_id,
                    "old_plan_name": old_plan_name,
                    "new_plan_name": new_plan_name,
                    "old_plan_price": old_plan_price,
                    "new_plan_price": new_plan_price,
                    "invoice_id": invoice_id
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription upgraded {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新升级次数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "subscription",
                "entity_id": subscription_id,
                "metric_type": "upgrades_count",
                "increment_value": 1,
                "metadata": {"invoice_id": invoice_id}
            }
        )
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "subscription_upgrades_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        # 更新计划统计
        if old_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": old_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
        if new_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": new_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription upgraded {subscription_id}: {e}", exc_info=True)

    # 3. 发送升级通知 (后台任务)
    try:
        price_difference = new_plan_price - old_plan_price if new_plan_price and old_plan_price else 0
        
        # 获取当前订阅状态，决定发送什么通知
        subscription = await subscription_service.get_resource_by_id(subscription_id)
        if subscription and subscription.status == "pending":
            # 待支付状态，发送待支付通知
            message = f"您的订阅升级待支付：{old_plan_name} → {new_plan_name}，差价：{price_difference}。请及时支付差价账单以激活新功能。"
            notification_title = "订阅升级待支付"
        else:
            # 已激活状态，发送升级成功通知
            message = f"您的订阅已成功升级：{old_plan_name} → {new_plan_name}"
            notification_title = "订阅升级成功"
        if price_difference > 0:
            message += f"，差价：{price_difference} 元"
        if invoice_id:
            message += f"，差价账单已生成（ID: {invoice_id}）"
        
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": notification_title,
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch upgrade notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription upgraded {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_DOWNGRADED)
async def handle_subscription_downgraded(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅降级事件 (本地): 记录审计、更新统计、发送通知、清理缓存。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    old_plan_id = payload.get('old_plan_id')
    new_plan_id = payload.get('new_plan_id')
    old_plan_name = payload.get('old_plan_name')
    new_plan_name = payload.get('new_plan_name')
    old_plan_price = payload.get('old_plan_price')
    new_plan_price = payload.get('new_plan_price')
    effective_date = payload.get('effective_date')
    refund_amount = payload.get('refund_amount')

    if not subscription_id or not user_id:
        logger.error("处理订阅降级事件失败: 缺少 subscription_id 或 user_id")
        return

    logger.info(f"[Local Event] 订阅已降级: id={subscription_id}, user_id={user_id}, {old_plan_name} -> {new_plan_name}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": user_id,
                "action": "subscription_downgraded",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "old_plan_id": old_plan_id,
                    "new_plan_id": new_plan_id,
                    "old_plan_name": old_plan_name,
                    "new_plan_name": new_plan_name,
                    "old_plan_price": old_plan_price,
                    "new_plan_price": new_plan_price,
                    "effective_date": effective_date,
                    "refund_amount": refund_amount
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription downgraded {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新降级次数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "subscription",
                "entity_id": subscription_id,
                "metric_type": "downgrades_count",
                "increment_value": 1,
                "metadata": {"refund_amount": refund_amount}
            }
        )
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "subscription_downgrades_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        # 更新计划统计
        if old_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": old_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
        if new_plan_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "subscription_plan",
                    "entity_id": new_plan_id,
                    "metric_type": "subscriptions_active_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id, "user_id": user_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription downgraded {subscription_id}: {e}", exc_info=True)

    # 3. 发送降级成功通知 (后台任务)
    try:
        price_difference = old_plan_price - new_plan_price if old_plan_price and new_plan_price else 0
        message = f"您的订阅已成功降级：{old_plan_name} → {new_plan_name}"
        if price_difference > 0:
            message += f"，退款金额：{price_difference} 元"
        if effective_date:
            message += f"，生效时间：{effective_date}"
        
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅降级成功",
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch downgrade notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription downgraded {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_REFUND_REQUESTED)
async def handle_refund_requested(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理退款请求事件 (本地): 记录审计、更新统计、发送通知。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    refund_amount = payload.get('refund_amount')
    currency = payload.get('currency')
    description = payload.get('description')
    meta_data = payload.get('meta_data', {})

    if not subscription_id or not refund_amount:
        logger.error("处理退款请求事件失败: 缺少 subscription_id 或 refund_amount")
        return

    logger.info(f"[Local Event] 退款请求: subscription_id={subscription_id}, amount={refund_amount} {currency}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": None,  # 系统自动触发
                "action": "refund_requested",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "refund_amount": refund_amount,
                    "currency": currency,
                    "description": description,
                    "meta_data": meta_data
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for refund requested {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 更新退款统计
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "refunds_requested_count",
                "increment_value": 1,
                "metadata": {"subscription_id": subscription_id}
            }
        )
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "refunds_requested_amount",
                "increment_value": refund_amount,
                "metadata": {"subscription_id": subscription_id, "currency": currency}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for refund requested {subscription_id}: {e}", exc_info=True)

    # 3. 发送退款通知 (后台任务)
    try:
        # 获取用户ID（从订阅信息中）
        user_id = meta_data.get('user_id')
        if user_id:
            dispatch(
                SYSTEM_NOTIFICATION_SEND_REQUESTED,
                payload={
                    "recipient_user_id": user_id,
                    "title": "退款申请已提交",
                    "message": f"您的退款申请已提交，金额：{refund_amount} {currency}。我们将在3-5个工作日内处理完成。",
                    "channel": "email"
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch refund notification for subscription {subscription_id}: {e}", exc_info=True)

@local_handler.register(event_name=BILLING_SUBSCRIPTION_STATUS_UPDATED)
async def handle_subscription_status_updated(
    event: Event,
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """处理订阅状态更新事件 (本地): 记录审计、更新统计、发送通知。"""
    event_name, payload = event
    subscription_id = payload.get('subscription_id')
    user_id = payload.get('user_id')
    old_status = payload.get('old_status')
    new_status = payload.get('new_status')
    updated_at = payload.get('updated_at')

    if not subscription_id or not user_id or not old_status or not new_status:
        logger.error("处理订阅状态更新事件失败: 缺少必要参数")
        return

    logger.info(f"[Local Event] 订阅状态已更新: id={subscription_id}, user_id={user_id}, {old_status} -> {new_status}")

    # 1. 记录审计日志 (后台任务)
    try:
        dispatch(
            SYSTEM_AUDIT_LOG_RECORDED,
            payload={
                "user_id": None,  # 系统自动触发
                "action": "subscription_status_updated",
                "resource_type": "subscription",
                "resource_id": subscription_id,
                "metadata": {
                    "user_id": user_id,
                    "old_status": old_status,
                    "new_status": new_status,
                    "updated_at": updated_at
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch audit log for subscription status updated {subscription_id}: {e}", exc_info=True)

    # 2. 更新统计 (后台任务)
    try:
        # 减少旧状态的统计
        if old_status == "active":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_active_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
        elif old_status == "past_due":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_past_due_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
        elif old_status == "trialing":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_trialing_count",
                    "increment_value": -1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )

        # 增加新状态的统计
        if new_status == "active":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_active_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
        elif new_status == "past_due":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_past_due_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
        elif new_status == "trialing":
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": "subscriptions_trialing_count",
                    "increment_value": 1,
                    "metadata": {"subscription_id": subscription_id}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for subscription status updated {subscription_id}: {e}", exc_info=True)

    # 3. 发送状态更新通知 (后台任务)
    try:
        status_messages = {
            "active": "您的订阅已激活，服务已恢复正常。",
            "past_due": "您的订阅已逾期，请及时支付账单以恢复服务。",
            "trialing": "您的订阅已进入试用期，请及时支付以继续使用服务。"
        }
        
        message = status_messages.get(new_status, f"您的订阅状态已更新为：{new_status}")
        
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_user_id": user_id,
                "title": "订阅状态更新",
                "message": message,
                "channel": "email"
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch status update notification for subscription {subscription_id}: {e}", exc_info=True)

    # 4. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription', 'resource_id': subscription_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'user_subscriptions', 'user_id': user_id}
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for subscription status updated {subscription_id}: {e}", exc_info=True)