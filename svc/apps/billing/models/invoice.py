import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import (BigInteger, Boolean, Column, DateTime, Float,
                        ForeignKey, String)
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class Invoice(Base, ResourceMixin):
    """账单模型"""
    __tablename__ = "invoices"
    __resource_type__ = "invoice"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    subscription_id = Column(BigInteger, ForeignKey("subscriptions.id", ondelete="CASCADE"), nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String, nullable=False, default="CNY")
    status = Column(String, nullable=False, default="pending")  # pending, paid, failed, canceled
    description = Column(String, nullable=True)
    invoice_number = Column(String, nullable=True, unique=True)
    invoice_date = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    due_date = Column(DateTime, nullable=True)
    paid_at = Column(DateTime, nullable=True)
    is_paid = Column(Boolean, default=False)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    subscription = relationship("Subscription", back_populates="invoices")
    payments = relationship("Payment", back_populates="invoice")
    refunds = relationship("Refund", back_populates="invoice")