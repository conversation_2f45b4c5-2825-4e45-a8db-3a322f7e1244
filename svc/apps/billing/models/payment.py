import uuid
from typing import Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, DateTime, ForeignKey, Float, BigInteger
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.core.models.resource_mixin import ResourceMixin


class Payment(Base, ResourceMixin):
    """支付记录模型"""
    __tablename__ = "payments"
    __resource_type__ = "payment"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    invoice_id = Column(BigInteger, ForeignKey("invoices.id"), nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String, nullable=False, default="CNY")
    payment_method = Column(String, nullable=False)  # alipay, wechat, bank_transfer
    payment_id = Column(String, nullable=True)  # 支付平台的交易ID
    status = Column(String, nullable=False, default="pending")  # pending, succeeded, failed
    error_message = Column(String, nullable=True)
    paid_at = Column(DateTime, nullable=True)
    refunded_at = Column(DateTime, nullable=True)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    invoice = relationship("Invoice", back_populates="payments")
    