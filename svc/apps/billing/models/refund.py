"""
退款模型

定义退款相关的数据结构和业务逻辑
"""

import uuid
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum

from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Boolean, BigInteger
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.core.models.resource_mixin import ResourceMixin


class RefundStatus(str, Enum):
    """退款状态枚举"""
    PENDING = "pending"       # 待处理
    PROCESSING = "processing" # 处理中
    PROCESSED = "processed"   # 已处理
    FAILED = "failed"         # 失败
    CANCELED = "canceled"     # 已取消


class RefundType(str, Enum):
    """退款类型枚举"""
    CANCELLATION = "cancellation"   # 取消订阅退款
    DOWNGRADE = "downgrade"         # 降级退款
    DISPUTE = "dispute"             # 争议退款
    OVERPAYMENT = "overpayment"     # 多付退款
    GOODWILL = "goodwill"           # 善意退款


class Refund(Base, ResourceMixin):
    """退款模型
    
    记录所有退款信息，包括退款金额、状态、类型等
    """
    __tablename__ = "refunds"
    __resource_type__ = "refund"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # 关联信息
    subscription_id = Column(BigInteger, ForeignKey("subscriptions.id", ondelete="CASCADE"), nullable=False)
    invoice_id = Column(BigInteger, ForeignKey("invoices.id", ondelete="SET NULL"), nullable=True)  # 关联原始账单
    user_id = Column(BigInteger, nullable=False)  # 冗余字段，便于查询
    
    # 退款基本信息
    amount = Column(Float, nullable=False)  # 退款金额
    currency = Column(String, nullable=False, default="CNY")  # 货币类型
    status = Column(String, nullable=False, default=RefundStatus.PENDING.value)  # 退款状态
    refund_type = Column(String, nullable=False)  # 退款类型
    
    # 描述信息
    reason = Column(String, nullable=True)  # 退款原因
    description = Column(String, nullable=True)  # 退款描述
    
    # 时间信息
    refund_date = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)  # 退款申请日期
    processed_at = Column(DateTime, nullable=True)  # 处理完成时间
    
    # 处理信息
    processor_id = Column(BigInteger, nullable=True)  # 处理人ID（管理员）
    processing_notes = Column(String, nullable=True)  # 处理备注
    
    # 外部信息
    external_refund_id = Column(String, nullable=True)  # 外部退款ID（支付网关）
    payment_method = Column(String, nullable=True)  # 退款方式
    
    # 元数据
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    
    # 审计字段
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    subscription = relationship("Subscription", back_populates="refunds")
    invoice = relationship("Invoice", back_populates="refunds")
    
    def __repr__(self):
        return f"<Refund(id={self.id}, subscription_id={self.subscription_id}, amount={self.amount}, status={self.status})>"
    
    @property
    def is_pending(self) -> bool:
        """是否为待处理状态"""
        return self.status == RefundStatus.PENDING.value
    
    @property
    def is_processed(self) -> bool:
        """是否已处理完成"""
        return self.status == RefundStatus.PROCESSED.value
    
    @property
    def is_failed(self) -> bool:
        """是否处理失败"""
        return self.status == RefundStatus.FAILED.value
    
    @property
    def can_be_canceled(self) -> bool:
        """是否可以取消"""
        return self.status in [RefundStatus.PENDING.value, RefundStatus.PROCESSING.value]
    
    @property
    def processing_time_days(self) -> Optional[int]:
        """处理耗时（天数）"""
        if not self.processed_at:
            return None
        return (self.processed_at - self.refund_date).days
    
    def get_display_status(self) -> str:
        """获取显示用的状态文本"""
        status_map = {
            RefundStatus.PENDING.value: "待处理",
            RefundStatus.PROCESSING.value: "处理中",
            RefundStatus.PROCESSED.value: "已完成",
            RefundStatus.FAILED.value: "失败",
            RefundStatus.CANCELED.value: "已取消"
        }
        return status_map.get(self.status, self.status)
    
    def get_display_type(self) -> str:
        """获取显示用的类型文本"""
        type_map = {
            RefundType.CANCELLATION.value: "取消订阅退款",
            RefundType.DOWNGRADE.value: "降级退款",
            RefundType.DISPUTE.value: "争议退款",
            RefundType.OVERPAYMENT.value: "多付退款",
            RefundType.GOODWILL.value: "善意退款"
        }
        return type_map.get(self.refund_type, self.refund_type)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "subscription_id": self.subscription_id,
            "invoice_id": self.invoice_id,
            "user_id": self.user_id,
            "amount": self.amount,
            "currency": self.currency,
            "status": self.status,
            "refund_type": self.refund_type,
            "reason": self.reason,
            "description": self.description,
            "refund_date": self.refund_date.isoformat() if self.refund_date else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "processor_id": self.processor_id,
            "processing_notes": self.processing_notes,
            "external_refund_id": self.external_refund_id,
            "payment_method": self.payment_method,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "display_status": self.get_display_status(),
            "display_type": self.get_display_type(),
            "is_pending": self.is_pending,
            "is_processed": self.is_processed,
            "is_failed": self.is_failed,
            "can_be_canceled": self.can_be_canceled,
            "processing_time_days": self.processing_time_days
        }
