"""订阅模块的数据模型。

该模块定义了订阅相关的数据模型，包括：
- 订阅基本信息
- 订阅状态管理
- 订阅周期控制
- 计费相关属性

技术说明：
- 使用BigInteger自增ID作为主键
- 包含软删除支持
- 支持元数据扩展
- 包含审计字段(created_at, updated_at)
"""

from datetime import timedelta
# 第三方库导入
from enum import Enum
# 标准库导入
from typing import Any, Dict, Optional

# 第三方库导入
from sqlalchemy import (BigInteger, Boolean, Column, DateTime, ForeignKey,
                        Integer, String)
from sqlalchemy.orm import relationship

# 项目内部导入
from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class SubscriptionStatus(str, Enum):
    """订阅状态枚举

    定义订阅在整个生命周期中的所有可能状态
    """
    DRAFT = "draft"           # 草稿状态，刚创建但未激活
    TRIALING = "trialing"     # 试用期状态
    ACTIVE = "active"         # 活跃状态，正常计费
    PAST_DUE = "past_due"     # 逾期状态，等待支付
    CANCELED = "canceled"     # 已取消，但可能还在服务期内
    EXPIRED = "expired"       # 已过期，服务已停止

    @classmethod
    def get_active_statuses(cls) -> list['SubscriptionStatus']:
        """获取所有活跃状态（用户可以使用服务的状态）"""
        return [cls.TRIALING, cls.ACTIVE, cls.PAST_DUE]

    @classmethod
    def get_billable_statuses(cls) -> list['SubscriptionStatus']:
        """获取所有可计费状态"""
        return [cls.ACTIVE, cls.PAST_DUE]

    @classmethod
    def get_terminal_statuses(cls) -> list['SubscriptionStatus']:
        """获取所有终态状态（不能再转换的状态）"""
        return [cls.EXPIRED]


class SubscriptionEvent(str, Enum):
    """订阅事件枚举

    定义所有可能触发订阅状态转换的事件
    """
    CREATE = "create"                 # 创建订阅
    ACTIVATE = "activate"             # 激活订阅
    TRIAL_START = "trial_start"       # 开始试用
    TRIAL_END = "trial_end"           # 试用结束
    PAYMENT_SUCCESS = "payment_success"  # 支付成功
    PAYMENT_FAILED = "payment_failed"    # 支付失败
    CANCEL = "cancel"                 # 取消订阅
    EXPIRE = "expire"                 # 订阅过期
    UPGRADE = "upgrade"               # 升级计划
    DOWNGRADE = "downgrade"           # 降级计划
    RENEW = "renew"                   # 续费
    SUSPEND = "suspend"               # 暂停（逾期等原因）
    REACTIVATE = "reactivate"         # 重新激活


class PlanChangeType(str, Enum):
    """计划变更类型枚举"""
    UPGRADE = "upgrade"       # 升级到更高价格的计划
    DOWNGRADE = "downgrade"   # 降级到更低价格的计划
    LATERAL = "lateral"       # 同价格计划间的变更


class BillingInterval(str, Enum):
    """计费周期枚举"""
    MONTH = "month"
    YEAR = "year"
    WEEK = "week"
    DAY = "day"


class DowngradeType(str, Enum):
    """降级类型枚举"""
    IMMEDIATE = "immediate"           # 立即降级
    END_OF_PERIOD = "end_of_period"   # 周期结束降级


class Subscription(Base, ResourceMixin):
    """订阅模型类。
    
    该模型用于管理用户的订阅信息，包括：
    - 订阅计划关联
    - 订阅状态跟踪
    - 试用期管理
    - 计费周期控制
    - 取消策略支持
    
    属性:
        id (BigInteger): 订阅唯一标识
        user_id (int): 用户ID
        plan_id (BigInteger): 订阅计划ID
        status (str): 订阅状态，可选值：draft, trialing, active, past_due, canceled, expired
        current_period_start (datetime): 当前计费周期开始时间
        current_period_end (datetime): 当前计费周期结束时间
        cancel_at_period_end (bool): 是否在周期结束时取消
        canceled_at (datetime): 取消时间
        trial_start (datetime): 试用期开始时间
        trial_end (datetime): 试用期结束时间
        quantity (int): 订阅数量
        meta_data (dict): 扩展元数据
        created_at (datetime): 创建时间
        updated_at (datetime): 更新时间
    
    关系:
        plan: 关联的订阅计划
        invoices: 关联的账单列表
    """
    
    __tablename__ = "subscriptions"
    __resource_type__ = "subscription"
    
    # 基本字段
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, index=True)
    plan_id = Column(BigInteger, ForeignKey("subscription_plans.id"), nullable=False)
    status = Column(String, nullable=False, default="active")
    current_period_start = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    current_period_end = Column(DateTime, nullable=False)
    cancel_at_period_end = Column(Boolean, default=False)
    canceled_at = Column(DateTime, nullable=True)
    trial_start = Column(DateTime, nullable=True)
    trial_end = Column(DateTime, nullable=True)
    quantity = Column(Integer, nullable=False, default=1)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    
    # 审计字段
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系定义
    plan = relationship("SubscriptionPlan", back_populates="subscriptions", lazy="selectin")
    invoices = relationship("Invoice", back_populates="subscription", lazy="dynamic")
    refunds = relationship("Refund", back_populates="subscription", cascade="all, delete-orphan")
