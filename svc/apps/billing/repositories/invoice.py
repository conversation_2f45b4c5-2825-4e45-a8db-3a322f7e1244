"""
账单数据访问层。
负责账单模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.billing.models.invoice import Invoice
from svc.apps.billing.schemas.invoice import InvoiceCreate, InvoiceUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class InvoiceRepository(BaseRepository[Invoice, InvoiceCreate, InvoiceUpdate]):
    """账单仓库类，提供账单相关的数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化账单仓库"""
        super().__init__(db=db,model=Invoice)

    def _get_default_load_options(self) -> List[Any]:
        """获取账单模型的默认关系加载选项
        
        默认加载：
        - subscription: 关联的订阅（selectin关系）
        - payments: 支付记录（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Invoice.subscription),
            selectinload(Invoice.payments)
        ]
    

    

    

    

    

    

    
    async def get_by_subscription(
        self, 
        subscription_id: int, 
        page_num: int = 1,
        page_size: int = 20
    ) -> Tuple[List[Invoice], int]:
        """
        通过订阅ID获取账单列表（分页）
        
        Args:
            subscription_id: 订阅ID
            page_num: 页码
            page_size: 每页记录数
            
        Returns:
            Tuple[List[Invoice], int]: 账单列表和总数
        """
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            subscription_id=subscription_id
        )
        return items, total
    
    async def get_overdue_invoices(
        self, 
        page_num: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Invoice], int]:
        """
        获取逾期未付款的账单（分页）
        
        Args:
            page_num: 页码
            page_size: 每页记录数

        Returns:
            Tuple[List[Invoice], int]: 逾期账单列表和总数
        """
        now = get_utc_now_without_tzinfo()
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            filters={"is_paid": False, "status": "pending", "due_date__is_not_null": True, "due_date__lt": now}
        )
        return items, total
    
    async def mark_as_overdue(self, invoice_id: int) -> Optional[Invoice]:
        """
        标记为已逾期
        
        Args:
            invoice_id: 账单ID
            
        Returns:
            Optional[Invoice]: 更新后的账单对象，不存在则返回None
        """
        invoice = await self.get_by_id(invoice_id)
        if not invoice:
            return None
            
        if not invoice.is_paid and invoice.status == "pending":
            invoice = await self.update(invoice, {"status": "overdue"})
            
            # 如果关联的订阅存在，将其状态更新为past_due
            if invoice.subscription:
                # 关联更新交由服务层处理
                pass
        return invoice
    
    async def check_and_mark_overdue(self) -> List[Invoice]:
        """
        检查并标记逾期账单，返回新标记的逾期账单列表
        
        Args:
            无

        Returns:
            List[Invoice]: 新标记的逾期账单列表
        """
        overdue_invoices = await self.get_overdue_invoices()
        marked_invoices = []
        
        for invoice in overdue_invoices:
            await self.mark_as_overdue(invoice.id)
            marked_invoices.append(invoice)
            
        return marked_invoices
    
    async def update_invoice_metadata(self, invoice_id: int, metadata: Dict[str, Any]) -> Optional[Invoice]:
        """
        更新账单元数据
        
        Args:
            invoice_id: 账单ID
            metadata: 新元数据
            
        Returns:
            Optional[Invoice]: 更新后的账单对象，不存在则返回None
        """
        invoice = await self.get_by_id(invoice_id)
        if not invoice:
            return None
        
        # 合并原有元数据和新元数据
        current_metadata = invoice.meta_data or {}
        updated_metadata = {**current_metadata, **metadata}
        
        data = {"meta_data": updated_metadata}
        return await self.update(invoice, data)
    
    async def send_reminder(self, invoice_id: int) -> Optional[Invoice]:
        """
        发送账单提醒（更新提醒元数据）
        
        Args:
            invoice_id: 账单ID
            
        Returns:
            Optional[Invoice]: 更新后的账单对象，不存在则返回None
        """
        invoice = await self.get_by_id(invoice_id)
        if not invoice:
            return None
        
        # 更新最后提醒发送时间
        current_metadata = invoice.meta_data or {}
        current_metadata["last_reminder_sent"] = get_utc_now_without_tzinfo().isoformat()
        current_metadata["reminder_count"] = current_metadata.get("reminder_count", 0) + 1
        
        data = {"meta_data": current_metadata}
        return await self.update(invoice, data)