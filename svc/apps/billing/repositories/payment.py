"""
支付数据访问层。
负责支付模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.billing.models.payment import Payment
from svc.apps.billing.schemas.payment import PaymentCreate, PaymentUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class PaymentRepository(BaseRepository[Payment, PaymentCreate, PaymentUpdate]):
    """支付仓库类，提供支付相关的数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化支付仓库"""
        super().__init__(db=db,model=Payment)

    def _get_default_load_options(self) -> List[Any]:
        """获取支付模型的默认关系加载选项
        
        默认加载：
        - invoice: 关联的账单（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Payment.invoice)
        ]

    
    async def get_payment_stats(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        获取支付统计数据
        
        Args:
            db: 数据库会话
            start_date: 开始时间
            end_date: 结束时间
            
        Returns:
            Dict[str, Any]: 统计数据字典
        """
        conditions = []
        
        if start_date:
            conditions.append(self.model.created_at >= start_date)
        if end_date:
            conditions.append(self.model.created_at <= end_date)
        
        base_filters = {}
        if start_date:
            base_filters["created_at__gte"] = start_date
        if end_date:
            base_filters["created_at__lte"] = end_date

        total_count = await self.count(filters=base_filters or None)
        success_count = await self.count(filters={**base_filters, "status": "succeeded"})
        failed_count = await self.count(filters={**base_filters, "status": "failed"})

        # 若需要金额汇总，保留一次性原生求和；简化为调用 get_list 后在服务层聚合可选
        # 这里改为零额外 SQL：返回 0 作为占位，建议服务层做聚合
        total_amount = 0

        return {
            "total_count": total_count,
            "success_count": success_count,
            "failed_count": failed_count,
            "total_amount": total_amount,
            "success_rate": (success_count / total_count * 100) if total_count > 0 else 0
        }
    
    async def get_payments(
        self,
        *,
        invoice_id: Optional[int] = None,
        user_id: Optional[int] = None, # Assuming User relationship is needed for filtering
        status: Optional[str] = None,
        page_num: int = 1,
        page_size: int = 100
    ) -> Tuple[List[Payment], int]:
        """
        获取支付记录列表，支持过滤和分页
        
        Args:
            invoice_id: 账单ID (可选)
            user_id: 用户ID (可选, 用于权限或过滤)
            status: 支付状态 (可选)
            page_num: 页码
            page_size: 每页记录数
            
        Returns:
            Tuple[List[Payment], int]: 支付记录列表和总记录数
        """
        simple_filters = {}
        if invoice_id is not None:
            simple_filters["invoice_id"] = invoice_id
        if status is not None:
            simple_filters["status"] = status

        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by="created_at",
            order_direction="desc",
            **simple_filters,
        )
        return items, (total or 0)