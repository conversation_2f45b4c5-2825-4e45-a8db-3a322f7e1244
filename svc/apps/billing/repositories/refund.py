"""
退款仓库

处理退款数据的持久化操作
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, asc, desc, or_
from sqlalchemy.orm import selectinload

from svc.core.repositories.base import BaseRepository

from ..models.refund import Refund, RefundStatus, RefundType


class RefundRepository(BaseRepository[Refund,Any,Any]):
    """退款仓库类"""
    
    def __init__(self, session):
        super().__init__(session, Refund)
    
    async def get_by_subscription(self, subscription_id: int, 
                                 status: Optional[str] = None) -> Tuple[List[Refund], int]:
        """根据订阅ID获取退款记录
        
        Args:
            subscription_id: 订阅ID
            status: 退款状态过滤
            
        Returns:
            Tuple[List[Refund], int]: (退款列表, 总数)
        """
        query = self.session.query(Refund).filter(
            Refund.subscription_id == subscription_id
        )
        
        if status:
            query = query.filter(Refund.status == status)
        
        query = query.order_by(desc(Refund.created_at))
        
        refunds = query.all()
        return refunds, len(refunds)
    
    async def get_by_user(self, user_id: int, 
                         page_num: int = 1, 
                         page_size: int = 20,
                         status: Optional[str] = None,
                         refund_type: Optional[str] = None) -> Tuple[List[Refund], int]:
        """根据用户ID获取退款记录
        
        Args:
            user_id: 用户ID
            page_num: 页码
            page_size: 每页大小
            status: 退款状态过滤
            refund_type: 退款类型过滤
            
        Returns:
            Tuple[List[Refund], int]: (退款列表, 总数)
        """
        query = self.session.query(Refund).filter(
            Refund.user_id == user_id
        )
        
        if status:
            query = query.filter(Refund.status == status)
        
        if refund_type:
            query = query.filter(Refund.refund_type == refund_type)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        query = query.order_by(desc(Refund.created_at))
        offset = (page_num - 1) * page_size
        refunds = query.offset(offset).limit(page_size).all()
        
        return refunds, total
    
    async def get_pending_refunds(self, limit: Optional[int] = None) -> List[Refund]:
        """获取待处理的退款记录
        
        Args:
            limit: 限制数量
            
        Returns:
            List[Refund]: 待处理退款列表
        """
        query = self.session.query(Refund).filter(
            Refund.status == RefundStatus.PENDING.value
        ).order_by(asc(Refund.created_at))
        
        if limit:
            query = query.limit(limit)
        
        return query.all()
    
    async def get_by_invoice(self, invoice_id: int) -> List[Refund]:
        """根据账单ID获取退款记录
        
        Args:
            invoice_id: 账单ID
            
        Returns:
            List[Refund]: 退款列表
        """
        return self.session.query(Refund).filter(
            Refund.invoice_id == invoice_id
        ).order_by(desc(Refund.created_at)).all()
    
    async def get_by_external_id(self, external_refund_id: str) -> Optional[Refund]:
        """根据外部退款ID获取退款记录
        
        Args:
            external_refund_id: 外部退款ID
            
        Returns:
            Optional[Refund]: 退款记录
        """
        return self.session.query(Refund).filter(
            Refund.external_refund_id == external_refund_id
        ).first()
    
    async def get_refund_statistics(self, user_id: Optional[int] = None,
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取退款统计信息
        
        Args:
            user_id: 用户ID（可选）
            start_date: 开始日期（可选）
            end_date: 结束日期（可选）
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        query = self.session.query(Refund)
        
        if user_id:
            query = query.filter(Refund.user_id == user_id)
        
        if start_date:
            query = query.filter(Refund.refund_date >= start_date)
        
        if end_date:
            query = query.filter(Refund.refund_date <= end_date)
        
        refunds = query.all()
        
        # 计算统计信息
        total_count = len(refunds)
        total_amount = sum(refund.amount for refund in refunds)
        
        # 按状态统计
        status_stats = {}
        for status in RefundStatus:
            status_refunds = [r for r in refunds if r.status == status.value]
            status_stats[status.value] = {
                "count": len(status_refunds),
                "amount": sum(r.amount for r in status_refunds)
            }
        
        # 按类型统计
        type_stats = {}
        for refund_type in RefundType:
            type_refunds = [r for r in refunds if r.refund_type == refund_type.value]
            type_stats[refund_type.value] = {
                "count": len(type_refunds),
                "amount": sum(r.amount for r in type_refunds)
            }
        
        return {
            "total_count": total_count,
            "total_amount": total_amount,
            "status_statistics": status_stats,
            "type_statistics": type_stats,
            "processed_count": len([r for r in refunds if r.is_processed]),
            "pending_count": len([r for r in refunds if r.is_pending]),
            "failed_count": len([r for r in refunds if r.is_failed])
        }
    
    async def update_status(self, refund_id: int, status: str, 
                           processor_id: Optional[int] = None,
                           processing_notes: Optional[str] = None,
                           external_refund_id: Optional[str] = None) -> Optional[Refund]:
        """更新退款状态
        
        Args:
            refund_id: 退款ID
            status: 新状态
            processor_id: 处理人ID
            processing_notes: 处理备注
            external_refund_id: 外部退款ID
            
        Returns:
            Optional[Refund]: 更新后的退款记录
        """
        refund = await self.get_by_id(refund_id)
        if not refund:
            return None
        
        update_data = {"status": status}
        
        if processor_id:
            update_data["processor_id"] = processor_id
        
        if processing_notes:
            update_data["processing_notes"] = processing_notes
        
        if external_refund_id:
            update_data["external_refund_id"] = external_refund_id
        
        # 如果状态变为已处理，设置处理时间
        if status == RefundStatus.PROCESSED.value:
            from svc.core.utils.datetime_utils import \
                get_utc_now_without_tzinfo
            update_data["processed_at"] = get_utc_now_without_tzinfo()
        
        return await self.update(refund, update_data)
    
    async def search_refunds(self, 
                           keyword: Optional[str] = None,
                           status: Optional[str] = None,
                           refund_type: Optional[str] = None,
                           user_id: Optional[int] = None,
                           subscription_id: Optional[int] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           page_num: int = 1,
                           page_size: int = 20) -> Tuple[List[Refund], int]:
        """搜索退款记录
        
        Args:
            keyword: 关键词搜索（描述、原因等）
            status: 退款状态
            refund_type: 退款类型
            user_id: 用户ID
            subscription_id: 订阅ID
            start_date: 开始日期
            end_date: 结束日期
            page_num: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[List[Refund], int]: (退款列表, 总数)
        """
        query = self.session.query(Refund)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    Refund.description.ilike(f"%{keyword}%"),
                    Refund.reason.ilike(f"%{keyword}%"),
                    Refund.processing_notes.ilike(f"%{keyword}%")
                )
            )
        
        # 状态过滤
        if status:
            query = query.filter(Refund.status == status)
        
        # 类型过滤
        if refund_type:
            query = query.filter(Refund.refund_type == refund_type)
        
        # 用户过滤
        if user_id:
            query = query.filter(Refund.user_id == user_id)
        
        # 订阅过滤
        if subscription_id:
            query = query.filter(Refund.subscription_id == subscription_id)
        
        # 日期范围过滤
        if start_date:
            query = query.filter(Refund.refund_date >= start_date)
        
        if end_date:
            query = query.filter(Refund.refund_date <= end_date)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        query = query.order_by(desc(Refund.created_at))
        offset = (page_num - 1) * page_size
        refunds = query.offset(offset).limit(page_size).all()
        
        return refunds, total
        
        return refunds, total
