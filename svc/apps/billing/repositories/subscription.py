"""
订阅数据访问层。
提供订阅相关的数据库操作，实现数据访问与业务逻辑分离。
"""

from datetime import datetime
from typing import Optional, List, Tuple, Dict, Any, Union

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.core.repositories import BaseRepository
from svc.apps.billing.models.subscription import Subscription
from svc.apps.billing.models.invoice import Invoice
from svc.apps.billing.schemas.subscription import SubscriptionCreate, SubscriptionUpdate

class SubscriptionRepository(BaseRepository[Subscription, SubscriptionCreate, SubscriptionUpdate]):
    """订阅仓库类，提供订阅相关的数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化订阅仓库"""
        super().__init__(db=db,model=Subscription)

    def _get_default_load_options(self) -> List[Any]:
        """获取订阅模型的默认关系加载选项
        
        默认加载：
        - plan: 订阅计划（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Subscription.plan)
        ]
    

    



    

    

    

    
