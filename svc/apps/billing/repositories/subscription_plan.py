"""
订阅计划数据访问层。
负责订阅计划模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.billing.models.subscription_plan import SubscriptionPlan
from svc.apps.billing.schemas.subscription_plan import (SubscriptionPlanCreate,
                                                        SubscriptionPlanUpdate)
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class SubscriptionPlanRepository(BaseRepository[SubscriptionPlan, SubscriptionPlanCreate, SubscriptionPlanUpdate]):
    """订阅计划仓库类，提供订阅计划相关的数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化订阅计划仓库"""
        super().__init__(db=db,model=SubscriptionPlan)

    def _get_default_load_options(self) -> List[Any]:
        """获取订阅计划模型的默认关系加载选项
        
        默认加载：
        - subscriptions: 关联的订阅（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(SubscriptionPlan.subscriptions)
        ]

    
    async def update(
        self,
       
        *,
        plan_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        price: Optional[float] = None,
        currency: Optional[str] = None,
        interval: Optional[str] = None,
        interval_count: Optional[int] = None,
        trial_period_days: Optional[int] = None,
        is_active: Optional[bool] = None,
        features: Optional[Dict[str, Any]] = None,
        meta_data: Optional[Dict[str, Any]] = None,
        tier: Optional[str] = None,
        valid_from: Optional[datetime] = None,
        valid_until: Optional[datetime] = None,
        max_users: Optional[int] = None,
        max_storage: Optional[int] = None,
        max_projects: Optional[int] = None,
        sort_order: Optional[int] = None,
    ) -> Optional[SubscriptionPlan]:
        """
        更新订阅计划
        
        Args:
            db: 数据库会话
            plan_id: 订阅计划ID
            name: 订阅计划名称
            description: 描述
            price: 价格
            currency: 货币
            interval: 时间间隔
            interval_count: 间隔计数
            trial_period_days: 试用期天数
            is_active: 是否激活
            features: 功能特性
            meta_data: 元数据
            tier: 层级
            valid_from: 生效日期
            valid_until: 失效日期
            max_users: 最大用户数
            max_storage: 最大存储空间
            max_projects: 最大项目数
            sort_order: 排序顺序
            
        Returns:
            Optional[SubscriptionPlan]: 更新后的订阅计划对象，不存在则返回None
        """
        plan = await self.get_by_id(plan_id)
        if not plan:
            return None
            
        data = {}
        if name is not None:
            data["name"] = name
        if description is not None:
            data["description"] = description
        if price is not None:
            data["price"] = price
        if currency is not None:
            data["currency"] = currency
        if interval is not None:
            data["interval"] = interval
        if interval_count is not None:
            data["interval_count"] = interval_count
        if trial_period_days is not None:
            data["trial_period_days"] = trial_period_days
        if is_active is not None:
            data["is_active"] = is_active
        if features is not None:
            data["features"] = features
        if meta_data is not None:
            data["meta_data"] = meta_data
        if tier is not None:
            data["tier"] = tier
        if valid_from is not None:
            data["valid_from"] = valid_from
        if valid_until is not None:
            data["valid_until"] = valid_until
        if max_users is not None:
            data["max_users"] = max_users
        if max_storage is not None:
            data["max_storage"] = max_storage
        if max_projects is not None:
            data["max_projects"] = max_projects
        if sort_order is not None:
            data["sort_order"] = sort_order
        
        data["updated_at"] = get_utc_now_without_tzinfo()
        
        return await super().update(plan, data)
    
    async def compare_plans(self, plan_ids: List[int]) -> Dict[str, List[Dict[str, Any]]]:
        """
        比较多个订阅计划
        
        Args:
            db: 数据库会话
            plan_ids: 订阅计划ID列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: 比较结果
        """
        plans = []
        for plan_id in plan_ids:
            plan = await self.get_by_id(plan_id)
            if plan:
                plans.append(plan)
                
        if not plans:
            return {"plans": []}
            
        # 提取所有特性键
        all_features = set()
        for plan in plans:
            if isinstance(plan.features, dict):
                all_features.update(plan.features.keys())
                
        # 构建比较结果
        result = []
        for plan in plans:
            plan_data = {
                "id": plan.id,
                "name": plan.name,
                "description": plan.description,
                "price": plan.price,
                "currency": plan.currency,
                "interval": plan.interval,
                "interval_count": plan.interval_count,
                "tier": plan.tier,
                "max_users": plan.max_users,
                "max_storage": plan.max_storage,
                "max_projects": plan.max_projects,
                "features": {}
            }
            
            # 填充特性数据
            for feature in all_features:
                if isinstance(plan.features, dict) and feature in plan.features:
                    plan_data["features"][feature] = plan.features[feature]
                else:
                    plan_data["features"][feature] = None
                    
            result.append(plan_data)
            
        return {"plans": result} 