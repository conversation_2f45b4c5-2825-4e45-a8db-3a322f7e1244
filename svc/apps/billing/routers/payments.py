"""
支付管理API路由
包含支付记录的创建、查询、状态更新和退款功能
"""
from typing import Any, Dict, List, Optional

from fastapi import (APIRouter, Body, Depends, HTTPException, Path, Query,
                     status)

from svc.apps.auth.dependencies import (get_current_active_user,
                                        get_current_superuser, has_permission,
                                        resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.billing.dependencies import get_payment_service
from svc.apps.billing.schemas.payment import (CreatePaymentParams,
                                              GetPaymentParams,
                                              GetPaymentsParams,
                                              MarkPaymentFailedParams,
                                              MarkPaymentSucceededParams,
                                              PaymentCreate,
                                              PaymentListResponse,
                                              PaymentResponse, PaymentUpdate,
                                              ProcessPaymentCallbackParams,
                                              RefundPaymentParams,
                                              UpdatePaymentParams)
from svc.apps.billing.services.payment import PaymentService
from svc.core.exceptions.route_error_handler import (PAYMENT_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result, ResultFactory
from svc.core.schemas.base import PageParams

router = APIRouter(tags=["支付"])


@router.get("/mine", response_model=Result[PaymentListResponse])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def get_my_payments(
    invoice_id: Optional[int] = Query(None, description="按账单ID过滤", alias="invoiceId"),
    status: Optional[str] = Query(None, description="按支付状态过滤", alias="status"),
    params: PageParams = Depends(),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[PaymentListResponse]:
    """获取当前用户的支付记录列表"""
    params_obj = GetPaymentsParams(
        invoice_id=invoice_id,
        user_id=current_user.id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await payment_service.get_payments(params_obj)
    return result


@router.get("/details/{payment_id}", response_model=Result[PaymentResponse])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def get_my_payment_details(
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[PaymentResponse]:
    """获取当前用户的特定支付详情"""
    params = GetPaymentParams(payment_id=payment_id, user_id=current_user.id)
    result = await payment_service.get_payment(params)
    return result


@router.post("/initiate", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def initiate_payment(
    payment_data: PaymentCreate,
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
) -> Result:
    """为当前用户发起一笔支付（创建支付记录）"""
    params = CreatePaymentParams(payment_data=payment_data, user_id=current_user.id)
    result = await payment_service.create_payment(params)
    return result


@router.post("/callback", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def process_payment_callback(
    callback_data: Dict[str, Any] = Body(..., description="支付回调数据"),
    payment_service: PaymentService = Depends(get_payment_service),
) -> Result:
    """处理支付网关回调（公开）"""
    params = ProcessPaymentCallbackParams(callback_data=callback_data)
    result = await payment_service.process_payment_callback(params)
    return result


@router.get("/admin/list", response_model=Result[PaymentListResponse])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_list_payments(
    invoice_id: Optional[int] = Query(None, description="按账单ID过滤", alias="invoiceId"),
    user_id: Optional[int] = Query(None, description="按用户ID过滤", alias="userId"),
    status: Optional[str] = Query(None, description="按支付状态过滤", alias="status"),
    params: PageParams = Depends(),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("payment:read"))
) -> Result[PaymentListResponse]:
    """获取支付记录列表 (管理端)"""
    params_obj = GetPaymentsParams(
        invoice_id=invoice_id,
        user_id=user_id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await payment_service.get_payments(params_obj)
    return result


@router.get("/admin/details/{payment_id}", response_model=Result[PaymentResponse])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_get_payment_details(
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("payment", "read"))
) -> Result[PaymentResponse]:
    """获取任意支付详情 (管理端)"""
    params = GetPaymentParams(payment_id=payment_id, user_id=None)
    result = await payment_service.get_payment(params)
    return result


@router.put("/admin/{payment_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_update_payment(
    payment_update: PaymentUpdate,
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("payment", "update"))
) -> Result:
    """更新任意支付记录 (管理端)"""
    params = UpdatePaymentParams(
        payment_id=payment_id,
        payment_data=payment_update
    )
    result = await payment_service.update_payment(params)
    return result


@router.post("/admin/mark-succeeded/{payment_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_mark_payment_succeeded(
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("payment:mark_succeeded"))
) -> Result:
    """手动标记支付为成功 (管理端)"""
    params = MarkPaymentSucceededParams(payment_id=payment_id)
    result = await payment_service.mark_payment_succeeded(params)
    return result


@router.post("/admin/mark-failed/{payment_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_mark_payment_failed(
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    error_message: Optional[str] = Query(None, description="失败原因", alias="errorMessage"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("payment:mark_failed"))
) -> Result:
    """手动标记支付为失败 (管理端)"""
    params = MarkPaymentFailedParams(
        payment_id=payment_id,
        error_message=error_message
    )
    result = await payment_service.mark_payment_failed(params)
    return result


@router.post("/admin/refund/{payment_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(PAYMENT_ERROR_MAPPING)
async def admin_refund_payment(
    payment_id: int = Path(..., description="支付ID", alias="paymentId"),
    payment_service: PaymentService = Depends(get_payment_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("payment:refund"))
) -> Result:
    """发起退款 (管理端)"""
    params = RefundPaymentParams(payment_id=payment_id)
    result = await payment_service.refund_payment(params)
    return result 