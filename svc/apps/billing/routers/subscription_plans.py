"""
订阅计划管理API路由
包含订阅计划的创建、查询、更新和删除功能
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Path, Query

from svc.apps.auth.dependencies import (get_current_superuser, has_permission,
                                        resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.billing.dependencies import get_subscription_plan_service
from svc.apps.billing.schemas.subscription_plan import (
    CreatePlanParams, DeletePlanParams, GetPlanParams, GetPlansParams,
    SubscriptionPlanCreate, SubscriptionPlanListResponse,
    SubscriptionPlanResponse, SubscriptionPlanUpdate, UpdatePlanParams,
    SubscriptionPlanBatchUpdateRequest, SubscriptionPlanBatchUpdateResponse,
    SubscriptionPlanBatchDeleteRequest, SubscriptionPlanBatchDeleteResponse)
from svc.apps.billing.services.subscription_plan import SubscriptionPlanService
from svc.core.exceptions.route_error_handler import (
    SUBSCRIPTION_PLAN_ERROR_MAPPING, handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

# Define the router for this file (prefix will be added by factory)
router = APIRouter(tags=["订阅计划"])

# === 客户端/公共路由 (Client/Public Routes) ===

@router.get("/list", response_model=Result[SubscriptionPlanListResponse]) # Path: /subscription_plans/list
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def list_subscription_plans(
    params: PageParams = Depends(),
    active_only: Optional[bool] = Query(False, description="是否只返回激活的计划", alias="activeOnly"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
) -> Result[SubscriptionPlanListResponse]:
    """获取订阅计划列表 (公开)"""
    params_obj = GetPlansParams(
        page_num=params.page_num,
        page_size=params.page_size,
        active_only=active_only
    )
    result = await subscription_plan_service.get_plans(params_obj)
    return result


@router.get("/details/{plan_id}", response_model=Result[SubscriptionPlanResponse]) # Path: /subscription_plans/details/{plan_id}
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def get_subscription_plan(
    plan_id: int = Path(..., description="计划ID"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
) -> Result[SubscriptionPlanResponse]:
    """获取订阅计划详情 (公开)"""
    params = GetPlanParams(plan_id=plan_id)
    result = await subscription_plan_service.get_plan(params)
    return result

# === 管理端路由 (Admin Routes) ===

@router.post("/admin/create", response_model=Result[Dict[str, Any]]) # Path: /subscription_plans/admin/create
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_create_subscription_plan(
    plan_data: SubscriptionPlanCreate,
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("subscription_plan:create")),
) -> Result:
    """创建订阅计划 (管理端)"""
    params = CreatePlanParams(plan_data=plan_data)
    result = await subscription_plan_service.create_plan(params)
    return result

@router.put("/admin/{plan_id}", response_model=Result[Dict[str, Any]]) # Path: /subscription_plans/admin/{plan_id}
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_update_subscription_plan(
    plan_data: SubscriptionPlanUpdate,
    plan_id: int = Path(..., description="计划ID", alias="planId"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """更新订阅计划 (管理端)"""
    params = UpdatePlanParams(
        plan_id=plan_id,
        plan_data=plan_data
    )
    result = await subscription_plan_service.update_plan(params)
    return result

@router.delete("/admin/{plan_id}", response_model=Result[Dict[str, Any]]) # Path: /subscription_plans/admin/{plan_id}
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_delete_subscription_plan(
    plan_id: int = Path(..., description="计划ID", alias="planId"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
    _: Any = Depends(resource_permission("subscription_plan", "delete")),
) -> Result:
    """删除订阅计划 (管理端)"""
    params = DeletePlanParams(plan_id=plan_id)
    result = await subscription_plan_service.delete_plan(params)
    return result

@router.post("/admin/activate/{plan_id}", response_model=Result[Dict[str, Any]]) # Path: /subscription_plans/admin/activate/{plan_id}
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_activate_subscription_plan(
    plan_id: int = Path(..., description="计划ID", alias="planId"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """激活订阅计划 (管理端)"""
    # Service logic might need adjustment if it expects update data differently
    result = await subscription_plan_service.activate_plan(plan_id=plan_id)
    return result

@router.post("/admin/deactivate/{plan_id}", response_model=Result[Dict[str, Any]]) # Path: /subscription_plans/admin/deactivate/{plan_id}
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_deactivate_subscription_plan(
    plan_id: int = Path(..., description="计划ID", alias="planId"),
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
    _: Any = Depends(resource_permission("subscription_plan", "update")),
) -> Result:
    """停用订阅计划 (管理端)"""
    # Service logic might need adjustment
    result = await subscription_plan_service.deactivate_plan(plan_id=plan_id)
    return result


# === 批量操作路由 ===

@router.put("/admin/batch", response_model=Result[SubscriptionPlanBatchUpdateResponse])
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_batch_update_subscription_plans(
    request: SubscriptionPlanBatchUpdateRequest,
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
) -> Result[SubscriptionPlanBatchUpdateResponse]:
    """批量更新订阅计划 (管理端)
    
    批量更新多个订阅计划的激活状态和价格等信息
    """
    return await subscription_plan_service.batch_update_subscription_plans(request=request)

@router.delete("/admin/batch", response_model=Result[SubscriptionPlanBatchDeleteResponse])
@handle_route_errors(SUBSCRIPTION_PLAN_ERROR_MAPPING)
async def admin_batch_delete_subscription_plans(
    request: SubscriptionPlanBatchDeleteRequest,
    subscription_plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service),
    current_user: User = Depends(get_current_superuser),
) -> Result[SubscriptionPlanBatchDeleteResponse]:
    """批量删除订阅计划 (管理端)
    
    批量删除多个订阅计划
    """
    return await subscription_plan_service.batch_delete_subscription_plans(request=request)