"""
订阅管理API路由
包含订阅的创建、查询、更新、取消和续订功能
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.billing.dependencies import get_subscription_service
from svc.apps.billing.schemas.subscription import (
    CancelSubscriptionParams, ChangePlanParams, CreateSubscriptionParams,
    GetSubscriptionParams, GetSubscriptionsParams, PauseSubscriptionParams,
    RenewSubscriptionParams, ResumeSubscriptionParams,
    SubscriptionBatchDeleteRequest, SubscriptionBatchDeleteResponse,
    SubscriptionBatchUpdateRequest, SubscriptionBatchUpdateResponse,
    SubscriptionCreate, SubscriptionListResponse, SubscriptionResponse,
    SubscriptionUpdate, UpdateSubscriptionParams)
from svc.apps.billing.services.subscription import SubscriptionService
from svc.core.exceptions.route_error_handler import (
    SUBSCRIPTION_ERROR_MAPPING, handle_route_errors)
from svc.core.models.result import Result, ResultFactory
from svc.core.schemas.base import PageParams

router = APIRouter(tags=["订阅"])


@router.get("/mine", response_model=Result[SubscriptionListResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def get_my_subscriptions(
    status: Optional[str] = Query(None, description="订阅状态过滤", alias="status"),
    params: PageParams = Depends(),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionListResponse]:
    """获取当前用户的订阅列表"""
    params_obj = GetSubscriptionsParams(
        user_id=current_user.id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await subscription_service.get_subscriptions(params_obj)
    return result





@router.get("/details/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def get_my_subscription_details(
    subscription_id: int = Path(..., description="订阅ID", alias="subscriptionId"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """获取当前用户的特定订阅详情"""
    params = GetSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.get_subscription(params)
    return result




@router.post("/create", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def create_my_subscription(
    subscription_data: SubscriptionCreate,
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """为当前用户创建新订阅"""
    if hasattr(subscription_data, 'user_id') and subscription_data.user_id != current_user.id:
         return ResultFactory.permission_denied("不能为其他用户创建订阅")
    params = CreateSubscriptionParams(subscription_data=subscription_data, user_id=current_user.id)
    result = await subscription_service.create_subscription(params)
    return result


@router.post("/cancel/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def cancel_my_subscription(
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """取消当前用户的特定订阅"""
    params = CancelSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.cancel_subscription(params)
    return result


@router.post("/renew/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def renew_my_subscription(
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """续订当前用户的特定订阅"""
    params = RenewSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.renew_subscription(params)
    return result


@router.post("/pause/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def pause_my_subscription(
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """暂停当前用户的特定订阅"""
    params = PauseSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.pause_subscription(params)
    return result


@router.post("/resume/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def resume_my_subscription(
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """恢复当前用户的特定订阅"""
    params = ResumeSubscriptionParams(subscription_id=subscription_id, user_id=current_user.id)
    result = await subscription_service.resume_subscription(params)
    return result


@router.post("/change-plan/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def change_my_subscription_plan(
    plan_id: int = Query(..., description="目标订阅计划ID", alias="planId"),
    effective_date: Optional[str] = Query(None, description="变更生效日期（ISO格式），降级时使用", alias="effectiveDate"),
    downgrade_type: Optional[str] = Query("end_of_period", description="降级类型：immediate（立即降级）或 end_of_period（周期结束降级）", alias="downgradeType"),
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
) -> Result[SubscriptionResponse]:
    """更改当前用户的订阅计划（自动判断升级或降级）

    降级类型说明：
    - immediate: 立即降级，计算退款，立即生效
    - end_of_period: 周期结束降级，当前周期结束后生效，无退款
    """
    params = ChangePlanParams(
        subscription_id=subscription_id,
        user_id=current_user.id,
        plan_id=plan_id,
        effective_date=effective_date,
        downgrade_type=downgrade_type
    )
    result = await subscription_service.change_plan(params)
    return result


@router.get("/admin/list", response_model=Result[SubscriptionListResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_list_subscriptions(
    user_id: Optional[int] = Query(None, description="按用户ID过滤", alias="userId"),
    status: Optional[str] = Query(None, description="按订阅状态过滤", alias="status"),
    params: PageParams = Depends(),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("subscription:read"))
) -> Result[SubscriptionListResponse]:
    """获取订阅列表 (管理端)"""
    params_obj = GetSubscriptionsParams(
        user_id=user_id,
        status=status,
        page_num=params.page_num,
        page_size=params.page_size
    )
    result = await subscription_service.get_subscriptions(params_obj)
    return result

# === 批量操作路由 ===

@router.put("/admin/batch", response_model=Result[SubscriptionBatchUpdateResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_batch_update_subscriptions(
    request: SubscriptionBatchUpdateRequest,
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("subscription:update"))
) -> Result[SubscriptionBatchUpdateResponse]:
    """批量更新订阅 (管理端)
    
    批量更新多个订阅的状态和数量等信息
    """
    return await subscription_service.batch_update_subscriptions(request=request)

@router.delete("/admin/batch", response_model=Result[SubscriptionBatchDeleteResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_batch_delete_subscriptions(
    request: SubscriptionBatchDeleteRequest,
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("subscription:delete"))
) -> Result[SubscriptionBatchDeleteResponse]:
    """批量删除订阅 (管理端)
    
    批量删除多个订阅
    """
    return await subscription_service.batch_delete_subscriptions(request=request)

@router.get("/admin/details/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_get_subscription_details(
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription", "read"))
) -> Result[SubscriptionResponse]:
    """获取任意订阅详情 (管理端)"""
    params = GetSubscriptionParams(subscription_id=subscription_id, user_id=None)
    result = await subscription_service.get_subscription(params)
    return result


@router.put("/admin/{subscription_id}", response_model=Result[SubscriptionResponse])
@handle_route_errors(SUBSCRIPTION_ERROR_MAPPING)
async def admin_update_subscription(
    subscription_update: SubscriptionUpdate,
    subscription_id: int = Path(..., description="订阅ID"),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("subscription", "update"))
) -> Result[SubscriptionResponse]:
    """更新任意订阅信息 (管理端)"""
    params = UpdateSubscriptionParams(
        subscription_id=subscription_id,
        subscription_data=subscription_update
    )
    result = await subscription_service.update_subscription(params)
    return result


