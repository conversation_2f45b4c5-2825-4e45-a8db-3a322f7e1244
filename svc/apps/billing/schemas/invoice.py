from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest, BatchDeleteResponse, BatchUpdateResponse


# 参数模型
class GetInvoiceParams(CamelCaseModel):
    """获取账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1001,
                "user_id": 101
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetInvoicesParams(BaseModel):
    """获取账单列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 501,
                "user_id": 101,
                "status": "unpaid",
                "page_num": 1,
                "page_size": 20
            }
        }
    )
    
    subscription_id: Optional[int] = Field(default=None, description="订阅ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")
    status: Optional[str] = Field(default=None, description="账单状态")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")


class GetUnpaidInvoicesParams(CamelCaseModel):
    """获取未支付账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 501
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")


class CreateInvoiceParams(BaseModel):
    """创建账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_data": {
                    "subscription_id": 501,
                    "user_id": 101,
                    "amount": 399900,
                    "currency": "CNY",
                    "description": "SHOEI GT-AIR II 全盔摩托车头盔 - 月度订阅",
                    "due_date": "2024-04-24T23:59:59",
                    "items": [
                        {
                            "product_id": 1,
                            "quantity": 1,
                            "unit_price": 399900,
                            "description": "SHOEI GT-AIR II 全盔摩托车头盔"
                        }
                    ]
                }
            }
        }
    )
    
    invoice_data: 'InvoiceCreate' = Field(description="账单创建数据")


class UpdateInvoiceParams(BaseModel):
    """更新账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1001,
                "invoice_data": {
                    "amount": 359900,
                    "description": "SHOEI GT-AIR II 全盔摩托车头盔 - 月度订阅（优惠价）",
                    "due_date": "2024-04-30T23:59:59",
                    "notes": "客户申请价格调整，已批准"
                }
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    invoice_data: 'InvoiceUpdate' = Field(description="账单更新数据")


class MarkAsPaidParams(CamelCaseModel):
    """标记为已支付参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1001,
                "paid_at": "2024-03-24T15:30:00",
                "payment_method": "alipay",
                "transaction_id": "ALI202403241530001234567890",
                "notes": "支付宝支付成功"
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    payment_id: Optional[int] = Field(default=None, description="支付ID")
    paid_at: Optional[datetime] = Field(default=None, description="支付时间")
    payment_method: Optional[str] = Field(default=None, description="支付方式")
    transaction_id: Optional[str] = Field(default=None, description="交易ID")
    notes: Optional[str] = Field(default=None, description="备注")


class MarkAsOverdueParams(CamelCaseModel):
    """标记为逾期参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


class CancelInvoiceParams(BaseModel):
    """取消账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


# 为了导入兼容性，添加别名
MarkInvoicePaidParams = MarkAsPaidParams
MarkInvoiceOverdueParams = MarkAsOverdueParams


class InvoiceItem(CamelCaseModel):
    description: str = Field(..., description="项目描述")
    quantity: int = Field(..., description="数量")
    unit_price: float = Field(..., description="单价")
    amount: float = Field(..., description="金额")


class InvoiceBase(CamelCaseModel):
    user_id: int = Field(..., description="用户ID")
    subscription_id: Optional[int] = Field(None, description="订阅ID")
    status: str = Field(..., description="账单状态 (e.g., draft, open, paid, void, uncollectible)")
    due_date: datetime = Field(..., description="到期日期")
    total_amount: float = Field(..., description="总金额")
    currency: str = Field("CNY", description="货币")
    items: List[InvoiceItem] = Field([], description="账单项目")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class InvoiceCreate(InvoiceBase):
    pass


class InvoiceUpdate(CamelCaseModel):
    status: Optional[str] = Field(None, description="账单状态")


class InvoiceResponse(InvoiceBase):
    id: int = Field(..., description="账单ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        """自定义验证方法，处理字段映射"""
        if hasattr(obj, 'amount'):
            # 将 amount 映射到 total_amount
            obj.total_amount = obj.amount
        if hasattr(obj, 'subscription') and obj.subscription:
            # 从 subscription 获取 user_id
            obj.user_id = obj.subscription.user_id
        return super().model_validate(obj, *args, **kwargs)

    class Config:
        from_attributes = True


class InvoiceListResponse(PaginatedResponse[InvoiceResponse]):
    """账单列表响应模型"""
    pass


# === 批量操作Schema ===

class InvoiceBatchUpdate(BaseModel):
    """账单批量更新模型
    
    定义允许批量更新的字段，仅包含安全的字段
    """
    model_config = ConfigDict(str_strip_whitespace=True)
    
    status: Optional[str] = Field(None, description="账单状态")

class InvoiceBatchUpdateRequest(BatchUpdateRequest[InvoiceBatchUpdate]):
    """账单批量更新请求模型
    
    包含要更新的账单ID列表和更新数据
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "paid"
                }
            }
        }
    )

class InvoiceBatchUpdateResponse(BatchUpdateResponse):
    """账单批量更新响应模型"""
    pass

class InvoiceBatchDeleteRequest(BatchDeleteRequest):
    """账单批量删除请求模型
    
    包含要删除的账单ID列表
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class InvoiceBatchDeleteResponse(BatchDeleteResponse):
    """账单批量删除响应模型"""
    pass 