"""
退款相关的数据模式定义
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator

from ..models.refund import RefundStatus, RefundType


class RefundBase(BaseModel):
    """退款基础模式"""
    amount: float = Field(..., gt=0, description="退款金额")
    currency: str = Field(default="CNY", description="货币类型")
    refund_type: str = Field(..., description="退款类型")
    reason: Optional[str] = Field(None, description="退款原因")
    description: Optional[str] = Field(None, description="退款描述")
    meta_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")
    
    @validator('refund_type')
    def validate_refund_type(cls, v):
        if v not in [t.value for t in RefundType]:
            raise ValueError(f"无效的退款类型: {v}")
        return v
    
    @validator('currency')
    def validate_currency(cls, v):
        if v not in ["CNY", "USD", "EUR"]:
            raise ValueError(f"不支持的货币类型: {v}")
        return v


class CreateRefundRequest(RefundBase):
    """创建退款请求"""
    subscription_id: int = Field(..., description="订阅ID")
    invoice_id: Optional[int] = Field(None, description="关联账单ID")


class RefundResponse(RefundBase):
    """退款响应"""
    id: int = Field(..., description="退款ID")
    subscription_id: int = Field(..., description="订阅ID")
    invoice_id: Optional[int] = Field(None, description="关联账单ID")
    user_id: int = Field(..., description="用户ID")
    status: str = Field(..., description="退款状态")
    refund_date: datetime = Field(..., description="退款申请日期")
    processed_at: Optional[datetime] = Field(None, description="处理完成时间")
    processor_id: Optional[int] = Field(None, description="处理人ID")
    processing_notes: Optional[str] = Field(None, description="处理备注")
    external_refund_id: Optional[str] = Field(None, description="外部退款ID")
    payment_method: Optional[str] = Field(None, description="退款方式")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 计算字段
    display_status: str = Field(..., description="显示状态")
    display_type: str = Field(..., description="显示类型")
    is_pending: bool = Field(..., description="是否待处理")
    is_processed: bool = Field(..., description="是否已处理")
    is_failed: bool = Field(..., description="是否失败")
    can_be_canceled: bool = Field(..., description="是否可取消")
    processing_time_days: Optional[int] = Field(None, description="处理耗时天数")
    
    class Config:
        from_attributes = True


class RefundListResponse(BaseModel):
    """退款列表响应"""
    items: List[RefundResponse] = Field(..., description="退款列表")
    total: int = Field(..., description="总数")
    page_num: int = Field(..., description="页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


class ProcessRefundRequest(BaseModel):
    """处理退款请求"""
    processor_id: int = Field(..., description="处理人ID")
    processing_notes: Optional[str] = Field(None, description="处理备注")
    external_refund_id: Optional[str] = Field(None, description="外部退款ID")


class CancelRefundRequest(BaseModel):
    """取消退款请求"""
    reason: str = Field(..., description="取消原因")


class RefundStatisticsResponse(BaseModel):
    """退款统计响应"""
    total_count: int = Field(..., description="总退款数")
    total_amount: float = Field(..., description="总退款金额")
    processed_count: int = Field(..., description="已处理数量")
    pending_count: int = Field(..., description="待处理数量")
    failed_count: int = Field(..., description="失败数量")
    status_statistics: Dict[str, Dict[str, Any]] = Field(..., description="状态统计")
    type_statistics: Dict[str, Dict[str, Any]] = Field(..., description="类型统计")


class RefundSearchRequest(BaseModel):
    """退款搜索请求"""
    keyword: Optional[str] = Field(None, description="关键词")
    status: Optional[str] = Field(None, description="退款状态")
    refund_type: Optional[str] = Field(None, description="退款类型")
    user_id: Optional[int] = Field(None, description="用户ID")
    subscription_id: Optional[int] = Field(None, description="订阅ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    @validator('status')
    def validate_status(cls, v):
        if v and v not in [s.value for s in RefundStatus]:
            raise ValueError(f"无效的退款状态: {v}")
        return v
    
    @validator('refund_type')
    def validate_refund_type(cls, v):
        if v and v not in [t.value for t in RefundType]:
            raise ValueError(f"无效的退款类型: {v}")
        return v


class CreateCancellationRefundRequest(BaseModel):
    """创建取消订阅退款请求"""
    subscription_id: int = Field(..., description="订阅ID")
    reason: str = Field(..., description="取消原因")
    calculate_prorated: bool = Field(default=True, description="是否按比例计算")


class CreateDowngradeRefundRequest(BaseModel):
    """创建降级退款请求"""
    subscription_id: int = Field(..., description="订阅ID")
    old_plan_id: int = Field(..., description="原计划ID")
    new_plan_id: int = Field(..., description="新计划ID")
    effective_date: Optional[datetime] = Field(None, description="生效日期")


class RefundSummary(BaseModel):
    """退款摘要"""
    refund_id: int = Field(..., description="退款ID")
    amount: float = Field(..., description="退款金额")
    currency: str = Field(..., description="货币类型")
    status: str = Field(..., description="退款状态")
    refund_type: str = Field(..., description="退款类型")
    refund_date: datetime = Field(..., description="退款日期")
    display_status: str = Field(..., description="显示状态")
    display_type: str = Field(..., description="显示类型")


class UserRefundHistoryResponse(BaseModel):
    """用户退款历史响应"""
    user_id: int = Field(..., description="用户ID")
    refunds: List[RefundSummary] = Field(..., description="退款列表")
    total_refunded: float = Field(..., description="总退款金额")
    refund_count: int = Field(..., description="退款次数")
    last_refund_date: Optional[datetime] = Field(None, description="最后退款日期")
