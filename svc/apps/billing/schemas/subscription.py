from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator

from svc.apps.billing.schemas.subscription_plan import SubscriptionPlanResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import (BatchDeleteRequest, BatchDeleteResponse,
                                    BatchUpdateRequest, BatchUpdateResponse)


# 参数模型
class GetSubscriptionParams(CamelCaseModel):
    """获取订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 501,
                "user_id": 101
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetSubscriptionsParams(BaseModel):
    """获取订阅列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 101,
                "status": "active",
                "page_num": 1,
                "page_size": 20
            }
        }
    )
    
    user_id: Optional[int] = Field(default=None, description="用户ID (为空时查询所有用户)")
    status: Optional[str] = Field(default=None, description="订阅状态")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")


class CreateSubscriptionParams(CamelCaseModel):
    """创建订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_data": {
                    "plan_id": 1,
                    "product_id": 1,
                    "quantity": 1,
                    "metadata": {
                        "product_name": "SHOEI GT-AIR II 全盔摩托车头盔",
                        "subscription_type": "monthly"
                    }
                },
                "user_id": 101
            }
        }
    )
    
    subscription_data: 'SubscriptionCreate' = Field(description="订阅创建数据")
    user_id: int = Field(description="用户ID")


class UpdateSubscriptionParams(BaseModel):
    """更新订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 501,
                "subscription_data": {
                    "quantity": 2,
                    "metadata": {
                        "product_name": "SHOEI GT-AIR II 全盔摩托车头盔",
                        "subscription_type": "monthly",
                        "updated_reason": "客户需求增加"
                    }
                },
                "user_id": 101
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    subscription_data: 'SubscriptionUpdate' = Field(description="订阅更新数据")
    user_id: int = Field(description="用户ID")


class CancelSubscriptionParams(CamelCaseModel):
    """取消订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 501,
                "user_id": 101,
                "at_period_end": True,
                "reason": "客户不再需要SHOEI头盔订阅服务"
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    user_id: int = Field(description="用户ID")
    at_period_end: bool = Field(default=True, description="是否在当前周期结束时取消")
    reason: Optional[str] = Field(default=None, description="取消原因")


class ReactivateSubscriptionParams(CamelCaseModel):
    """重新激活订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    user_id: int = Field(description="用户ID")


class RenewSubscriptionParams(BaseModel):
    """续订订阅参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")
    user_id: int = Field(description="用户ID")


class SubscriptionBase(CamelCaseModel):
    """订阅基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "plan_id": 1,
                "quantity": 1,
                "meta_data": {}
            }
        }
    )
    
    user_id: int = Field(description="用户ID")
    plan_id: int = Field(description="订阅计划ID")
    quantity: int = Field(default=1, description="数量")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class SubscriptionCreate(SubscriptionBase):
    """创建订阅请求模型"""
    pass


class SubscriptionUpdate(CamelCaseModel):
    """更新订阅请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 2,
                "quantity": 2,
                "meta_data": {}
            }
        }
    )
    
    plan_id: Optional[int] = Field(default=None, description="订阅计划ID")
    quantity: Optional[int] = Field(default=None, description="数量")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")


class SubscriptionInDB(SubscriptionBase):
    """数据库中的订阅模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "status": "active",
                "current_period_start": "2024-03-24T12:00:00",
                "current_period_end": "2024-04-24T12:00:00",
                "cancel_at_period_end": False,
                "canceled_at": None,
                "trial_start": "2024-03-24T12:00:00",
                "trial_end": "2024-03-31T12:00:00",
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="订阅ID")
    status: str = Field(description="订阅状态")
    current_period_start: datetime = Field(description="当前周期开始时间")
    current_period_end: datetime = Field(description="当前周期结束时间")
    cancel_at_period_end: bool = Field(description="是否在当前周期结束时取消")
    canceled_at: Optional[datetime] = Field(default=None, description="取消时间")
    trial_start: Optional[datetime] = Field(default=None, description="试用开始时间")
    trial_end: Optional[datetime] = Field(default=None, description="试用结束时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    @field_validator('meta_data', mode='before')
    @classmethod
    def validate_meta_data(cls, v, info):
        """将meta_data字段映射到meta_data"""
        if hasattr(info.data, 'meta_data'):
            return info.data.meta_data
        return v


class SubscriptionResponse(SubscriptionInDB):
    """订阅响应模型"""
    plan: Optional[SubscriptionPlanResponse] = Field(default=None, description="订阅计划信息")


class SubscriptionListResponse(PaginatedResponse[SubscriptionResponse]):
    """订阅列表响应模型"""
    pass


class SubscriptionCancelRequest(BaseModel):
    """取消订阅请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "at_period_end": True
            }
        }
    )
    
    at_period_end: bool = Field(default=True, description="是否在当前周期结束时取消") 


class ChangePlanParams(CamelCaseModel):
    """更改订阅计划参数（自动判断升级或降级）"""
    subscription_id: int
    user_id: int
    plan_id: int
    effective_date: Optional[str] = Field(None, description="变更生效日期（ISO格式），降级时使用")
    downgrade_type: Optional[str] = Field("end_of_period", description="降级类型：immediate（立即降级）或 end_of_period（周期结束降级）")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1,
                "plan_id": 2,
                "effective_date": None,
                "downgrade_type": "end_of_period"
            }
        }
    )

class PauseSubscriptionParams(CamelCaseModel):
    """暂停订阅参数"""
    subscription_id: int
    user_id: int
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1
            }
        }
    )

class ResumeSubscriptionParams(CamelCaseModel):
    """恢复订阅参数"""
    subscription_id: int
    user_id: int
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1
            }
        }
    )

class UserSubscriptionsResult(CamelCaseModel):
    """用户订阅结果"""
    subscriptions: List[SubscriptionResponse] = Field(default=[], description="订阅列表")
    total: int = Field(description="总记录数")


# === 批量操作Schema ===

class SubscriptionBatchUpdate(CamelCaseModel):
    """订阅批量更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    status: Optional[str] = Field(None, description="订阅状态")
    quantity: Optional[int] = Field(None, description="数量")

class SubscriptionBatchUpdateRequest(BatchUpdateRequest[SubscriptionBatchUpdate]):
    """订阅批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {"status": "active"}
            }
        }
    )

class SubscriptionBatchUpdateResponse(BatchUpdateResponse):
    """订阅批量更新响应模型"""
    pass

class SubscriptionBatchDeleteRequest(BatchDeleteRequest):
    """订阅批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class SubscriptionBatchDeleteResponse(BatchDeleteResponse):
    """订阅批量删除响应模型"""
    pass
        