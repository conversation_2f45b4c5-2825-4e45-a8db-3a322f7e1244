from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator
from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest, BatchDeleteResponse, BatchUpdateResponse

from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse


# 参数模型
class GetPlanParams(CamelCaseModel):
    """获取订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1,
                "user_id": 101
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetPlanByNameParams(CamelCaseModel):
    """通过名称获取订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI头盔月度订阅"
            }
        }
    )
    
    name: str = Field(description="订阅计划名称")


class GetPlansParams(CamelCaseModel):
    """获取订阅计划列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 20,
                "active_only": True
            }
        }
    )
    
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")
    active_only: bool = Field(default=False, description="是否只返回激活的计划")


class CreatePlanParams(CamelCaseModel):
    """创建订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_data": {
                    "name": "SHOEI头盔月度订阅",
                    "description": "SHOEI头盔产品月度订阅服务",
                    "price": 399.90,
                    "currency": "CNY",
                    "interval": "month",
                    "interval_count": 1,
                    "trial_period_days": 7,
                    "features": {
                        "product_type": "motorcycle_helmet",
                        "brand": "SHOEI",
                        "warranty": "12个月"
                    }
                }
            }
        }
    )
    
    plan_data: 'SubscriptionPlanCreate' = Field(description="订阅计划创建数据")


class UpdatePlanParams(CamelCaseModel):
    """更新订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1,
                "plan_data": {
                    "name": "SHOEI头盔月度订阅（升级版）",
                    "price": 359.90,
                    "features": {
                        "product_type": "motorcycle_helmet",
                        "brand": "SHOEI",
                        "warranty": "12个月",
                        "free_shipping": True
                    }
                }
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")
    plan_data: 'SubscriptionPlanUpdate' = Field(description="订阅计划更新数据")


class DeletePlanParams(CamelCaseModel):
    """删除订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")


class SubscriptionPlanBase(CamelCaseModel):
    """订阅计划基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI头盔月度订阅",
                "description": "SHOEI头盔产品月度订阅服务",
                "price": 399.90,
                "currency": "CNY",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 7,
                "features": {
                    "product_type": "motorcycle_helmet",
                    "brand": "SHOEI",
                    "warranty": "12个月",
                    "free_shipping": True
                },
                "meta_data": {
                    "category": "motorcycle_accessories",
                    "target_audience": "motorcycle_riders"
                }
            }
        }
    )
    
    name: str = Field(default='', max_length=100, description="套餐名称")
    description: Optional[str] = Field(default='', description="套餐描述")
    price: float = Field(default=0.0, description="价格")
    currency: str = Field(default="CNY", description="货币")
    interval:str = Field(default= "month",description="订阅间隔")  
    interval_count:int = Field(default=1,description="订阅间隔次数")
    features: Dict[str,Any] = Field(default={}, description="功能列表")
    meta_data: Optional[Dict[str, Any]] = Field(default={}, description="元数据")


class SubscriptionPlanCreate(SubscriptionPlanBase):
    """创建订阅计划请求模型"""
    pass


class SubscriptionPlanUpdate(CamelCaseModel):
    """更新订阅计划请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI头盔月度订阅（升级版）",
                "price": 359.90,
                "is_active": True,
                "features": {
                    "product_type": "motorcycle_helmet",
                    "brand": "SHOEI",
                    "warranty": "12个月",
                    "free_shipping": True,
                    "priority_support": True
                }
            }
        }
    )
    
    name: Optional[str] = Field(None, max_length=100, description="套餐名称")
    description: Optional[str] = Field(None, description="套餐描述")
    price: Optional[float] = Field(None, description="价格")
    currency: Optional[str] = Field(None, description="货币")
    features: Optional[List[str]] = Field(default=[], description="功能列表")
    meta_data: Optional[Dict[str, Any]] = Field(default={}, description="元数据")


class SubscriptionPlanInDB(SubscriptionPlanBase):
    """数据库中的订阅计划模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "SHOEI头盔月度订阅",
                "description": "SHOEI头盔产品月度订阅服务",
                "price": 399.90,
                "currency": "CNY",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 7,
                "features": {
                    "product_type": "motorcycle_helmet",
                    "brand": "SHOEI",
                    "warranty": "12个月",
                    "free_shipping": True
                },
                "meta_data": {
                    "category": "motorcycle_accessories",
                    "target_audience": "motorcycle_riders"
                },
                "is_active": True,
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00"
            }
        }
    )
    
    id: int = Field(..., description="套餐ID")
    is_active: bool = Field(description="是否激活")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    @field_validator('meta_data', mode='before')
    @classmethod
    def validate_meta_data(cls, v, info):
        """将meta_data字段映射到meta_data"""
        if hasattr(info.data, 'meta_data'):
            return info.data.meta_data
        return v


class SubscriptionPlanResponse(SubscriptionPlanInDB):
    """订阅计划响应模型"""
    pass


class SubscriptionPlanListResponse(PaginatedResponse[SubscriptionPlanResponse]):
    """订阅计划列表响应模型"""
    pass


# === 批量操作Schema ===

class SubscriptionPlanBatchUpdate(CamelCaseModel):
    """订阅计划批量更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    is_active: Optional[bool] = Field(None, description="是否激活")
    price: Optional[float] = Field(None, description="价格")

class SubscriptionPlanBatchUpdateRequest(BatchUpdateRequest[SubscriptionPlanBatchUpdate]):
    """订阅计划批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {"is_active": True}
            }
        }
    )

class SubscriptionPlanBatchUpdateResponse(BatchUpdateResponse):
    """订阅计划批量更新响应模型"""
    pass

class SubscriptionPlanBatchDeleteRequest(BatchDeleteRequest):
    """订阅计划批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class SubscriptionPlanBatchDeleteResponse(BatchDeleteResponse):
    """订阅计划批量删除响应模型"""
    pass 