"""
账单服务模块，提供面向对象形式的账单管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.models.invoice import Invoice
from svc.apps.billing.repositories.invoice import InvoiceRepository
from svc.apps.billing.schemas.invoice import (CancelInvoiceParams,
                                              CreateInvoiceParams,
                                              GetInvoiceParams,
                                              GetInvoicesParams,
                                              GetUnpaidInvoicesParams,
                                              InvoiceBatchDeleteRequest,
                                              InvoiceBatchDeleteResponse,
                                              InvoiceBatchUpdateRequest,
                                              InvoiceBatchUpdateResponse,
                                              InvoiceListResponse,
                                              InvoiceResponse,
                                              MarkAsPaidParams,
                                              UpdateInvoiceParams)
from svc.core.events.event_names import (BILLING_INVOICE_CREATED,
                                         BILLING_INVOICE_PAID,
                                         BILLING_INVOICE_UPDATED,
                                         BILLING_PAYMENT_FAILED,
                                         BILLING_SUBSCRIPTION_CANCELED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

logger = logging.getLogger(__name__)


class InvoiceService(BaseService, BatchOperationMixin):
    """账单服务类，提供账单管理相关功能

    该服务类负责：
    1. 处理账单的业务逻辑
    2. 验证请求参数和权限
    3. 调用模型层方法进行数据库操作
    4. 触发相关事件
    5. 封装结果返回
    """

    def __init__(self, repository: InvoiceRepository, redis: Optional[Redis] = None, **kwargs):
        """初始化账单服务

        Args:
            repository: 账单仓库实例
            redis: Redis客户端（可选）
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="invoice",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(repository, config, redis, **kwargs)
        self.repository = repository

    async def get_resource_by_id(self, invoice_id: int) -> Optional[Invoice]:
        """获取指定ID的账单资源

        Args:
            invoice_id: 账单ID

        Returns:
            Optional[Invoice]: 账单对象，不存在时返回None
        """
        return await self.repository.get_by_id( invoice_id)

    async def create_invoice(self, params: CreateInvoiceParams) -> Result:
        """创建账单

        Args:
            params: 创建账单参数

        Returns:
            账单结果对象
        """
        logger.info(f"开始创建账单: subscription_id={params.invoice_data.subscription_id}, amount={params.invoice_data.amount}")

        try:
            # 从Pydantic模型中提取数据，确保metadata字段被正确处理
            invoice_in = params.invoice_data
            invoice_data = invoice_in.model_dump()

            # 创建账单
            invoice = await self.repository.create(invoice_data)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 缓存账单
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": invoice.subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(BILLING_INVOICE_CREATED, payload=event_data)

            logger.info(f"账单创建成功: id={invoice.id}, subscription_id={invoice.subscription_id}")
            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"创建账单失败: subscription_id={invoice_data.get('subscription_id', 'N/A')}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"创建账单失败: {str(e)}"
            )

    async def get_invoice(self, params: GetInvoiceParams) -> Result[InvoiceResponse]:
        """获取账单

        Args:
            params: 获取账单参数

        Returns:
            账单结果对象
        """
        logger.info(f"获取账单信息: invoice_id={params.invoice_id}, user_id={params.user_id}")

        try:
            # 先尝试从缓存获取
            cached_invoice = await self._get_cached_invoice(params.invoice_id)
            if cached_invoice:
                logger.debug(f"从缓存获取到账单: invoice_id={params.invoice_id}")

                # 如果指定了用户ID，检查是否有权限访问
                if params.user_id and hasattr(cached_invoice, "user_id") and cached_invoice.user_id != params.user_id:
                    logger.warning(f"用户权限不足: invoice_id={params.invoice_id}, 期望user_id={cached_invoice.user_id}, 实际user_id={params.user_id}")
                    return self.permission_denied_result(params.invoice_id)

                return self.create_success_result(cached_invoice)

            # 从数据库获取
            invoice = await self.get_resource_by_id(params.invoice_id)
            if not invoice:
                logger.warning(f"账单不存在: invoice_id={params.invoice_id}")
                return self.resource_not_found_result(params.invoice_id)

            # 如果指定了用户ID，检查是否有权限访问（通过订阅关联检查）
            if params.user_id:
                # 获取订阅信息并检查所有者
                subscription =  invoice.subscription
                if subscription and subscription.user_id != params.user_id:
                    logger.warning(f"用户权限不足: invoice_id={params.invoice_id}, subscription_id={invoice.subscription_id}, user_id={params.user_id}")
                    return self.permission_denied_result(params.invoice_id)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 缓存账单
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"获取账单失败: invoice_id={params.invoice_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"获取账单失败: {str(e)}"
            )

    def _get_invoice_cache_key(self, invoice_id: int) -> str:
        """获取账单缓存键

        Args:
            invoice_id: 账单ID

        Returns:
            str: 缓存键
        """
        return f"invoice:{invoice_id}"

    def _get_unpaid_invoices_cache_key(self, subscription_id: int) -> str:
        """获取未支付账单缓存键

        Args:
            subscription_id: 订阅ID

        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:subscription:{subscription_id}:unpaid"

    def _get_subscription_invoices_cache_key(self, subscription_id: int, page_num: int = 1, page_size: int = 10) -> str:
        """获取订阅账单列表缓存键 (使用 page_num 和 page_size)

        Args:
            subscription_id: 订阅ID
            page_num: 页码
            page_size: 每页数量

        Returns:
            str: 缓存键
        """
        return f"subscription:{subscription_id}:invoices:page_{page_num}:size_{page_size}"

    async def get_invoices(self, params: GetInvoicesParams) -> Result[InvoiceListResponse]:
        """获取账单列表（通用方法）

        Args:
            params: 获取账单列表参数 (包含过滤和分页信息)

        Returns:
            Result[InvoiceListResponse]: 包含分页信息的账单列表结果
        """
        logger.info(f"获取账单列表: user_id={params.user_id}, sub_id={params.subscription_id}, status={params.status}, page={params.page_num}, size={params.page_size}")

        try:    

            invoices, total = await self.repository.get_paginated(
                user_id=params.user_id,
                subscription_id=params.subscription_id,
                status=params.status,
                page_num=params.page_num,
                page_size=params.page_size
            )

            # 构建响应列表
            responses = [InvoiceResponse.model_validate(inv) for inv in invoices]

            # 计算总页数
            total_pages = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0

            # 构建分页响应
            paginated_response = InvoiceListResponse(
                items=responses,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=total_pages
            )

            # 缓存结果 (如果需要缓存过滤列表)
            # if self.redis: ...

            return self.create_success_result(paginated_response)

        except Exception as e:
            logger.error(f"获取账单列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取账单列表失败: {str(e)}"
            )

    async def get_unpaid_invoices(self, params: GetUnpaidInvoicesParams) -> Result[InvoiceListResponse]:
        """获取订阅的未支付账单列表

        Args:
            params: 获取未支付账单参数

        Returns:
            Result[InvoiceListResponse]: 包含未支付账单的列表结果
        """
        logger.info(f"获取未支付账单: subscription_id={params.subscription_id}")

        try:
            # 尝试从缓存获取
            cache_key = self._get_unpaid_invoices_cache_key(params.subscription_id)
            if self.redis:
                try:
                    cached_data = await self.get_cached_resource(cache_key)
                    if cached_data:
                        logger.debug(f"从缓存获取到未支付账单: subscription_id={params.subscription_id}")
                        # 假设缓存了列表的 dict 形式
                        response_list = [InvoiceResponse.model_validate(item) for item in cached_data]
                        paginated_response = InvoiceListResponse(
                            items=response_list,
                            total=len(response_list), # 缓存时可能没有总数信息
                            page=1, # 缓存的是整个列表
                            size=len(response_list),
                            pages=1
                        )
                        return self.create_success_result(paginated_response)
                except Exception as e:
                    logger.warning(f"从缓存获取未支付账单失败: subscription_id={params.subscription_id}, 错误={str(e)}")

            # 从数据库获取
            invoices = await self.repository.get_unpaid_by_subscription(
                
                subscription_id=params.subscription_id
            )

            # 构建响应
            response_list = [InvoiceResponse.model_validate(inv) for inv in invoices]
            total = len(response_list)
            paginated_response = InvoiceListResponse(
                items=response_list,
                total=total,
                page=1,
                size=total,
                pages=1
            )

            # 缓存结果
            if self.redis:
                try:
                    await self.cache_resource(cache_key, [item.model_dump() for item in response_list], CACHE_TTL)
                    logger.debug(f"未支付账单缓存成功: subscription_id={params.subscription_id}")
                except Exception as e:
                    logger.warning(f"缓存未支付账单失败: subscription_id={params.subscription_id}, 错误={str(e)}")

            return self.create_success_result(paginated_response)

        except Exception as e:
            logger.error(f"获取未支付账单失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取未支付账单失败: {str(e)}"
            )

    async def update_invoice(self, params: UpdateInvoiceParams) -> Result:
        """更新账单

        Args:
            params: 更新账单参数

        Returns:
            账单结果对象
        """
        logger.info(f"更新账单: invoice_id={params.invoice_id}")

        try:
            # 获取账单
            invoice = await self.get_resource_by_id(params.invoice_id)
            if not invoice:
                logger.warning(f"账单不存在: invoice_id={params.invoice_id}")
                return self.resource_not_found_result(params.invoice_id)

            # 只有未支付的账单才能更新
            if invoice.is_paid:
                logger.warning(f"已支付的账单不能更新: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_PAID,
                    error_message="已支付的账单不能更新"
                )

            # 已取消的账单不能更新
            if invoice.status == "cancelled":
                logger.warning(f"已取消的账单不能更新: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_CANCELLED,
                    error_message="已取消的账单不能更新"
                )

            # 提取更新数据
            update_data = params.invoice_data.model_dump(exclude_unset=True)
            if not update_data:
                logger.debug(f"没有需要更新的数据: invoice_id={params.invoice_id}")
                return self.create_success_result(InvoiceResponse.model_validate(invoice))

            # 更新账单
            updated_invoice = await self.repository.update_by_id( params.invoice_id, update_data)
            if not updated_invoice:
                 logger.error(f"更新账单失败（未找到或更新未执行）: invoice_id={params.invoice_id}")
                 return self.create_error_result(
                     error_code=ErrorCode.OPERATION_FAILED,
                     error_message=f"更新账单失败"
                 )
            invoice = updated_invoice

            # 构建事件数据
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": invoice.subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "updated_fields": list(update_data.keys()),
                "updated_at": invoice.updated_at.isoformat() if invoice.updated_at else None
            }

            # 触发账单更新事件
            dispatch(BILLING_INVOICE_UPDATED, payload=event_data)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 更新缓存
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            # 如果是未支付账单，更新未支付账单缓存
            if not invoice.is_paid and invoice.status != "cancelled":
                await self._clear_unpaid_invoices_cache(invoice.subscription_id)

            logger.info(f"账单更新成功: invoice_id={invoice.id}, 更新字段={list(update_data.keys())}")

            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"更新账单失败: invoice_id={params.invoice_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新账单失败: {str(e)}"
            )

    async def mark_as_paid(self, params: MarkAsPaidParams) -> Result[InvoiceResponse]:
        """将账单标记为已支付

        Args:
            params: 标记为已支付参数

        Returns:
            账单结果对象
        """
        logger.info(f"标记账单为已支付: invoice_id={params.invoice_id}, payment_id={params.payment_id}")

        try:
            # 获取账单
            invoice = await self.get_resource_by_id(params.invoice_id)
            if not invoice:
                logger.warning(f"账单不存在: invoice_id={params.invoice_id}")
                return self.resource_not_found_result(params.invoice_id)

            # 已支付的账单不能重复标记
            if invoice.is_paid:
                logger.warning(f"账单已经标记为已支付: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_PAID,
                    error_message="账单已经标记为已支付"
                )

            # 已取消的账单不能标记为已支付
            if invoice.status == "cancelled":
                logger.warning(f"已取消的账单不能标记为已支付: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_CANCELLED,
                    error_message="已取消的账单不能标记为已支付"
                )

            # 标记为已支付
            paid_time = get_utc_now_without_tzinfo()
            
            # 更新账单状态和支付详情
            update_data = {
                "payment_id": params.payment_id,
                "payment_method": params.payment_method,
                "status": "paid",
                "paid_at": paid_time,
                "is_paid": True
            }
            
            updated_invoice = await self.repository.update_by_id(params.invoice_id, update_data)
            if not updated_invoice:
                logger.error(f"更新账单为已支付失败: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"标记账单为已支付失败"
                )

            invoice = updated_invoice

            # 构建事件数据
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": invoice.subscription_id,
                "user_id": invoice.subscription.user_id if invoice.subscription else None,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "payment_id": params.payment_id,
                "payment_method": params.payment_method,
                "status": invoice.status,
                "paid_at": invoice.paid_at.isoformat() if invoice.paid_at else None,
                "meta_data": invoice.meta_data
            }

            # 触发账单支付事件
            dispatch(BILLING_INVOICE_PAID, payload=event_data)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 更新缓存
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            # 清除未支付账单缓存
            await self._clear_unpaid_invoices_cache(invoice.subscription_id)

            logger.info(f"账单已标记为已支付: invoice_id={invoice.id}, payment_id={params.payment_id}")

            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"标记账单为已支付失败: invoice_id={params.invoice_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"标记账单为已支付失败: {str(e)}"
            )

    async def cancel_invoice(self, params: CancelInvoiceParams) -> Result[InvoiceResponse]:
        """取消账单

        Args:
            params: 取消账单参数

        Returns:
            账单结果对象
        """
        logger.info(f"取消账单: invoice_id={params.invoice_id}")

        try:
            # 获取账单
            invoice = await self.get_resource_by_id(params.invoice_id)
            if not invoice:
                logger.warning(f"账单不存在: invoice_id={params.invoice_id}")
                return self.resource_not_found_result(params.invoice_id)

            # 已支付的账单不能取消
            if invoice.is_paid:
                logger.warning(f"已支付的账单不能取消: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_PAID,
                    error_message="已支付的账单不能取消"
                )

            # 已取消的账单不能重复取消
            if invoice.status == "cancelled":
                logger.warning(f"账单已经取消: invoice_id={params.invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_CANCELLED,
                    error_message="账单已经取消"
                )

            # 标记为已取消
            core_updated_invoice = await self.repository.update_by_id(params.invoice_id, {"status": "cancelled"})
            if not core_updated_invoice:
                 logger.error(f"标记账单核心状态为已取消失败: invoice_id={params.invoice_id}")
                 return self.create_error_result(
                     error_code=ErrorCode.OPERATION_FAILED,
                     error_message=f"取消账单失败（核心状态）"
                 )

            # Update cancellation details
            cancelled_time = get_utc_now_without_tzinfo()
            cancellation_details_data = {
                "cancelled_at": cancelled_time,
                "cancellation_reason": params.reason,
                "status": "cancelled"
            }
            final_updated_invoice = await self.repository.update_by_id( params.invoice_id, cancellation_details_data)
            if not final_updated_invoice:
                 logger.error(f"更新账单取消详情失败: invoice_id={params.invoice_id}")
                 return self.create_error_result(
                     error_code=ErrorCode.OPERATION_FAILED,
                     error_message=f"取消账单失败（取消详情）"
                 )

            invoice = final_updated_invoice

            # 构建事件数据
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": invoice.subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "status": invoice.status,
                "reason": invoice.cancellation_reason,
                "cancelled_at": invoice.cancelled_at.isoformat() if invoice.cancelled_at else None,
                "meta_data": invoice.meta_data
            }

            # 触发账单取消事件
            dispatch(BILLING_SUBSCRIPTION_CANCELED, payload=event_data)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 更新缓存
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            # 清除未支付账单缓存
            await self._clear_unpaid_invoices_cache(invoice.subscription_id)

            logger.info(f"账单已取消: invoice_id={invoice.id}, reason={invoice.cancellation_reason}")

            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"取消账单失败: invoice_id={params.invoice_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"取消账单失败: {str(e)}"
            )

    async def mark_as_overdue(self, invoice_id: int) -> Result[InvoiceResponse]:
        """将账单标记为逾期

        Args:
            invoice_id: 账单ID

        Returns:
            账单结果对象
        """
        logger.info(f"标记账单为逾期: invoice_id={invoice_id}")

        try:
            # 获取账单
            invoice = await self.get_resource_by_id(invoice_id)
            if not invoice:
                logger.warning(f"账单不存在: invoice_id={invoice_id}")
                return self.resource_not_found_result(invoice_id)

            # 已支付的账单不能标记为逾期
            if invoice.is_paid:
                logger.warning(f"已支付的账单不能标记为逾期: invoice_id={invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_PAID,
                    error_message="已支付的账单不能标记为逾期"
                )

            # 已取消的账单不能标记为逾期
            if invoice.status == "cancelled":
                logger.warning(f"已取消的账单不能标记为逾期: invoice_id={invoice_id}")
                return self.create_error_result(
                    error_code=ErrorCode.INVOICE_CANCELLED,
                    error_message="已取消的账单不能标记为逾期"
                )

            # 已逾期的账单不需要重复标记
            if invoice.status == "overdue":
                logger.debug(f"账单已经是逾期状态: invoice_id={invoice_id}")
                return self.create_success_result(InvoiceResponse.model_validate(invoice))

            # 标记为逾期
            overdue_time = get_utc_now_without_tzinfo()
            core_updated_invoice = await self.repository.mark_as_overdue( invoice_id)
            if not core_updated_invoice:
                 logger.error(f"标记账单核心状态为逾期失败: invoice_id={invoice_id}")
                 return self.create_error_result(
                     error_code=ErrorCode.OPERATION_FAILED,
                     error_message=f"标记账单为逾期失败（核心状态）"
                 )

            # Update overdue details
            overdue_details_data = {
                "overdue_at": overdue_time,
                "status": "overdue"
            }
            final_updated_invoice = await self.repository.update_by_id( invoice_id, overdue_details_data)
            if not final_updated_invoice:
                 logger.error(f"更新账单逾期详情失败: invoice_id={invoice_id}")
                 return self.create_error_result(
                     error_code=ErrorCode.OPERATION_FAILED,
                     error_message=f"标记账单为逾期失败（逾期详情）"
                 )

            invoice = final_updated_invoice

            # 构建事件数据
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": invoice.subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "status": invoice.status,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "overdue_at": invoice.overdue_at.isoformat() if invoice.overdue_at else None,
                "meta_data": invoice.meta_data
            }

            # 触发账单逾期事件
            dispatch(BILLING_PAYMENT_FAILED, payload=event_data)

            # 构建响应
            response = InvoiceResponse.model_validate(invoice)

            # 更新缓存
            await self.cache_resource(self._get_invoice_cache_key(invoice.id), response, CACHE_TTL)

            logger.info(f"账单已标记为逾期: invoice_id={invoice.id}")

            return self.create_success_result(response)

        except Exception as e:
            logger.error(f"标记账单为逾期失败: invoice_id={invoice_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"标记账单为逾期失败: {str(e)}"
            )



    async def _get_cached_invoice(self, invoice_id: int) -> Optional[InvoiceResponse]:
        """从缓存获取账单信息

        Args:
            invoice_id: 账单ID

        Returns:
            账单响应对象，如果不存在则返回None
        """
        if not self.redis:
            return None

        try:
            key = self._get_invoice_cache_key(invoice_id)
            return await self.get_cached_resource(
                key,
                lambda data: InvoiceResponse.model_validate(data)
            )
        except Exception as e:
            # 获取缓存失败不应影响主要业务逻辑
            logger.warning(f"从缓存获取账单失败: invoice_id={invoice_id}, 错误={str(e)}")

        return None

    async def _clear_unpaid_invoices_cache(self, subscription_id: int) -> None:
        """清除未支付账单缓存

        Args:
            subscription_id: 订阅ID
        """
        if not self.redis:
            return

        try:
            cache_key = self._get_unpaid_invoices_cache_key(subscription_id)
            await self.delete_cache(cache_key)
            logger.debug(f"清除未支付账单缓存: subscription_id={subscription_id}")
        except Exception as e:
            # 清除缓存失败不应影响主要业务逻辑
            logger.warning(f"清除未支付账单缓存失败: subscription_id={subscription_id}, 错误={str(e)}")
    
    # === 批量操作方法 ===
    
    async def batch_update_invoices(self, request: InvoiceBatchUpdateRequest) -> Result[InvoiceBatchUpdateResponse]:
        """批量更新账单"""
        logger.info(f"批量更新账单: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.repository,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_invoices(self, request: InvoiceBatchDeleteRequest) -> Result[InvoiceBatchDeleteResponse]:
        """批量删除账单"""
        logger.info(f"批量删除账单: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.repository,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"invoice:{resource_id}"