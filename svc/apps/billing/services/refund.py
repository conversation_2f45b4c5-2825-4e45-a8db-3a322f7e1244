"""
退款服务

处理退款相关的业务逻辑
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch

from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService, BatchOperationMixin, ServiceConfig
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

from ..models.refund import Refund, RefundStatus, RefundType
from ..repositories.invoice import InvoiceRepository
from ..repositories.refund import RefundRepository
from ..repositories.subscription import SubscriptionRepository
from ..schemas.refund import (CreateRefundRequest, RefundListResponse,
                              RefundResponse)


class RefundService(BaseService, BatchOperationMixin):
    """退款服务类"""

    def __init__(self, refund_repo: RefundRepository, invoice_repo: InvoiceRepository = None, subscription_repo: SubscriptionRepository = None, **kwargs):
        config = ServiceConfig(
            resource_type="refund",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(refund_repo, config, **kwargs)

        self.refund_repo = refund_repo
        self.invoice_repo = invoice_repo
        self.subscription_repo = subscription_repo
    
    async def create_refund(self, request: CreateRefundRequest) -> RefundResponse:
        """创建退款记录
        
        Args:
            request: 创建退款请求
            
        Returns:
            RefundResponse: 退款响应
        """
        try:
            self.logger.info(f"创建退款记录: subscription_id={request.subscription_id}, amount={request.amount}")
            
            # 验证订阅存在
            subscription = await self.subscription_repo.get_by_id(request.subscription_id)
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.SUBSCRIPTION_NOT_FOUND,
                    error_message="订阅不存在"
                )
            
            # 验证账单存在（如果指定了账单ID）
            if request.invoice_id:
                invoice = await self.invoice_repo.get_by_id(request.invoice_id)
                if not invoice:
                    return self.create_error_result(
                        error_code=ErrorCode.INVOICE_NOT_FOUND,
                        error_message="账单不存在"
                    )
                
                # 验证账单属于该订阅
                if invoice.subscription_id != request.subscription_id:
                    return self.create_error_result(
                        error_code=ErrorCode.INVALID_INPUT,
                        error_message="账单不属于该订阅"
                    )
            
            # 创建退款记录
            refund_data = {
                "subscription_id": request.subscription_id,
                "invoice_id": request.invoice_id,
                "user_id": subscription.user_id,
                "amount": request.amount,
                "currency": request.currency or "CNY",
                "status": RefundStatus.PENDING.value,
                "refund_type": request.refund_type,
                "reason": request.reason,
                "description": request.description,
                "refund_date": get_utc_now_without_tzinfo(),
                "meta_data": request.meta_data or {}
            }
            
            refund = await self.repository.create(refund_data)
            
            # 创建退款账单（负金额账单）
            await self._create_refund_invoice(refund, subscription)
            
            # 触发退款创建事件
            await self._trigger_refund_event(refund, "created")
            
            self.logger.info(f"退款记录创建成功: refund_id={refund.id}")
            return self.create_success_result(RefundResponse.model_validate(refund))
            
        except Exception as e:
            self.logger.error(f"创建退款记录失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.CREATE_FAILED,
                error_message=f"创建退款记录失败: {str(e)}"
            )
    
    async def process_refund(self, refund_id: int, processor_id: int, 
                           processing_notes: Optional[str] = None,
                           external_refund_id: Optional[str] = None) -> RefundResponse:
        """处理退款
        
        Args:
            refund_id: 退款ID
            processor_id: 处理人ID
            processing_notes: 处理备注
            external_refund_id: 外部退款ID
            
        Returns:
            RefundResponse: 退款响应
        """
        try:
            self.logger.info(f"处理退款: refund_id={refund_id}, processor_id={processor_id}")
            
            # 获取退款记录
            refund = await self.repository.get_by_id(refund_id)
            if not refund:
                return self.create_error_result(
                    error_code=ErrorCode.NOT_FOUND,
                    error_message="退款记录不存在"
                )
            
            # 检查退款状态
            if refund.status != RefundStatus.PENDING.value:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"退款状态为 {refund.status}，无法处理"
                )
            
            # 更新退款状态
            updated_refund = await self.repository.update_status(
                refund_id=refund_id,
                status=RefundStatus.PROCESSED.value,
                processor_id=processor_id,
                processing_notes=processing_notes,
                external_refund_id=external_refund_id
            )
            
            # 触发退款处理完成事件
            await self._trigger_refund_event(updated_refund, "processed")
            
            self.logger.info(f"退款处理完成: refund_id={refund_id}")
            return self.create_success_result(RefundResponse.model_validate(updated_refund))
            
        except Exception as e:
            self.logger.error(f"处理退款失败: refund_id={refund_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"处理退款失败: {str(e)}"
            )
    
    async def cancel_refund(self, refund_id: int, reason: str) -> RefundResponse:
        """取消退款
        
        Args:
            refund_id: 退款ID
            reason: 取消原因
            
        Returns:
            RefundResponse: 退款响应
        """
        try:
            self.logger.info(f"取消退款: refund_id={refund_id}, reason={reason}")
            
            # 获取退款记录
            refund = await self.repository.get_by_id(refund_id)
            if not refund:
                return self.create_error_result(
                    error_code=ErrorCode.NOT_FOUND,
                    error_message="退款记录不存在"
                )
            
            # 检查是否可以取消
            if not refund.can_be_canceled:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"退款状态为 {refund.status}，无法取消"
                )
            
            # 更新退款状态
            updated_refund = await self.repository.update_status(
                refund_id=refund_id,
                status=RefundStatus.CANCELED.value,
                processing_notes=f"取消原因: {reason}"
            )
            
            # 取消对应的退款账单
            await self._cancel_refund_invoice(refund)
            
            # 触发退款取消事件
            await self._trigger_refund_event(updated_refund, "canceled")
            
            self.logger.info(f"退款取消成功: refund_id={refund_id}")
            return self.create_success_result(RefundResponse.model_validate(updated_refund))
            
        except Exception as e:
            self.logger.error(f"取消退款失败: refund_id={refund_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"取消退款失败: {str(e)}"
            )
    
    async def get_user_refunds(self, user_id: int, page_num: int = 1, page_size: int = 20,
                              status: Optional[str] = None,
                              refund_type: Optional[str] = None) -> RefundListResponse:
        """获取用户退款列表
        
        Args:
            user_id: 用户ID
            page_num: 页码
            page_size: 每页大小
            status: 状态过滤
            refund_type: 类型过滤
            
        Returns:
            RefundListResponse: 退款列表响应
        """
        try:
            refunds, total = await self.repository.get_by_user(
                user_id=user_id,
                page_num=page_num,
                page_size=page_size,
                status=status,
                refund_type=refund_type
            )
            
            refund_responses = [RefundResponse.model_validate(refund) for refund in refunds]
            
            return RefundListResponse(
                items=refund_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                total_pages=(total + page_size - 1) // page_size
            )
            
        except Exception as e:
            self.logger.error(f"获取用户退款列表失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return RefundListResponse(items=[], total=0, page_num=page_num, page_size=page_size, total_pages=0)
    
    async def _create_refund_invoice(self, refund: Refund, subscription) -> None:
        """创建退款账单（负金额账单）
        
        Args:
            refund: 退款记录
            subscription: 订阅对象
        """
        try:
            # 创建负金额账单表示退款
            invoice_data = {
                "subscription_id": refund.subscription_id,
                "amount": -refund.amount,  # 负金额表示退款
                "currency": refund.currency,
                "status": "paid",  # 退款账单直接标记为已支付
                "description": f"退款 - {refund.get_display_type()}",
                "invoice_date": refund.refund_date,
                "paid_at": refund.refund_date,
                "is_paid": True,
                "meta_data": {
                    "billing_type": "refund",
                    "refund_id": refund.id,
                    "refund_type": refund.refund_type,
                    "original_invoice_id": refund.invoice_id
                }
            }
            
            await self.invoice_repo.create(invoice_data)
            self.logger.info(f"退款账单创建成功: refund_id={refund.id}")
            
        except Exception as e:
            self.logger.error(f"创建退款账单失败: refund_id={refund.id}, 错误={str(e)}", exc_info=True)
    
    async def _cancel_refund_invoice(self, refund: Refund) -> None:
        """取消退款账单
        
        Args:
            refund: 退款记录
        """
        try:
            # 查找对应的退款账单
            invoices, _ = await self.invoice_repo.get_by_subscription(refund.subscription_id)
            refund_invoices = [
                inv for inv in invoices
                if inv.meta_data and inv.meta_data.get("refund_id") == refund.id
            ]
            
            # 取消退款账单
            for invoice in refund_invoices:
                await self.invoice_repo.update(invoice, {"status": "canceled"})
                self.logger.info(f"退款账单已取消: invoice_id={invoice.id}")
                
        except Exception as e:
            self.logger.error(f"取消退款账单失败: refund_id={refund.id}, 错误={str(e)}", exc_info=True)
    
    async def _trigger_refund_event(self, refund: Refund, action: str) -> None:
        """触发退款事件
        
        Args:
            refund: 退款记录
            action: 动作类型 (created, processed, canceled)
        """
        try:
            event_data = {
                "refund_id": refund.id,
                "subscription_id": refund.subscription_id,
                "user_id": refund.user_id,
                "amount": refund.amount,
                "currency": refund.currency,
                "status": refund.status,
                "refund_type": refund.refund_type,
                "action": action,
                "timestamp": get_utc_now_without_tzinfo().isoformat()
            }
            
            # 根据动作类型选择事件名称
            event_mapping = {
                "created": event_names.BILLING_REFUND_REQUESTED,
                "processed": "billing:refund:processed",
                "canceled": "billing:refund:canceled"
            }
            
            event_name = event_mapping.get(action, event_names.BILLING_REFUND_REQUESTED)
            dispatch(event_name, payload=event_data)
            
        except Exception as e:
            self.logger.error(f"触发退款事件失败: refund_id={refund.id}, action={action}, 错误={str(e)}", exc_info=True)
        except Exception as e:
            self.logger.error(f"触发退款事件失败: refund_id={refund.id}, action={action}, 错误={str(e)}", exc_info=True)
