"""
订阅服务模块，提供面向对象形式的订阅管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

from datetime import timedelta
from typing import Any, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.models.subscription import Subscription
from svc.apps.billing.repositories.subscription import SubscriptionRepository
from svc.apps.billing.repositories.subscription_plan import \
    SubscriptionPlanRepository
from svc.apps.billing.schemas.subscription import (
    CancelSubscriptionParams, ChangePlanParams, CreateSubscriptionParams,
    GetSubscriptionParams, GetSubscriptionsParams, PauseSubscriptionParams,
    ReactivateSubscriptionParams, RenewSubscriptionParams,
    ResumeSubscriptionParams, SubscriptionBatchDeleteRequest,
    SubscriptionBatchDeleteResponse, SubscriptionBatchUpdateRequest,
    SubscriptionBatchUpdateResponse, SubscriptionListResponse,
    SubscriptionResponse, UpdateSubscriptionParams, UserSubscriptionsResult)
from svc.apps.billing.schemas.subscription_plan import SubscriptionPlanResponse
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CACHE_VERSION
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

from ..models.refund import RefundType
from ..models.subscription import SubscriptionEvent, SubscriptionStatus
from ..services.refund import RefundService
from ..utils.billing_calculator import BillingCalculator, BillingCycle
from ..utils.validators import SubscriptionValidator

# 定义缺失的参数类


# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

class SubscriptionService(BaseService, BatchOperationMixin):
    """订阅服务类，提供订阅管理相关功能
    
    该服务类负责：
    1. 处理订阅的业务逻辑
    2. 验证请求参数和权限
    3. 通过仓库层访问数据
    4. 触发相关事件
    5. 管理缓存
    """
    
    # 设置资源类型名称
    resource_type = "subscription"
    
    def __init__(
        self,
        subscription_repo: SubscriptionRepository,
        subscription_plan_repo: SubscriptionPlanRepository,
        invoice_repo=None,  # 账单仓库，用于生成账单
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化订阅服务

        Args:
            subscription_repo: 订阅仓库
            subscription_plan_repo: 订阅计划仓库
            invoice_repo: 账单仓库，用于生成账单
            redis: Redis客户端
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="subscription",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(subscription_repo, config, redis, **kwargs)

        self.subscription_repo = subscription_repo
        self.subscription_plan_repo = subscription_plan_repo
        self.invoice_repo = invoice_repo
        # 初始化退款服务（需要session，这里暂时设为None，在实际使用时初始化）
        self.refund_service = None
    
    async def get_resource_by_id(self, subscription_id: int) -> Optional[Subscription]:
        """获取指定ID的订阅资源
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            Optional[Subscription]: 订阅对象，不存在时返回None
        """
        return await self.subscription_repo.get_by_id(subscription_id)
    
    async def create_subscription(self, params: CreateSubscriptionParams) -> Result[SubscriptionResponse]:
        """重构后的订阅创建方法

        使用新的状态机和业务规则验证，提供更清晰的订阅创建流程

        Args:
            params: 创建订阅参数

        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始创建订阅: user_id={params.user_id}, plan_id={params.subscription_data.plan_id}")

        try:
            # 1. 获取订阅计划
            plan = await self.subscription_plan_repo.get_by_id(params.subscription_data.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: plan_id={params.subscription_data.plan_id}")
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message=ErrorCode.get_message(ErrorCode.PLAN_NOT_FOUND)
                )

            # 2. 获取用户现有订阅
            existing_subscriptions, _ = await self.subscription_repo.get_paginated(
                page_num=1,
                page_size=10,
                user_id=params.user_id
            )

            # 3. 业务规则验证
            validation_result = await self._validate_subscription_creation(params.user_id, plan, existing_subscriptions)
            if not validation_result.is_success:
                return validation_result

            # 4. 确定初始状态和计费周期
            initial_status = self._determine_initial_status(plan)
            billing_cycle = BillingCalculator.calculate_initial_cycle(plan)

            # 5. 创建订阅数据
            subscription_data = self._build_subscription_data(params, plan, initial_status, billing_cycle)

            # 6. 创建订阅
            subscription = await self.subscription_repo.create(subscription_data)
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.CREATE_FAILED,
                    error_message="创建订阅失败"
                )

            # 7. 处理后续业务逻辑
            await self._handle_post_creation(subscription, plan)

            # 8. 构建响应
            response = await self._build_subscription_response(subscription, plan)

            # 9. 触发事件
            await self._trigger_subscription_event(subscription, SubscriptionEvent.CREATE, plan)

            self.logger.info(f"订阅创建成功: subscription_id={subscription.id}, status={subscription.status}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"订阅创建失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"创建订阅时发生内部错误: {str(e)}"
            )

    async def _validate_subscription_creation(self, user_id: int, plan, existing_subscriptions: list) -> Result[None]:
        """验证订阅创建的业务规则

        Args:
            user_id: 用户ID
            plan: 订阅计划
            existing_subscriptions: 现有订阅列表

        Returns:
            Result[None]: 验证结果
        """
        # 使用验证器进行业务规则验证
        is_valid, error_message, error_code = SubscriptionValidator.validate_subscription_creation(
            user_id, plan, existing_subscriptions
        )

        if not is_valid:
            self.logger.warning(f"订阅创建验证失败: user_id={user_id}, error={error_message}")
            return self.create_error_result(
                error_code=error_code,
                error_message=error_message
            )

        return self.create_success_result(None)

    def _determine_initial_status(self, plan) -> SubscriptionStatus:
        """确定订阅的初始状态

        Args:
            plan: 订阅计划

        Returns:
            SubscriptionStatus: 初始状态
        """
        # 如果有试用期，直接进入试用状态
        if plan.trial_period_days and plan.trial_period_days > 0:
            return SubscriptionStatus.TRIALING

        # 免费计划直接激活
        if plan.price <= 0:
            return SubscriptionStatus.ACTIVE

        # 付费计划需要支付后激活
        return SubscriptionStatus.DRAFT

    def _build_subscription_data(self, params: CreateSubscriptionParams, plan,
                               initial_status: SubscriptionStatus, billing_cycle: BillingCycle) -> dict:
        """构建订阅创建数据

        Args:
            params: 创建参数
            plan: 订阅计划
            initial_status: 初始状态
            billing_cycle: 计费周期

        Returns:
            dict: 订阅数据
        """
        subscription_data = params.subscription_data.model_dump()
        subscription_data.update({
            "user_id": params.user_id,
            "status": initial_status.value,
            "current_period_start": billing_cycle.start,
            "current_period_end": billing_cycle.end,
            "trial_start": billing_cycle.trial_start,
            "trial_end": billing_cycle.trial_end,
            "meta_data": self._build_initial_metadata(params, plan, initial_status)
        })

        return subscription_data

    def _build_initial_metadata(self, params: CreateSubscriptionParams, plan,
                               initial_status: SubscriptionStatus) -> dict:
        """构建初始元数据

        Args:
            params: 创建参数
            plan: 订阅计划
            initial_status: 初始状态

        Returns:
            dict: 元数据
        """
        metadata = {
            "creation_source": "api",
            "initial_status": initial_status.value,
            "plan_snapshot": {
                "name": plan.name,
                "price": plan.price,
                "currency": plan.currency,
                "interval": plan.interval,
                "interval_count": plan.interval_count
            },
            "business_rules": {
                "trial_eligible": plan.trial_period_days > 0 if plan.trial_period_days else False,
                "requires_payment": initial_status == SubscriptionStatus.DRAFT
            }
        }

        # 添加用户提供的元数据
        if hasattr(params.subscription_data, 'meta_data') and params.subscription_data.meta_data:
            metadata.update(params.subscription_data.meta_data)

        return metadata

    async def _handle_post_creation(self, subscription, plan) -> None:
        """处理订阅创建后的业务逻辑

        Args:
            subscription: 订阅对象
            plan: 订阅计划
        """
        try:
            # 根据状态处理不同的后续逻辑
            if subscription.status == SubscriptionStatus.DRAFT.value:
                # 草稿状态需要生成账单
                await self._generate_initial_invoice(subscription, plan)
            elif subscription.status == SubscriptionStatus.TRIALING.value:
                # 试用期状态可能需要设置提醒
                await self._schedule_trial_end_reminder(subscription)
            elif subscription.status == SubscriptionStatus.ACTIVE.value:
                # 活跃状态需要设置续费提醒
                await self._schedule_renewal_reminder(subscription)

            # 清除相关缓存
            await self._clear_user_subscription_cache(subscription.user_id)

        except Exception as e:
            self.logger.error(f"处理订阅创建后逻辑失败: subscription_id={subscription.id}, error={str(e)}")
            # 不抛出异常，避免影响主流程

    async def _build_subscription_response(self, subscription, plan) -> SubscriptionResponse:
        """构建订阅响应对象

        Args:
            subscription: 订阅对象
            plan: 订阅计划

        Returns:
            SubscriptionResponse: 响应对象
        """
        response = SubscriptionResponse.model_validate(subscription)
        response.plan = SubscriptionPlanResponse.model_validate(plan)

        # 缓存响应
        await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)

        return response

    async def _trigger_subscription_event(self, subscription, event: SubscriptionEvent, plan=None) -> None:
        """触发订阅事件

        Args:
            subscription: 订阅对象
            event: 事件类型
            plan: 订阅计划（可选）
        """
        try:
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "event": event.value,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                "trial_start": subscription.trial_start.isoformat() if subscription.trial_start else None,
                "trial_end": subscription.trial_end.isoformat() if subscription.trial_end else None,
                "timestamp": get_utc_now_without_tzinfo().isoformat()
            }

            # 添加计划信息
            if plan:
                event_data.update({
                    "plan_name": plan.name,
                    "plan_price": plan.price,
                    "plan_currency": plan.currency,
                    "plan_interval": plan.interval
                })

            # 根据事件类型选择事件名称
            event_name_mapping = {
                SubscriptionEvent.CREATE: event_names.BILLING_SUBSCRIPTION_CREATED,
                SubscriptionEvent.ACTIVATE: event_names.BILLING_SUBSCRIPTION_ACTIVATED,
                SubscriptionEvent.CANCEL: event_names.BILLING_SUBSCRIPTION_CANCELED,
                SubscriptionEvent.EXPIRE: event_names.BILLING_SUBSCRIPTION_EXPIRED,
                SubscriptionEvent.UPGRADE: event_names.BILLING_SUBSCRIPTION_UPGRADED,
                SubscriptionEvent.DOWNGRADE: event_names.BILLING_SUBSCRIPTION_DOWNGRADED,
            }

            event_name = event_name_mapping.get(event, event_names.BILLING_SUBSCRIPTION_UPDATED)
            dispatch(event_name, payload=event_data)

        except Exception as e:
            self.logger.error(f"触发订阅事件失败: subscription_id={subscription.id}, event={event}, error={str(e)}")

    async def _schedule_trial_end_reminder(self, subscription) -> None:
        """安排试用期结束提醒

        Args:
            subscription: 订阅对象
        """
        # TODO: 实现试用期结束提醒逻辑
        pass

    async def _schedule_renewal_reminder(self, subscription) -> None:
        """安排续费提醒

        Args:
            subscription: 订阅对象
        """
        # TODO: 实现续费提醒逻辑
        pass

    async def _clear_user_subscription_cache(self, user_id: int) -> None:
        """清除用户订阅相关缓存

        Args:
            user_id: 用户ID
        """
        try:
            cache_keys = [
                f"user_subscription:{user_id}",
                f"active_subscription:{user_id}",
                f"user_subscriptions:{user_id}"
            ]

            for key in cache_keys:
                await self.cache_resource(key, None, 0)  # TTL为0表示删除

        except Exception as e:
            self.logger.error(f"清除用户订阅缓存失败: user_id={user_id}, error={str(e)}")

    async def get_subscription(self, params: GetSubscriptionParams) -> Result[SubscriptionResponse]:
        """获取订阅
        
        Args:
            params: 获取订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"获取订阅信息: subscription_id={params.subscription_id}")
        
        try:
            # 先尝试从缓存获取
            cached_response = await self._get_cached_subscription(params.subscription_id)
            if cached_response:
                self.logger.debug(f"从缓存获取到订阅: subscription_id={params.subscription_id}")
                # 验证用户权限
                if params.user_id and cached_response.user_id != params.user_id:
                    return self.permission_denied_result(params.subscription_id)
                return self.create_success_result(cached_response)
            
            # 从数据库获取
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                return self.resource_not_found_result(params.subscription_id)
            
            # 验证用户权限
            if params.user_id and subscription.user_id != params.user_id:
                return self.permission_denied_result(params.subscription_id)
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="订阅计划不存在"
                )
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存结果
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取订阅失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取订阅失败: {str(e)}"
            )
    
    async def get_subscriptions(self, params: GetSubscriptionsParams) -> Result[SubscriptionListResponse]:
        """获取订阅列表
        
        Args:
            params: 获取订阅列表参数
            
        Returns:
            订阅列表结果对象
        """
        self.logger.info(f"获取订阅列表: user_id={params.user_id}, status={params.status}, page_num={params.page_num}, page_size={params.page_size}")
        
        try:
            filters = {}
            if params.user_id is not None:
                filters['user_id'] = params.user_id
            if params.status is not None:
                filters['status'] = params.status

            subscriptions, total = await self.subscription_repo.get_paginated(
                page_num=params.page_num,
                page_size=params.page_size,
                order_by="created_at",
                order_direction="desc",
                **filters
            )

            import math
            page_count = math.ceil(total / params.page_size) if params.page_size > 0 else 0

            response = SubscriptionListResponse(
                items=[SubscriptionResponse.model_validate(s) for s in subscriptions],
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )

            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取订阅列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取订阅列表失败: {str(e)}"
            )
    
    async def get_active_subscription(self, user_id: int) -> Result[SubscriptionResponse]:
        """获取用户的活跃订阅"""
        try:
            self.logger.info(f"获取用户活跃订阅: user_id={user_id}")
            
            # 检查缓存
            cache_key = self._get_active_subscription_cache_key(user_id)
            cached_data = await self.get_cached_resource(
                cache_key,
                lambda data: SubscriptionResponse.model_validate(data)
            )
            
            if cached_data:
                self.logger.debug(f"从缓存获取活跃订阅: user_id={user_id}")
                return self.create_success_result(cached_data)
            
            # 从数据库获取活跃订阅（不包括pending状态）
            subscriptions, _ = await self.subscription_repo.get_paginated(
                filters={"user_id": user_id, "status": "active"},
                page_num=1,
                page_size=1
            )
            subscription = subscriptions[0] if subscriptions else None
            
            # 检查订阅是否存在且不是即将取消的订阅
            if not subscription or subscription.cancel_at_period_end:
                return self.create_error_result(
                    error_code=ErrorCode.SUBSCRIPTION_NOT_FOUND,
                    error_message="用户没有活跃的订阅"
                )
            
            response = SubscriptionResponse.model_validate(subscription.to_dict())
            
            # 缓存结果
            await self.cache_resource(self._get_active_subscription_cache_key(user_id), response, CACHE_TTL)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取活跃订阅失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"获取活跃订阅失败: {str(e)}"
            )
    
    async def update_subscription(self, params: UpdateSubscriptionParams) -> Result[SubscriptionResponse]:
        """更新订阅
        
        Args:
            params: 更新订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"更新订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if params.user_id and subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: subscription_id={params.subscription_id}, 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 提取更新数据
            update_data = params.subscription_data.model_dump(exclude_unset=True)
            if not update_data:
                self.logger.debug(f"没有需要更新的数据: subscription_id={params.subscription_id}")
                
                # 不进行额外查询，直接返回当前订阅
                # 获取关联的计划
                plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
                
                # 构建响应
                response = SubscriptionResponse.model_validate(subscription)
                if plan:
                    response.plan = SubscriptionPlanResponse.model_validate(plan)
                    
                return self.create_success_result(response)
            
            # 如果更新了关键字段，需要清除用户的活跃订阅缓存
            clear_active_cache = any(field in update_data for field in ["status", "end_date"])
            
            # 检查计划ID是否发生变化
            if "plan_id" in update_data:
                plan = await self.subscription_plan_repo.get_by_id(update_data["plan_id"])
                if not plan:
                    self.logger.warning(f"订阅计划不存在: plan_id={update_data['plan_id']}")
                    return self.create_error_result(
                        error_code=ErrorCode.PLAN_NOT_FOUND,
                        error_message="订阅计划不存在"
                    )
            
            # 记录更新前的重要字段
            old_status = subscription.status
            old_plan_id = subscription.plan_id
            
            # 使用仓库方法更新订阅
            subscription = await self.subscription_repo.update(
                subscription,
                update_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.UPDATE_FAILED,
                    error_message="更新订阅失败，无法查找到订阅"
                )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 如果需要，清除活跃订阅缓存
            if clear_active_cache:
                await self._clear_active_subscription_cache(subscription.user_id)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "updated_fields": list(update_data.keys()),
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加状态变更信息
            if "status" in update_data and old_status != subscription.status:
                event_data["old_status"] = old_status
                event_data["new_status"] = subscription.status
            
            # 添加计划变更信息
            if "plan_id" in update_data and old_plan_id != subscription.plan_id:
                event_data["old_plan_id"] = old_plan_id
                event_data["new_plan_id"] = subscription.plan_id
                if plan:
                    event_data["new_plan_name"] = plan.name
                    event_data["new_plan_price"] = plan.price
                    event_data["new_plan_currency"] = plan.currency
            
            # 触发订阅更新事件
            dispatch(event_names.BILLING_SUBSCRIPTION_UPDATED, payload=event_data)
            
            self.logger.info(f"订阅更新成功: subscription_id={params.subscription_id}, 更新字段={list(update_data.keys())}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更新订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"更新订阅失败: {str(e)}"
            )
    
    async def cancel_subscription(self, params: CancelSubscriptionParams) -> Result:
        """取消订阅
        
        Args:
            params: 取消订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始取消订阅: subscription_id={params.subscription_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                return self.resource_not_found_result(params.subscription_id)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status not in ["active", "past_due"]:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"无法取消状态为 {subscription.status} 的订阅"
                )
            
            # 更新订阅状态
            now = get_utc_now_without_tzinfo()
            meta_data = {"cancel_reason": params.reason} if params.reason else {}
            
            if params.at_period_end:
                # 在当前周期结束时取消
                update_data = {
                    "cancel_at_period_end": True,
                    "canceled_at": now,
                    "meta_data": meta_data
                }
                # 状态保持为active，直到周期结束
            else:
                # 立即取消
                update_data = {
                    "status": "canceled",
                    "cancel_at_period_end": False,
                    "canceled_at": now,
                    "meta_data": meta_data
                }

                # 处理立即取消的退款
                await self._handle_cancellation_refund(subscription, params.reason or "用户主动取消")
            
            subscription = await self.subscription_repo.update_by_id(params.subscription_id, update_data)
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 清除活跃订阅缓存
            await self._clear_active_subscription_cache(subscription.user_id)
            
            # 同步账单状态
            await self._sync_invoice_status(subscription.id)
            
            # 触发事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "cancel_at_period_end": params.at_period_end,
                "reason": params.reason
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_CANCELED, payload=event_data)
            
            self.logger.info(f"订阅已取消: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"取消订阅失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"取消订阅失败: {str(e)}"
            )
    
    async def reactivate_subscription(self, params: ReactivateSubscriptionParams) -> Result[SubscriptionResponse]:
        """重新激活订阅
        
        Args:
            params: 重新激活订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始重新激活订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "canceled":
                self.logger.warning(f"订阅状态不允许重新激活: status={subscription.status}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="只能重新激活已取消状态的订阅"
                )
            
            # 检查是否有其他活跃订阅
            active_subscriptions, _ = await self.subscription_repo.get_paginated(
                filters={"user_id": params.user_id, "status": "active"},
                page_num=1,
                page_size=1
            )
            if active_subscriptions:
                self.logger.warning(f"用户已有活跃订阅: user_id={params.user_id}, 已有订阅ID={active_subscriptions[0].id}")
                return self.create_error_result(
                    error_code=ErrorCode.SUBSCRIPTION_EXISTS,
                    error_message="用户已有活跃订阅"
                )
            
            # 记录重新激活前的状态
            old_status = subscription.status
            
            # 执行重新激活操作
            self.logger.info(f"执行订阅重新激活操作: subscription_id={subscription.id}")
            
            # 使用仓库方法更新状态为活跃
            now = get_utc_now_without_tzinfo()
            
            # 获取原计划信息以计算正确的周期
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="订阅计划不存在"
                )
            
            # 根据原计划计算新的结束时间
            if plan.interval == "month":
                period_end = now + timedelta(days=30 * plan.interval_count)
            elif plan.interval == "year":
                period_end = now + timedelta(days=365 * plan.interval_count)
            else:
                period_end = now + timedelta(days=30)  # 默认30天
            
            update_data = {
                "status": "active",
                "current_period_start": now,
                "current_period_end": period_end
            }
            
            subscription = await self.subscription_repo.update(
                subscription,
                update_data
            )
            
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="重新激活订阅失败，无法更新状态"
                )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 清除活跃订阅缓存
            await self._clear_active_subscription_cache(subscription.user_id)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "old_status": old_status,
                "new_status": subscription.status,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加计划信息
            if plan:
                event_data.update({
                    "plan_name": plan.name,
                    "plan_price": plan.price,
                    "plan_currency": plan.currency
                })
            
            # 触发订阅重新激活事件
            dispatch(event_names.BILLING_SUBSCRIPTION_REACTIVATED, payload=event_data)
            
            self.logger.info(f"订阅已重新激活: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"重新激活订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="REACTIVATE_FAILED",
                error_message=f"重新激活订阅失败: {str(e)}"
            )
    
    async def renew_subscription(self, params: RenewSubscriptionParams) -> Result[SubscriptionResponse]:
        """续订订阅，开始新的计费周期
        
        Args:
            params: 续订订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始续订订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status not in ["active", "past_due"]:
                self.logger.warning(f"订阅状态不允许续订: status={subscription.status}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"无法续订状态为 {subscription.status} 的订阅"
                )
            
            # 记录续订前的状态和周期
            old_status = subscription.status
            old_period_end = subscription.current_period_end
            
            # 执行续订操作
            self.logger.info(f"执行订阅续订操作: subscription_id={subscription.id}")
            
            # 获取当前计划信息
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="订阅计划不存在"
                )
            
            # 计算新的计费周期
            now = get_utc_now_without_tzinfo()
            new_period_start = subscription.current_period_end
            
            # 根据计划的interval和interval_count计算计费周期天数
            if plan.interval == "month":
                # 按月计算，假设每月30天
                cycle_days = plan.interval_count * 30
            elif plan.interval == "year":
                # 按年计算，假设每年365天
                cycle_days = plan.interval_count * 365
            elif plan.interval == "week":
                # 按周计算
                cycle_days = plan.interval_count * 7
            elif plan.interval == "day":
                # 按天计算
                cycle_days = plan.interval_count
            else:
                # 默认按月计算
                cycle_days = plan.interval_count * 30
            
            new_period_end = new_period_start + timedelta(days=cycle_days)
            
            # 更新订阅信息
            update_data = {
                "current_period_start": new_period_start,
                "current_period_end": new_period_end,
                "status": "active",
                "updated_at": now
            }
            
            subscription = await self.subscription_repo.update_by_id(subscription.id, update_data)
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.UPDATE_FAILED,
                    error_message="订阅续订失败，无法更新状态"
                )
            
            # 获取关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "old_status": old_status,
                "new_status": subscription.status,
                "old_period_end": old_period_end.isoformat() if old_period_end else None,
                "new_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "new_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加计划信息
            if plan:
                event_data.update({
                    "plan_name": plan.name,
                    "plan_price": plan.price,
                    "plan_currency": plan.currency
                })
            
            # 生成续费账单
            await self._generate_renewal_invoice(subscription, plan)
            
            # 触发订阅续订事件
            dispatch(event_names.BILLING_SUBSCRIPTION_RENEWED, payload=event_data)
            
            self.logger.info(f"订阅已续订: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"续订订阅失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.SUBSCRIPTION_RENEWAL_FAILED,
                error_message=f"续订订阅失败: {str(e)}"
            )
    
    async def change_plan(self, params: ChangePlanParams) -> Result[SubscriptionResponse]:
        """更改订阅计划
        
        Args:
            params: 更改订阅计划参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始更改订阅计划: subscription_id={params.subscription_id}, user_id={params.user_id}, plan_id={params.plan_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "active":
                self.logger.warning(f"订阅状态不允许更改计划: status={subscription.status}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="只能为活跃状态的订阅更改计划"
                )
            
            # 检查新计划是否存在
            new_plan = await self.subscription_plan_repo.get_by_id(params.plan_id)
            if not new_plan:
                self.logger.warning(f"订阅计划不存在: plan_id={params.plan_id}")
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="订阅计划不存在"
                )
            
            # 如果新计划与当前计划相同，则不执行更改
            if subscription.plan_id == params.plan_id:
                self.logger.info(f"订阅计划未发生变化: plan_id={params.plan_id}")
                
                # 构建响应
                response = SubscriptionResponse.model_validate(subscription)
                response.plan = SubscriptionPlanResponse.model_validate(new_plan)
                
                return self.create_success_result(response)
            
            # 获取当前计划
            old_plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 记录更改前的状态和计划
            old_plan_id = subscription.plan_id
            old_period_end = subscription.current_period_end
            
            # 执行计划更改操作
            self.logger.info(f"执行订阅计划更改操作: subscription_id={subscription.id}, old_plan_id={old_plan_id}, new_plan_id={params.plan_id}")
            await subscription.change_plan(self.db, params.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            response.plan = SubscriptionPlanResponse.model_validate(new_plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 构建事件数据
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_plan_id": old_plan_id,
                "new_plan_id": params.plan_id,
                "old_period_end": old_period_end.isoformat() if old_period_end else None,
                "new_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "new_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
            }
            
            # 添加新旧计划信息
            if old_plan:
                event_data.update({
                    "old_plan_name": old_plan.name,
                    "old_plan_price": old_plan.price,
                    "old_plan_currency": old_plan.currency
                })
            
            event_data.update({
                "new_plan_name": new_plan.name,
                "new_plan_price": new_plan.price,
                "new_plan_currency": new_plan.currency
            })
            
            # 触发订阅计划更改事件
            dispatch(event_names.BILLING_SUBSCRIPTION_PLAN_CHANGED, payload=event_data)
            
            self.logger.info(f"订阅计划已更改: subscription_id={subscription.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更改订阅计划失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CHANGE_PLAN_FAILED",
                error_message=f"更改订阅计划失败: {str(e)}"
            )
    
    async def pause_subscription(self, params: PauseSubscriptionParams) -> Result[SubscriptionResponse]:
        """暂停订阅
        
        Args:
            params: 暂停订阅的参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始暂停订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 检查订阅状态
            if subscription.status != "active":
                self.logger.warning(f"订阅状态不允许暂停: status={subscription.status}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=f"无法暂停状态为 {subscription.status} 的订阅"
                )
            
            self.logger.info(f"执行订阅暂停操作: subscription_id={subscription.id}")
            
            # 使用仓库方法执行暂停
            meta_data = {"paused_at": get_utc_now_without_tzinfo().isoformat()}
            update_data = {"status": "paused", "meta_data": meta_data}
            
            subscription = await self.subscription_repo.update_by_id(subscription.id, update_data)
            
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="订阅暂停失败，无法更新状态"
                )
            
            # 触发订阅暂停事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status,
                "paused_at": meta_data["paused_at"]
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_PAUSED, payload=event_data)
            
            self.logger.info(f"订阅已暂停: subscription_id={subscription.id}")
            
            # 加载关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 清除活跃订阅缓存
            await self._clear_active_subscription_cache(subscription.user_id)
            
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"订阅暂停失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"暂停订阅失败: {str(e)}"
            )
    
    async def resume_subscription(self, params: ResumeSubscriptionParams) -> Result[SubscriptionResponse]:
        """恢复暂停的订阅
        
        Args:
            params: 恢复订阅参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始恢复订阅: subscription_id={params.subscription_id}, user_id={params.user_id}")
        
        # 获取订阅
        subscription = await self.get_resource_by_id(params.subscription_id)
        if not subscription:
            self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
            return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
        
        # 验证用户权限
        if subscription.user_id != params.user_id:
            self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
            return self.permission_denied_result(params.subscription_id)
        
        # 检查订阅状态
        if subscription.status != "paused":
            self.logger.warning(f"订阅状态不允许恢复: status={subscription.status}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"无法恢复状态为 {subscription.status} 的订阅"
            )
        
        try:
            self.logger.info(f"执行订阅恢复操作: subscription_id={subscription.id}")
            
            # 使用仓库方法执行恢复
            subscription = await self.subscription_repo.update_by_id(subscription.id, {"status": "active"})
            if not subscription:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="订阅恢复失败，无法更新状态"
                )
            
            # 触发订阅恢复事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": subscription.status
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_RESUMED, payload=event_data)
            
            self.logger.info(f"订阅已恢复: subscription_id={subscription.id}")
            
            # 加载关联的计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            
            # 构建响应
            response = SubscriptionResponse.model_validate(subscription)
            if plan:
                response.plan = SubscriptionPlanResponse.model_validate(plan)
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
            
            # 清除活跃订阅缓存
            await self._clear_active_subscription_cache(subscription.user_id)
            
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"订阅恢复失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"订阅恢复失败: {str(e)}"
            )
    
    async def _get_cached_subscription(self, subscription_id: int) -> Optional[SubscriptionResponse]:
        """从缓存获取订阅信息
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            订阅响应对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_resource_cache_key(subscription_id)
            return await self.get_cached_resource(
                key,
                lambda data: SubscriptionResponse.model_validate(data)
            )
        except Exception as e:
            # 获取缓存失败不应影响主要业务逻辑
            self.logger.warning(f"从缓存获取订阅失败: subscription_id={subscription_id}, 错误={str(e)}")
        
        return None
    
    async def _clear_active_subscription_cache(self, user_id: int) -> None:
        """清除用户活跃订阅缓存
        
        Args:
            user_id: 用户ID
        """
        if not self.redis:
            return
            
        try:
            user_key = self._get_active_subscription_cache_key(user_id)
            await self.delete_cache(user_key)
            self.logger.debug(f"清除活跃订阅缓存: key={user_key}")
        except Exception as e:
            # 清除缓存失败不应影响主要业务逻辑
            self.logger.warning(f"清除活跃订阅缓存失败: user_id={user_id}, 错误={str(e)}")

    async def get_user_subscriptions(self, user_id: int) -> UserSubscriptionsResult:
        """获取用户的所有订阅
        
        Args:
            user_id: 用户ID
            
        Returns:
            UserSubscriptionsResult: 包含用户所有订阅的结果对象
        """
        self.logger.info(f"获取用户的所有订阅: user_id={user_id}")
        
        try:
            # 检查缓存
            cache_key = self._get_user_subscriptions_cache_key(user_id)
            if self.redis:
                cached_data = await self.get_cached_list(cache_key)
                if cached_data:
                    self.logger.debug(f"从缓存获取用户订阅列表: key={cache_key}")
                    responses = [SubscriptionResponse.model_validate(item) for item in cached_data]
                    return UserSubscriptionsResult(
                        is_success=True,
                        subscriptions=responses
                    )
            
            # 从数据库获取订阅
            subscriptions, total = await self.subscription_repo.get_paginated(
                self.db,
                user_id=user_id,
                skip=0,
                limit=100  # 获取所有订阅，上限设置为100
            )
            
            if not subscriptions:
                self.logger.info(f"用户没有订阅: user_id={user_id}")
                return UserSubscriptionsResult(
                    is_success=True,
                    subscriptions=[]
                )
            
            # 获取计划ID集合
            plan_ids = [s.plan_id for s in subscriptions if s.plan_id]
            
            # 获取所有相关的计划
            plans = {}
            if plan_ids:
                plans_list, _ = await self.subscription_plan_repo.get_paginated(filters={"id__in": plan_ids})
                plans = {p.id: p for p in plans_list}
            
            # 构建响应
            responses = []
            for subscription in subscriptions:
                response = SubscriptionResponse.model_validate(subscription)
                if subscription.plan_id in plans:
                    response.plan = SubscriptionPlanResponse.model_validate(plans[subscription.plan_id])
                responses.append(response)
            
            # 缓存结果
            if self.redis:
                await self.cache_resource(cache_key, [s.model_dump() for s in responses], CACHE_TTL)
                self.logger.debug(f"用户订阅列表缓存成功: key={cache_key}")
            
            return UserSubscriptionsResult(
                is_success=True,
                subscriptions=responses
            )
        except Exception as e:
            self.logger.error(f"获取用户订阅失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return UserSubscriptionsResult(
                is_success=False,
                error_code="FETCH_FAILED",
                error_message=f"获取用户订阅失败: {str(e)}"
            )
    
    # === 批量操作方法 ===
    
    async def batch_update_subscriptions(self, request: SubscriptionBatchUpdateRequest) -> Result[SubscriptionBatchUpdateResponse]:
        """批量更新订阅"""
        self.logger.info(f"批量更新订阅: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.subscription_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_subscriptions(self, request: SubscriptionBatchDeleteRequest) -> Result[SubscriptionBatchDeleteResponse]:
        """批量删除订阅"""
        self.logger.info(f"批量删除订阅: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.subscription_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:{resource_id}"
    

    
    async def _generate_initial_invoice(self, subscription, plan) -> None:
        """为订阅生成初始账单
        
        Args:
            subscription: 订阅对象
            plan: 订阅计划对象
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过账单生成")
            return
            
        try:
            self.logger.info(f"为订阅生成初始账单: subscription_id={subscription.id}, plan_id={plan.id}")
            
            # 准备账单数据
            now = get_utc_now_without_tzinfo()
            invoice_data = {
                "subscription_id": subscription.id,
                "amount": plan.price,
                "currency": plan.currency,
                "status": "pending",
                "description": f"订阅 {plan.name} 的初始账单",
                "invoice_date": now,
                "due_date": subscription.current_period_end,  # 到期时间与订阅周期结束时间一致
                "is_paid": False,
                "meta_data": {
                    "subscription_id": subscription.id,
                    "plan_id": plan.id,
                    "plan_name": plan.name,
                    "billing_type": "initial"
                }
            }
            
            # 创建账单
            invoice = await self.invoice_repo.create(invoice_data)
            
            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": subscription.id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(event_names.BILLING_INVOICE_CREATED, payload=event_data)
            
            self.logger.info(f"初始账单生成成功: invoice_id={invoice.id}, subscription_id={subscription.id}")
            
        except Exception as e:
            self.logger.error(f"生成初始账单失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            # 账单生成失败不影响订阅创建，只记录错误
    
    async def _update_subscription_on_payment(self, subscription_id: int, paid_invoice_id: int = None) -> None:
        """账单支付成功后更新订阅状态
        
        Args:
            subscription_id: 订阅ID
            paid_invoice_id: 已支付的账单ID（可选）
        """
        try:
            self.logger.info(f"账单支付后更新订阅状态: subscription_id={subscription_id}, paid_invoice_id={paid_invoice_id}")
            
            # 获取订阅
            subscription = await self.get_resource_by_id(subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={subscription_id}")
                return
            
            # 根据当前状态决定更新策略
            if subscription.status == "draft":
                # 草稿状态订阅支付后激活
                if paid_invoice_id and self.invoice_repo:
                    # 如果有指定账单ID，需要验证并处理升级逻辑
                    await self._handle_draft_subscription_payment(subscription, paid_invoice_id)
                else:
                    # 普通草稿订阅支付后激活
                    update_data = {
                        "status": "active",
                        "current_period_start": get_utc_now_without_tzinfo()
                    }
                    await self._update_subscription_status(subscription, update_data, "草稿订阅支付后激活")

            elif subscription.status == "pending":
                # 待支付订阅支付后激活
                if paid_invoice_id and self.invoice_repo:
                    # 如果有指定账单ID，需要验证并处理升级逻辑
                    await self._handle_pending_subscription_payment(subscription, paid_invoice_id)
                else:
                    # 普通pending订阅支付后激活
                    update_data = {
                        "status": "active",
                        "current_period_start": get_utc_now_without_tzinfo()
                    }
                    await self._update_subscription_status(subscription, update_data, "待支付订阅支付后激活")

            elif subscription.status == "past_due":
                # 逾期订阅支付后恢复为活跃状态
                update_data = {
                    "status": "active",
                    "current_period_start": get_utc_now_without_tzinfo()
                }
                await self._update_subscription_status(subscription, update_data, "逾期订阅支付后恢复为活跃状态")

            elif subscription.status == "trialing":
                # 试用期订阅支付后转为活跃状态
                update_data = {
                    "status": "active",
                    "current_period_start": get_utc_now_without_tzinfo()
                }
                await self._update_subscription_status(subscription, update_data, "试用期订阅支付后转为活跃状态")

            else:
                # 其他状态不需要更新
                self.logger.info(f"订阅状态无需更新: subscription_id={subscription_id}, status={subscription.status}")
                return
            
        except Exception as e:
            self.logger.error(f"更新订阅状态失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)

    async def _handle_draft_subscription_payment(self, subscription, paid_invoice_id: int) -> None:
        """处理草稿订阅的支付

        Args:
            subscription: 订阅对象
            paid_invoice_id: 已支付的账单ID
        """
        try:
            self.logger.info(f"处理草稿订阅支付: subscription_id={subscription.id}, paid_invoice_id={paid_invoice_id}")

            # 获取已支付的账单
            paid_invoice = await self.invoice_repo.get_by_id(paid_invoice_id)
            if not paid_invoice:
                self.logger.warning(f"已支付的账单不存在: invoice_id={paid_invoice_id}")
                return

            # 检查账单类型并相应处理
            billing_type = paid_invoice.meta_data.get("billing_type") if paid_invoice.meta_data else "initial"

            if billing_type == "initial":
                # 初始账单支付，直接激活订阅
                update_data = {
                    "status": "active",
                    "current_period_start": get_utc_now_without_tzinfo()
                }
                await self._update_subscription_status(subscription, update_data, "草稿订阅初始账单支付后激活")

            else:
                # 其他类型账单，记录日志但不更新状态
                self.logger.info(f"草稿订阅收到非初始账单支付: subscription_id={subscription.id}, billing_type={billing_type}")

        except Exception as e:
            self.logger.error(f"处理草稿订阅支付失败: subscription_id={subscription.id}, paid_invoice_id={paid_invoice_id}, 错误={str(e)}", exc_info=True)

    async def _handle_pending_subscription_payment(self, subscription, paid_invoice_id: int) -> None:
        """处理pending订阅的支付（处理升级订单支付）
        
        Args:
            subscription: 订阅对象
            paid_invoice_id: 已支付的账单ID
        """
        try:
            self.logger.info(f"处理pending订阅支付: subscription_id={subscription.id}, paid_invoice_id={paid_invoice_id}")
            
            # 获取已支付的账单
            paid_invoice = await self.invoice_repo.get_by_id(paid_invoice_id)
            if not paid_invoice:
                self.logger.warning(f"已支付的账单不存在: invoice_id={paid_invoice_id}")
                return
            
            # 检查是否是升级差价账单
            if (paid_invoice.meta_data and 
                paid_invoice.meta_data.get("billing_type") == "upgrade_difference"):
                
                # 获取升级的目标计划ID
                new_plan_id = paid_invoice.meta_data.get("new_plan_id")
                if not new_plan_id:
                    self.logger.warning(f"升级账单缺少目标计划ID: invoice_id={paid_invoice_id}")
                    return
                
                # 验证目标计划是否存在
                new_plan = await self.subscription_plan_repo.get_by_id(new_plan_id)
                if not new_plan:
                    self.logger.warning(f"目标计划不存在: plan_id={new_plan_id}")
                    return
                
                # 取消其他pending的升级订单
                await self._cancel_other_pending_upgrade_orders(subscription.id, paid_invoice_id)
                
                # 激活订阅到目标计划
                now = get_utc_now_without_tzinfo()
                update_data = {
                    "status": "active",
                    "plan_id": new_plan_id,
                    "current_period_start": now
                }
                
                # 计算新的结束时间
                if new_plan.interval == "month":
                    update_data["current_period_end"] = now + timedelta(days=30 * new_plan.interval_count)
                elif new_plan.interval == "year":
                    update_data["current_period_end"] = now + timedelta(days=365 * new_plan.interval_count)
                else:
                    update_data["current_period_end"] = now + timedelta(days=30)  # 默认30天
                
                # 清除试用期信息（高级订阅不需要试用）
                update_data["trial_start"] = None
                update_data["trial_end"] = None
                
                await self._update_subscription_status(subscription, update_data, f"升级到计划 {new_plan.name} 后激活")
                
                # 触发升级完成事件
                event_data = {
                    "subscription_id": subscription.id,
                    "user_id": subscription.user_id,
                    "new_plan_id": new_plan_id,
                    "new_plan_name": new_plan.name,
                    "new_plan_price": new_plan.price,
                    "paid_invoice_id": paid_invoice_id,
                    "upgrade_completed_at": now.isoformat()
                }
                dispatch(event_names.BILLING_SUBSCRIPTION_UPGRADED, payload=event_data)
                
            else:
                # 普通账单支付，直接激活订阅
                update_data = {
                    "status": "active",
                    "current_period_start": get_utc_now_without_tzinfo()
                }
                await self._update_subscription_status(subscription, update_data, "普通账单支付后激活")
                
        except Exception as e:
            self.logger.error(f"处理pending订阅支付失败: subscription_id={subscription.id}, paid_invoice_id={paid_invoice_id}, 错误={str(e)}", exc_info=True)
    
    async def _cancel_other_pending_upgrade_orders(self, subscription_id: int, paid_invoice_id: int) -> None:
        """取消其他pending的升级订单
        
        Args:
            subscription_id: 订阅ID
            paid_invoice_id: 已支付的账单ID（不取消这个）
        """
        try:
            self.logger.info(f"取消其他pending升级订单: subscription_id={subscription_id}, paid_invoice_id={paid_invoice_id}")
            
            # 获取订阅相关的所有pending升级账单
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            pending_upgrade_invoices = [
                inv for inv in invoices 
                if inv.id != paid_invoice_id  # 不取消已支付的账单
                and inv.status == "pending" 
                and inv.meta_data 
                and inv.meta_data.get("billing_type") == "upgrade_difference"
            ]
            
            # 取消其他pending升级订单
            for invoice in pending_upgrade_invoices:
                await self.invoice_repo.update(invoice, {"status": "canceled"})
                self.logger.info(f"取消pending升级订单: invoice_id={invoice.id}")
                
                # 触发账单取消事件
                event_data = {
                    "invoice_id": invoice.id,
                    "subscription_id": subscription_id,
                    "cancel_reason": "other_upgrade_paid",
                    "canceled_at": get_utc_now_without_tzinfo().isoformat()
                }
                dispatch(event_names.BILLING_INVOICE_CANCELED, payload=event_data)
            
            self.logger.info(f"取消其他pending升级订单完成: subscription_id={subscription_id}, canceled_count={len(pending_upgrade_invoices)}")
            
        except Exception as e:
            self.logger.error(f"取消其他pending升级订单失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
    
    async def _update_subscription_status(self, subscription, update_data: dict, reason: str) -> None:
        """更新订阅状态
        
        Args:
            subscription: 订阅对象
            update_data: 更新数据
            reason: 更新原因
        """
        try:
            self.logger.info(f"更新订阅状态: subscription_id={subscription.id}, reason={reason}")
            
            # 更新订阅
            updated_subscription = await self.subscription_repo.update(subscription, update_data)
            
            # 清理缓存
            await self.delete_cache(self._get_resource_cache_key(subscription.id))
            
            # 触发订阅状态更新事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_status": subscription.status,
                "new_status": updated_subscription.status,
                "update_reason": reason,
                "updated_at": updated_subscription.updated_at.isoformat() if updated_subscription.updated_at else None
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_STATUS_UPDATED, payload=event_data)
            
            self.logger.info(f"订阅状态更新成功: subscription_id={subscription.id}, new_status={updated_subscription.status}")
            
        except Exception as e:
            self.logger.error(f"更新订阅状态失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
    
    async def _sync_invoice_status(self, subscription_id: int) -> None:
        """同步订阅相关的账单状态
        
        Args:
            subscription_id: 订阅ID
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过账单状态同步")
            return
            
        try:
            self.logger.info(f"同步订阅相关账单状态: subscription_id={subscription_id}")
            
            # 获取订阅
            subscription = await self.get_resource_by_id(subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={subscription_id}")
                return
            
            # 获取订阅相关的所有账单
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            
            for invoice in invoices:
                # 根据订阅状态更新账单状态
                if subscription.status == "canceled":
                    if invoice.status == "pending":
                        await self.invoice_repo.update(invoice, {"status": "canceled"})
                        self.logger.info(f"订阅取消，账单状态更新为已取消: invoice_id={invoice.id}")
                elif subscription.status == "past_due":
                    if invoice.status == "pending" and invoice.due_date and invoice.due_date < get_utc_now_without_tzinfo():
                        await self.invoice_repo.update(invoice, {"status": "overdue"})
                        self.logger.info(f"订阅逾期，账单状态更新为逾期: invoice_id={invoice.id}")
            
            self.logger.info(f"账单状态同步完成: subscription_id={subscription_id}")
            
        except Exception as e:
            self.logger.error(f"同步账单状态失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
    
    async def _generate_periodic_invoice(self, subscription_id: int) -> None:
        """基于订阅周期生成账单
        
        Args:
            subscription_id: 订阅ID
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过周期账单生成")
            return
            
        try:
            self.logger.info(f"生成周期账单: subscription_id={subscription_id}")
            
            # 获取订阅
            subscription = await self.get_resource_by_id(subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={subscription_id}")
                return
            
            # 检查订阅状态
            if subscription.status not in ["active", "past_due"]:
                self.logger.info(f"订阅状态不适合生成周期账单: status={subscription.status}")
                return
            
            # 获取订阅计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: plan_id={subscription.plan_id}")
                return
            
            # 检查是否已有未支付的周期账单
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            pending_invoices = [inv for inv in invoices if inv.status == "pending"]
            
            if pending_invoices:
                self.logger.info(f"订阅已有未支付账单，跳过周期账单生成: subscription_id={subscription_id}")
                return
            
            # 准备账单数据
            now = get_utc_now_without_tzinfo()
            invoice_data = {
                "subscription_id": subscription_id,
                "amount": plan.price,
                "currency": plan.currency,
                "status": "pending",
                "description": f"订阅 {plan.name} 的周期账单",
                "invoice_date": now,
                "due_date": subscription.current_period_end,  # 到期时间与订阅周期结束时间一致
                "is_paid": False,
                "meta_data": {
                    "subscription_id": subscription_id,
                    "plan_id": plan.id,
                    "plan_name": plan.name,
                    "billing_type": "periodic",
                    "period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                    "period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
                }
            }
            
            # 创建账单
            invoice = await self.invoice_repo.create(invoice_data)
            
            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(event_names.BILLING_INVOICE_CREATED, payload=event_data)
            
            self.logger.info(f"周期账单生成成功: invoice_id={invoice.id}, subscription_id={subscription_id}")
            
        except Exception as e:
            self.logger.error(f"生成周期账单失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
    
    async def _handle_trial_end(self, subscription_id: int) -> None:
        """处理试用期结束，生成正式账单
        
        Args:
            subscription_id: 订阅ID
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过试用期结束处理")
            return
            
        try:
            self.logger.info(f"处理试用期结束: subscription_id={subscription_id}")
            
            # 获取订阅
            subscription = await self.get_resource_by_id(subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={subscription_id}")
                return
            
            # 检查是否为试用期订阅
            if subscription.status != "trialing":
                self.logger.info(f"订阅不是试用期状态: status={subscription.status}")
                return
            
            # 检查试用期是否已结束
            now = get_utc_now_without_tzinfo()
            if subscription.trial_end and subscription.trial_end > now:
                self.logger.info(f"试用期尚未结束: trial_end={subscription.trial_end}")
                return
            
            # 获取订阅计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: plan_id={subscription.plan_id}")
                return
            
            # 准备账单数据
            invoice_data = {
                "subscription_id": subscription_id,
                "amount": plan.price,
                "currency": plan.currency,
                "status": "pending",
                "description": f"订阅 {plan.name} 的正式账单（试用期结束）",
                "invoice_date": now,
                "due_date": subscription.current_period_end,  # 到期时间与订阅周期结束时间一致
                "is_paid": False,
                "meta_data": {
                    "subscription_id": subscription_id,
                    "plan_id": plan.id,
                    "plan_name": plan.name,
                    "billing_type": "trial_end",
                    "trial_start": subscription.trial_start.isoformat() if subscription.trial_start else None,
                    "trial_end": subscription.trial_end.isoformat() if subscription.trial_end else None
                }
            }
            
            # 创建账单
            invoice = await self.invoice_repo.create(invoice_data)
            
            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": subscription_id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(event_names.BILLING_INVOICE_CREATED, payload=event_data)
            
            self.logger.info(f"试用期结束账单生成成功: invoice_id={invoice.id}, subscription_id={subscription_id}")
            
        except Exception as e:
            self.logger.error(f"处理试用期结束失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
    
    async def _generate_renewal_invoice(self, subscription, plan) -> None:
        """生成续费账单
        
        Args:
            subscription: 订阅对象
            plan: 订阅计划对象
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过续费账单生成")
            return
            
        try:
            self.logger.info(f"生成续费账单: subscription_id={subscription.id}, plan_id={plan.id}")
            
            # 准备账单数据
            now = get_utc_now_without_tzinfo()
            invoice_data = {
                "subscription_id": subscription.id,
                "amount": plan.price,
                "currency": plan.currency,
                "status": "pending",
                "description": f"订阅 {plan.name} 的续费账单",
                "invoice_date": now,
                "due_date": subscription.current_period_end,  # 到期时间与新的订阅周期结束时间一致
                "is_paid": False,
                "meta_data": {
                    "subscription_id": subscription.id,
                    "plan_id": plan.id,
                    "plan_name": plan.name,
                    "billing_type": "renewal",
                    "period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                    "period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
                }
            }
            
            # 创建账单
            invoice = await self.invoice_repo.create(invoice_data)
            
            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": subscription.id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(event_names.BILLING_INVOICE_CREATED, payload=event_data)
            
            self.logger.info(f"续费账单生成成功: invoice_id={invoice.id}, subscription_id={subscription.id}")
            
        except Exception as e:
            self.logger.error(f"生成续费账单失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
    
    async def _generate_upgrade_invoice(self, subscription, current_plan, new_plan, effective_date=None) -> Optional[Any]:
        """生成升级差价账单
        
        Args:
            subscription: 订阅对象
            current_plan: 当前订阅计划
            new_plan: 新订阅计划
            effective_date: 升级生效日期
        """
        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过升级差价账单生成")
            return None
            
        try:
            self.logger.info(f"生成升级差价账单: subscription_id={subscription.id}, current_plan_id={current_plan.id}, new_plan_id={new_plan.id}")
            
            # 计算差价
            price_difference = new_plan.price - current_plan.price
            
            # 如果新计划价格更低或相同，不需要生成差价账单
            if price_difference <= 0:
                self.logger.info(f"新计划价格不高于当前计划，跳过差价账单生成: price_difference={price_difference}")
                return None
            
            # 计算按比例收费的金额（基于剩余时间）
            prorated_amount = await self._calculate_prorated_amount(
                subscription, current_plan, new_plan, effective_date
            )
            
            # 准备账单数据
            now = get_utc_now_without_tzinfo()
            invoice_data = {
                "subscription_id": subscription.id,
                "amount": prorated_amount,
                "currency": new_plan.currency,
                "status": "pending",
                "description": f"订阅升级差价账单：{current_plan.name} → {new_plan.name}",
                "invoice_date": now,
                "due_date": effective_date if effective_date else now,  # 升级时立即收费
                "is_paid": False,
                "meta_data": {
                    "subscription_id": subscription.id,
                    "current_plan_id": current_plan.id,
                    "current_plan_name": current_plan.name,
                    "new_plan_id": new_plan.id,
                    "new_plan_name": new_plan.name,
                    "billing_type": "upgrade_difference",
                    "price_difference": price_difference,
                    "prorated_amount": prorated_amount,
                    "effective_date": effective_date.isoformat() if effective_date else None,
                    "upgrade_type": "immediate" if not effective_date else "scheduled"
                }
            }
            
            # 创建账单
            invoice = await self.invoice_repo.create(invoice_data)
            
            # 触发账单创建事件
            event_data = {
                "invoice_id": invoice.id,
                "subscription_id": subscription.id,
                "amount": invoice.amount,
                "currency": invoice.currency,
                "description": invoice.description,
                "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                "status": invoice.status,
                "meta_data": invoice.meta_data,
                "created_at": invoice.created_at.isoformat() if invoice.created_at else None
            }
            dispatch(event_names.BILLING_INVOICE_CREATED, payload=event_data)
            
            self.logger.info(f"升级差价账单生成成功: invoice_id={invoice.id}, subscription_id={subscription.id}, amount={prorated_amount}")
            
            return invoice
            
        except Exception as e:
            self.logger.error(f"生成升级差价账单失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return None
    
    async def _handle_downgrade_refund(self, subscription, current_plan, new_plan, effective_date=None, immediate=False) -> None:
        """处理降级退款

        Args:
            subscription: 订阅对象
            current_plan: 当前订阅计划
            new_plan: 新订阅计划
            effective_date: 降级生效日期
            immediate: 是否为立即降级
        """
        # 只有立即降级才需要处理退款
        if not immediate:
            self.logger.info(f"周期结束降级无需退款: subscription_id={subscription.id}")
            return

        if not self.invoice_repo:
            self.logger.warning("账单仓库未配置，跳过降级退款处理")
            return

        try:
            self.logger.info(f"处理立即降级退款: subscription_id={subscription.id}, current_plan_id={current_plan.id}, new_plan_id={new_plan.id}")

            # 计算差价
            price_difference = current_plan.price - new_plan.price

            # 如果当前计划价格不高于新计划，不需要退款
            if price_difference <= 0:
                self.logger.info(f"当前计划价格不高于新计划，跳过退款处理: price_difference={price_difference}")
                return

            # 计算按比例退款的金额（基于剩余时间）
            prorated_refund = await self._calculate_prorated_refund_amount(
                subscription, current_plan, new_plan, effective_date
            )
            
            # 查找可退款的账单
            refundable_invoices = await self._find_refundable_invoices(subscription.id)
            
            if not refundable_invoices:
                self.logger.info(f"没有找到可退款的账单: subscription_id={subscription.id}")
                return
            
            # 创建退款记录
            refund_data = {
                "subscription_id": subscription.id,
                "amount": prorated_refund,
                "currency": current_plan.currency,
                "status": "pending",
                "description": f"订阅降级退款：{current_plan.name} → {new_plan.name}",
                "refund_date": get_utc_now_without_tzinfo(),
                "meta_data": {
                    "subscription_id": subscription.id,
                    "current_plan_id": current_plan.id,
                    "current_plan_name": current_plan.name,
                    "new_plan_id": new_plan.id,
                    "new_plan_name": new_plan.name,
                    "billing_type": "downgrade_refund",
                    "price_difference": price_difference,
                    "prorated_refund": prorated_refund,
                    "effective_date": effective_date.isoformat() if effective_date else None,
                    "downgrade_type": "immediate",
                    "refundable_invoices": [inv.id for inv in refundable_invoices]
                }
            }
            
            # 这里应该调用退款服务，暂时记录日志
            self.logger.info(f"降级退款处理: subscription_id={subscription.id}, refund_amount={prorated_refund}")
            
            # 触发退款事件
            event_data = {
                "subscription_id": subscription.id,
                "refund_amount": prorated_refund,
                "currency": current_plan.currency,
                "description": refund_data["description"],
                "meta_data": refund_data["meta_data"],
                "created_at": get_utc_now_without_tzinfo().isoformat()
            }
            dispatch(event_names.BILLING_REFUND_REQUESTED, payload=event_data)
            
            self.logger.info(f"降级退款处理成功: subscription_id={subscription.id}, refund_amount={prorated_refund}")
            
        except Exception as e:
            self.logger.error(f"处理降级退款失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)

    async def _handle_cancellation_refund(self, subscription, reason: str) -> None:
        """处理取消订阅的退款

        Args:
            subscription: 订阅对象
            reason: 取消原因
        """
        try:
            self.logger.info(f"处理取消订阅退款: subscription_id={subscription.id}, reason={reason}")

            # 计算剩余时间退款金额
            refund_amount = await self._calculate_cancellation_refund_amount(subscription)

            if refund_amount <= 0:
                self.logger.info(f"无需退款: subscription_id={subscription.id}, refund_amount={refund_amount}")
                return

            # 初始化退款服务（如果还没有初始化）
            if not self.refund_service:
                self.refund_service = RefundService(self.subscription_repo.session)

            # 创建退款记录
            from ..schemas.refund import CreateRefundRequest
            refund_request = CreateRefundRequest(
                subscription_id=subscription.id,
                amount=refund_amount,
                currency="CNY",  # 默认货币，实际应该从订阅计划获取
                refund_type=RefundType.CANCELLATION.value,
                reason=reason,
                description=f"订阅取消退款 - {reason}",
                meta_data={
                    "cancellation_date": get_utc_now_without_tzinfo().isoformat(),
                    "original_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
                }
            )

            # 创建退款
            refund_result = await self.refund_service.create_refund(refund_request)

            if refund_result.is_success:
                self.logger.info(f"取消订阅退款创建成功: subscription_id={subscription.id}, refund_amount={refund_amount}")
            else:
                self.logger.error(f"取消订阅退款创建失败: subscription_id={subscription.id}, error={refund_result.error_message}")

        except Exception as e:
            self.logger.error(f"处理取消订阅退款失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)

    async def _calculate_cancellation_refund_amount(self, subscription) -> float:
        """计算取消订阅的退款金额

        Args:
            subscription: 订阅对象

        Returns:
            float: 退款金额
        """
        try:
            # 如果没有当前周期信息，无法计算退款
            if not subscription.current_period_start or not subscription.current_period_end:
                self.logger.warning(f"订阅周期信息不完整，无法计算退款: subscription_id={subscription.id}")
                return 0.0

            now = get_utc_now_without_tzinfo()

            # 如果当前周期已结束，无需退款
            if now >= subscription.current_period_end:
                self.logger.info(f"当前周期已结束，无需退款: subscription_id={subscription.id}")
                return 0.0

            # 获取订阅计划
            plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在，无法计算退款: plan_id={subscription.plan_id}")
                return 0.0

            # 使用计费计算器计算退款金额
            refund_amount = BillingCalculator.calculate_refund_amount(
                original_amount=plan.price,
                period_start=subscription.current_period_start,
                period_end=subscription.current_period_end,
                refund_date=now
            )

            self.logger.info(f"取消订阅退款计算: subscription_id={subscription.id}, "
                           f"plan_price={plan.price}, refund_amount={refund_amount}")

            return refund_amount

        except Exception as e:
            self.logger.error(f"计算取消订阅退款金额失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return 0.0

    async def _calculate_prorated_refund_amount(self, subscription, current_plan, new_plan, effective_date=None) -> float:
        """计算立即降级的按比例退款金额

        Args:
            subscription: 订阅对象
            current_plan: 当前计划
            new_plan: 新计划
            effective_date: 生效日期

        Returns:
            float: 退款金额
        """
        try:
            now = effective_date or get_utc_now_without_tzinfo()

            # 计算当前周期的总天数
            if subscription.current_period_start and subscription.current_period_end:
                total_days = (subscription.current_period_end - subscription.current_period_start).days
                # 计算剩余天数
                remaining_days = (subscription.current_period_end - now).days

                if remaining_days <= 0:
                    self.logger.info("当前周期已结束，无需退款")
                    return 0.0

                # 计算剩余时间比例
                remaining_ratio = remaining_days / total_days if total_days > 0 else 0

                # 计算价格差异
                price_difference = current_plan.price - new_plan.price

                # 计算按比例退款金额
                refund_amount = price_difference * remaining_ratio

                self.logger.info(f"退款计算: total_days={total_days}, remaining_days={remaining_days}, "
                               f"remaining_ratio={remaining_ratio:.2f}, price_difference={price_difference}, "
                               f"refund_amount={refund_amount:.2f}")

                return max(0.0, refund_amount)
            else:
                self.logger.warning("订阅周期信息不完整，无法计算退款")
                return 0.0

        except Exception as e:
            self.logger.error(f"计算退款金额失败: {str(e)}", exc_info=True)
            return 0.0

    async def _calculate_prorated_amount(self, subscription, current_plan, new_plan, effective_date=None) -> float:
        """计算按比例收费/退款的金额
        
        Args:
            subscription: 订阅对象
            current_plan: 当前订阅计划
            new_plan: 新订阅计划
            effective_date: 变更生效日期
            
        Returns:
            按比例计算的金额
        """
        try:
            now = get_utc_now_without_tzinfo()
            effective_date = effective_date or now
            
            # 获取当前订阅周期信息
            period_start = subscription.current_period_start
            period_end = subscription.current_period_end
            
            if not period_start or not period_end:
                self.logger.warning(f"订阅周期信息不完整，使用全额计算: subscription_id={subscription.id}")
                return abs(new_plan.price - current_plan.price)
            
            # 计算剩余天数
            if effective_date >= period_end:
                # 如果生效日期在周期结束后，按全额计算
                return abs(new_plan.price - current_plan.price)
            
            # 计算已使用天数和剩余天数
            total_days = (period_end - period_start).days
            remaining_days = (period_end - effective_date).days
            
            if total_days <= 0 or remaining_days <= 0:
                self.logger.warning(f"周期天数计算异常，使用全额计算: total_days={total_days}, remaining_days={remaining_days}")
                return abs(new_plan.price - current_plan.price)
            
            # 按比例计算金额
            price_difference = abs(new_plan.price - current_plan.price)
            prorated_amount = (price_difference * remaining_days) / total_days
            
            # 保留两位小数
            prorated_amount = round(prorated_amount, 2)
            
            self.logger.info(f"按比例计算金额: subscription_id={subscription.id}, price_difference={price_difference}, "
                           f"total_days={total_days}, remaining_days={remaining_days}, prorated_amount={prorated_amount}")
            
            return prorated_amount
            
        except Exception as e:
            self.logger.error(f"计算按比例金额失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            # 出错时返回全额
            return abs(new_plan.price - current_plan.price)
    
    async def _find_refundable_invoices(self, subscription_id: int) -> list:
        """查找可退款的账单
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            可退款的账单列表
        """
        try:
            if not self.invoice_repo:
                return []
            
            # 获取订阅的所有账单
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            
            # 筛选已支付且可退款的账单
            refundable_invoices = []
            for invoice in invoices:
                if (invoice.status == "paid" and 
                    invoice.is_paid and 
                    invoice.meta_data.get("billing_type") in ["initial", "periodic", "renewal"]):
                    refundable_invoices.append(invoice)
            
            self.logger.info(f"找到可退款账单: subscription_id={subscription_id}, count={len(refundable_invoices)}")
            return refundable_invoices
            
        except Exception as e:
            self.logger.error(f"查找可退款账单失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
            return []
    
    async def _check_overdue_subscriptions(self) -> None:
        """检查并处理逾期订阅"""
        try:
            self.logger.info("开始检查逾期订阅")
            
            # 获取所有活跃、试用期和待支付的订阅
            active_subscriptions, _ = await self.subscription_repo.get_by_status("active")
            trialing_subscriptions, _ = await self.subscription_repo.get_by_status("trialing")
            pending_subscriptions, _ = await self.subscription_repo.get_by_status("pending")
            
            all_subscriptions = active_subscriptions + trialing_subscriptions + pending_subscriptions
            overdue_count = 0
            
            for subscription in all_subscriptions:
                if await self._is_subscription_overdue(subscription):
                    await self._handle_overdue_subscription(subscription)
                    overdue_count += 1
            
            self.logger.info(f"逾期订阅检查完成，处理了 {overdue_count} 个逾期订阅")
            
        except Exception as e:
            self.logger.error(f"检查逾期订阅失败: 错误={str(e)}", exc_info=True)
    
    async def _check_period_end_cancellations(self) -> None:
        """检查并处理周期结束的取消订阅"""
        try:
            self.logger.info("开始检查周期结束的取消订阅")
            
            # 获取所有设置了cancel_at_period_end=True的活跃订阅
            active_subscriptions, _ = await self.subscription_repo.get_by_status("active")
            period_end_cancellations = []
            
            now = get_utc_now_without_tzinfo()
            
            for subscription in active_subscriptions:
                if (subscription.cancel_at_period_end and 
                    subscription.current_period_end and 
                    subscription.current_period_end <= now):
                    period_end_cancellations.append(subscription)
            
            # 处理周期结束的取消订阅
            for subscription in period_end_cancellations:
                await self._handle_period_end_cancellation(subscription)
            
            if period_end_cancellations:
                self.logger.info(f"处理了 {len(period_end_cancellations)} 个周期结束的取消订阅")
            else:
                self.logger.info("没有发现需要处理的周期结束取消订阅")
                
        except Exception as e:
            self.logger.error(f"检查周期结束取消订阅失败: 错误={str(e)}", exc_info=True)
    
    async def _handle_period_end_cancellation(self, subscription) -> None:
        """处理周期结束的取消订阅
        
        Args:
            subscription: 订阅对象
        """
        try:
            self.logger.info(f"处理周期结束的取消订阅: subscription_id={subscription.id}")
            
            # 更新订阅状态为已取消
            update_data = {
                "status": "canceled",
                "cancel_at_period_end": False  # 标记为已处理
            }
            
            updated_subscription = await self.subscription_repo.update(subscription, update_data)
            
            # 清理缓存
            await self._delete_resource_cache(subscription.id)
            await self._clear_active_subscription_cache(subscription.user_id)
            
            # 同步账单状态
            await self._sync_invoice_status(subscription.id)
            
            # 触发订阅取消事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "plan_id": subscription.plan_id,
                "status": "canceled",
                "cancel_at_period_end": False,
                "reason": subscription.meta_data.get("cancel_reason", "周期结束自动取消")
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_CANCELED, payload=event_data)
            
            self.logger.info(f"周期结束的取消订阅处理完成: subscription_id={subscription.id}")
            
        except Exception as e:
            self.logger.error(f"处理周期结束的取消订阅失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
    
    async def _is_subscription_overdue(self, subscription) -> bool:
        """检查订阅是否逾期
        
        Args:
            subscription: 订阅对象
            
        Returns:
            是否逾期
        """
        try:
            now = get_utc_now_without_tzinfo()
            
            # 检查订阅周期是否已结束，或者订阅状态为pending
            if (subscription.current_period_end and subscription.current_period_end < now) or subscription.status == "pending":
                # 检查是否有未支付的账单
                if not self.invoice_repo:
                    return False
                
                invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
                unpaid_invoices = [inv for inv in invoices if inv.status in ["pending", "overdue"]]
                
                return len(unpaid_invoices) > 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查订阅逾期状态失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False
    
    async def _handle_overdue_subscription(self, subscription) -> None:
        """处理逾期订阅
        
        Args:
            subscription: 逾期订阅对象
        """
        try:
            self.logger.info(f"处理逾期订阅: subscription_id={subscription.id}")
            
            # 更新订阅状态为逾期
            update_data = {"status": "past_due"}
            updated_subscription = await self.subscription_repo.update(subscription, update_data)
            
            if not updated_subscription:
                self.logger.error(f"更新订阅状态失败: subscription_id={subscription.id}")
                return
            
            # 更新相关账单状态
            await self._update_overdue_invoices(subscription.id)
            
            # 清除缓存
            await self._delete_resource_cache(subscription.id)
            await self._clear_active_subscription_cache(subscription.user_id)
            
            # 触发订阅状态更新事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_status": subscription.status,
                "new_status": "past_due",
                "updated_at": get_utc_now_without_tzinfo().isoformat()
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_STATUS_UPDATED, payload=event_data)
            
            self.logger.info(f"逾期订阅处理完成: subscription_id={subscription.id}")
            
        except Exception as e:
            self.logger.error(f"处理逾期订阅失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
    
    async def _update_overdue_invoices(self, subscription_id: int) -> None:
        """更新逾期订阅的相关账单状态
        
        Args:
            subscription_id: 订阅ID
        """
        if not self.invoice_repo:
            return
            
        try:
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            
            for invoice in invoices:
                if invoice.status == "pending" and invoice.due_date:
                    now = get_utc_now_without_tzinfo()
                    if invoice.due_date < now:
                        # 更新账单状态为逾期
                        await self.invoice_repo.update(invoice, {"status": "overdue"})
                        self.logger.info(f"账单状态更新为逾期: invoice_id={invoice.id}")
            
        except Exception as e:
            self.logger.error(f"更新逾期账单状态失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
    
    async def _validate_subscription_rules(self, subscription, operation: str, **kwargs) -> tuple[bool, str]:
        """验证订阅业务规则
        
        Args:
            subscription: 订阅对象
            operation: 操作类型（create, update, cancel, change_plan等）
            **kwargs: 其他参数
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            self.logger.info(f"验证订阅业务规则: subscription_id={subscription.id}, operation={operation}")
            
            # 基础规则验证
            basic_validation, error_msg = await self._validate_basic_rules(subscription, operation)
            if not basic_validation:
                return False, error_msg
            
            # 操作特定规则验证
            if operation == "change_plan":
                return await self._validate_plan_change_rules(subscription, **kwargs)
            elif operation == "cancel":
                return await self._validate_cancel_rules(subscription, **kwargs)
            elif operation == "renew":
                return await self._validate_renew_rules(subscription, **kwargs)
            elif operation == "pause":
                return await self._validate_pause_rules(subscription, **kwargs)
            elif operation == "resume":
                return await self._validate_resume_rules(subscription, **kwargs)
            
            return True, "验证通过"
            
        except Exception as e:
            self.logger.error(f"验证订阅业务规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"验证过程发生错误: {str(e)}"
    
    async def _validate_basic_rules(self, subscription, operation: str) -> tuple[bool, str]:
        """验证基础业务规则
        
        Args:
            subscription: 订阅对象
            operation: 操作类型
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 检查订阅是否存在
            if not subscription:
                return False, "订阅不存在"
            
            # 检查订阅状态是否允许操作
            allowed_operations = {
                "active": ["change_plan", "cancel", "renew", "pause"],
                "past_due": ["change_plan", "cancel", "renew"],
                "trialing": ["change_plan", "cancel", "renew"],
                "canceled": ["reactivate"],
                "paused": ["resume", "cancel"]
            }
            
            current_status = subscription.status
            if current_status not in allowed_operations:
                return False, f"订阅状态 {current_status} 不允许任何操作"
            
            if operation not in allowed_operations.get(current_status, []):
                return False, f"订阅状态 {current_status} 不允许 {operation} 操作"
            
            # 检查订阅是否已过期（对于某些操作）
            if operation in ["renew", "change_plan"]:
                if subscription.current_period_end and subscription.current_period_end < get_utc_now_without_tzinfo():
                    return False, "订阅已过期，请先续费"
            
            return True, "基础规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证基础业务规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"基础规则验证失败: {str(e)}"
    
    async def _validate_plan_change_rules(self, subscription, **kwargs) -> tuple[bool, str]:
        """验证计划变更规则
        
        Args:
            subscription: 订阅对象
            **kwargs: 包含 new_plan_id, effective_date 等
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            new_plan_id = kwargs.get('new_plan_id')
            if not new_plan_id:
                return False, "缺少新计划ID"
            
            # 检查新计划是否存在
            new_plan = await self.subscription_plan_repo.get_by_id(new_plan_id)
            if not new_plan:
                return False, "新计划不存在"
            
            # 检查是否与当前计划相同
            if subscription.plan_id == new_plan_id:
                return False, "新计划与当前计划相同"
            
            # 检查计划变更频率限制
            if await self._is_plan_change_too_frequent(subscription.id):
                return False, "计划变更过于频繁，请稍后再试"
            
            # 检查是否有其他pending的升级订单
            if subscription.status == "pending":
                pending_check_result = await self._check_pending_upgrade_orders(subscription.id, new_plan_id)
                if not pending_check_result[0]:
                    return pending_check_result
            
            # 检查降级限制（如果适用）
            if new_plan.price < subscription.plan.price:
                can_downgrade, downgrade_error = await self._can_downgrade_plan(subscription)
                if not can_downgrade:
                    return False, downgrade_error
            
            return True, "计划变更规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证计划变更规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"计划变更规则验证失败: {str(e)}"
    
    async def _check_pending_upgrade_orders(self, subscription_id: int, new_plan_id: int) -> tuple[bool, str]:
        """检查是否有其他pending的升级订单
        
        Args:
            subscription_id: 订阅ID
            new_plan_id: 新计划ID
            
        Returns:
            (是否允许, 错误信息)
        """
        try:
            if not self.invoice_repo:
                return True, "无法检查pending订单"
            
            # 获取订阅相关的所有pending账单
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription_id)
            pending_upgrade_invoices = [
                inv for inv in invoices 
                if inv.status == "pending" 
                and inv.meta_data 
                and inv.meta_data.get("billing_type") == "upgrade_difference"
            ]
            
            if not pending_upgrade_invoices:
                return True, "没有其他pending升级订单"
            
            # 检查是否有相同目标计划的pending订单
            same_plan_pending = [
                inv for inv in pending_upgrade_invoices
                if inv.meta_data.get("new_plan_id") == new_plan_id
            ]
            
            if same_plan_pending:
                return False, f"已有相同目标计划({new_plan_id})的升级订单待支付，请先支付或取消现有订单"
            
            # 检查是否有不同目标计划的pending订单
            different_plan_pending = [
                inv for inv in pending_upgrade_invoices
                if inv.meta_data.get("new_plan_id") != new_plan_id
            ]
            
            if different_plan_pending:
                return False, f"已有其他目标计划的升级订单待支付，请先支付或取消现有订单后再申请新的升级"
            
            return True, "pending订单检查通过"
            
        except Exception as e:
            self.logger.error(f"检查pending升级订单失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
            return False, f"检查pending订单时发生错误: {str(e)}"
    
    async def _validate_cancel_rules(self, subscription, **kwargs) -> tuple[bool, str]:
        """验证取消订阅规则
        
        Args:
            subscription: 订阅对象
            **kwargs: 包含 cancel_at_period_end 等
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 检查是否有未支付的账单
            if not self.invoice_repo:
                return True, "无法检查账单状态"
            
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
            unpaid_invoices = [inv for inv in invoices if inv.status in ["pending", "overdue"]]
            
            if unpaid_invoices:
                return False, "存在未支付的账单，请先支付后再取消订阅"
            
            # 检查取消频率限制
            if await self._is_cancel_too_frequent(subscription.user_id):
                return False, "取消操作过于频繁，请稍后再试"
            
            return True, "取消规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证取消规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"取消规则验证失败: {str(e)}"
    
    async def _validate_renew_rules(self, subscription, **kwargs) -> tuple[bool, str]:
        """验证续费规则
        
        Args:
            subscription: 订阅对象
            **kwargs: 其他参数
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 检查是否有未支付的账单
            if not self.invoice_repo:
                return True, "无法检查账单状态"
            
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
            unpaid_invoices = [inv for inv in invoices if inv.status in ["pending", "overdue"]]
            
            if unpaid_invoices:
                return False, "存在未支付的账单，请先支付后再续费"
            
            # 检查续费频率限制
            if await self._is_renew_too_frequent(subscription.id):
                return False, "续费操作过于频繁，请稍后再试"
            
            return True, "续费规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证续费规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"续费规则验证失败: {str(e)}"
    
    async def _validate_pause_rules(self, subscription, **kwargs) -> tuple[bool, str]:
        """验证暂停规则
        
        Args:
            subscription: 订阅对象
            **kwargs: 其他参数
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 检查是否有未支付的账单
            if not self.invoice_repo:
                return True, "无法检查账单状态"
            
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
            unpaid_invoices = [inv for inv in invoices if inv.status in ["pending", "overdue"]]
            
            if unpaid_invoices:
                return False, "存在未支付的账单，无法暂停订阅"
            
            # 检查暂停频率限制
            if await self._is_pause_too_frequent(subscription.id):
                return False, "暂停操作过于频繁，请稍后再试"
            
            return True, "暂停规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证暂停规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"暂停规则验证失败: {str(e)}"
    
    async def _validate_resume_rules(self, subscription, **kwargs) -> tuple[bool, str]:
        """验证恢复规则
        
        Args:
            subscription: 订阅对象
            **kwargs: 其他参数
            
        Returns:
            (是否通过验证, 错误信息)
        """
        try:
            # 检查是否有未支付的账单
            if not self.invoice_repo:
                return True, "无法检查账单状态"
            
            invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
            unpaid_invoices = [inv for inv in invoices if inv.status in ["pending", "overdue"]]
            
            if unpaid_invoices:
                return False, "存在未支付的账单，无法恢复订阅"
            
            return True, "恢复规则验证通过"
            
        except Exception as e:
            self.logger.error(f"验证恢复规则失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"恢复规则验证失败: {str(e)}"
    
    async def _is_plan_change_too_frequent(self, subscription_id: int) -> bool:
        """检查计划变更是否过于频繁
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            是否过于频繁
        """
        try:
            # 这里应该查询审计日志或操作记录
            # 暂时返回 False，表示不频繁
            return False
        except Exception as e:
            self.logger.error(f"检查计划变更频率失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
            return False
    
    async def _can_downgrade_plan(self, subscription) -> tuple[bool, str]:
        """检查是否可以降级计划

        Args:
            subscription: 订阅对象

        Returns:
            tuple[bool, str]: (是否可以降级, 错误信息)
        """
        try:
            # 检查订阅状态
            if subscription.status not in ["active", "trialing"]:
                return False, f"订阅状态为 {subscription.status}，无法降级"

            # 检查是否有进行中的计划变更
            if subscription.meta_data and subscription.meta_data.get("pending_downgrade"):
                return False, "已有待执行的降级操作"

            # 检查是否有未支付的升级账单（降级时应该允许有常规账单）
            if self.invoice_repo:
                try:
                    invoices, _ = await self.invoice_repo.get_by_subscription(subscription.id)
                    upgrade_invoices = [
                        inv for inv in invoices
                        if inv.status in ["pending", "overdue"]
                        and inv.meta_data
                        and inv.meta_data.get("billing_type") == "upgrade_difference"
                    ]
                    if upgrade_invoices:
                        return False, "存在未支付的升级差价账单，请先支付后再降级"
                except Exception as e:
                    self.logger.warning(f"检查升级账单时出错: {str(e)}")

            return True, ""

        except Exception as e:
            self.logger.error(f"检查降级条件失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False, f"检查降级条件时发生错误: {str(e)}"

    async def _schedule_downgrade(self, subscription, new_plan_id: int, effective_date=None) -> bool:
        """调度周期结束降级

        Args:
            subscription: 订阅对象
            new_plan_id: 新计划ID
            effective_date: 生效日期，默认为当前周期结束时间

        Returns:
            bool: 是否调度成功
        """
        try:
            if not effective_date:
                effective_date = subscription.current_period_end

            # 在订阅元数据中记录待执行的降级信息
            meta_data = subscription.meta_data or {}
            meta_data["pending_downgrade"] = {
                "new_plan_id": new_plan_id,
                "effective_date": effective_date.isoformat() if effective_date else None,
                "scheduled_at": get_utc_now_without_tzinfo().isoformat(),
                "type": "end_of_period"
            }

            # 更新订阅元数据
            subscription = await self.subscription_repo.update_by_id(
                subscription.id,
                {"meta_data": meta_data}
            )

            if subscription:
                self.logger.info(f"已调度周期结束降级: subscription_id={subscription.id}, new_plan_id={new_plan_id}, effective_date={effective_date}")
                return True
            else:
                self.logger.error(f"调度降级失败，无法更新订阅元数据: subscription_id={subscription.id}")
                return False

        except Exception as e:
            self.logger.error(f"调度降级失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False

    async def _is_cancel_too_frequent(self, user_id: int) -> bool:
        """检查取消操作是否过于频繁
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否过于频繁
        """
        try:
            # 这里应该查询用户的取消历史
            # 暂时返回 False，表示不频繁
            return False
        except Exception as e:
            self.logger.error(f"检查取消频率失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return False
    
    async def _is_renew_too_frequent(self, subscription_id: int) -> bool:
        """检查续费操作是否过于频繁
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            是否过于频繁
        """
        try:
            # 这里应该查询续费历史
            # 暂时返回 False，表示不频繁
            return False
        except Exception as e:
            self.logger.error(f"检查续费频率失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
            return False
    
    async def _is_pause_too_frequent(self, subscription_id: int) -> bool:
        """检查暂停操作是否过于频繁
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            是否过于频繁
        """
        try:
            # 这里应该查询暂停历史
            # 暂时返回 False，表示不频繁
            return False
        except Exception as e:
            self.logger.error(f"检查暂停频率失败: subscription_id={subscription_id}, 错误={str(e)}", exc_info=True)
            return False
    
    async def run_overdue_check_task(self) -> None:
        """运行逾期检查定时任务"""
        try:
            self.logger.info("开始执行逾期检查定时任务")
            await self._check_overdue_subscriptions()
            await self._check_period_end_cancellations()
            self.logger.info("逾期检查定时任务执行完成")
        except Exception as e:
            self.logger.error(f"逾期检查定时任务执行失败: 错误={str(e)}", exc_info=True)
    
    async def run_business_rule_validation_task(self) -> None:
        """运行业务规则验证定时任务"""
        try:
            self.logger.info("开始执行业务规则验证定时任务")
            
            # 获取所有活跃、逾期和待支付的订阅进行规则验证
            active_subscriptions, _ = await self.subscription_repo.get_by_status("active")
            past_due_subscriptions, _ = await self.subscription_repo.get_by_status("past_due")
            pending_subscriptions, _ = await self.subscription_repo.get_by_status("pending")
            
            all_subscriptions = active_subscriptions + past_due_subscriptions + pending_subscriptions
            validation_issues = []
            
            for subscription in all_subscriptions:
                # 检查基础规则
                validation_passed, error_message = await self._validate_basic_rules(subscription, "check")
                if not validation_passed:
                    validation_issues.append({
                        "subscription_id": subscription.id,
                        "user_id": subscription.user_id,
                        "issue": error_message
                    })
            
            if validation_issues:
                self.logger.warning(f"发现 {len(validation_issues)} 个业务规则验证问题")
                for issue in validation_issues:
                    self.logger.warning(f"订阅 {issue['subscription_id']} 存在规则问题: {issue['issue']}")
            else:
                self.logger.info("所有订阅业务规则验证通过")
            
            self.logger.info("业务规则验证定时任务执行完成")
            
        except Exception as e:
            self.logger.error(f"业务规则验证定时任务执行失败: 错误={str(e)}", exc_info=True)
    
    async def change_plan(self, params: 'ChangePlanParams') -> Result[SubscriptionResponse]:
        """更改订阅计划（自动判断升级或降级）
        
        Args:
            params: 更改订阅计划参数
            
        Returns:
            订阅结果对象
        """
        self.logger.info(f"开始更改订阅计划: subscription_id={params.subscription_id}, user_id={params.user_id}, plan_id={params.plan_id}")
        
        try:
            # 获取订阅
            subscription = await self.get_resource_by_id(params.subscription_id)
            if not subscription:
                self.logger.warning(f"订阅不存在: subscription_id={params.subscription_id}")
                return self.resource_not_found_result(params.subscription_id, ErrorCode.SUBSCRIPTION_NOT_FOUND)
            
            # 验证用户权限
            if subscription.user_id != params.user_id:
                self.logger.warning(f"用户权限不足: 期望user_id={subscription.user_id}, 实际user_id={params.user_id}")
                return self.permission_denied_result(params.subscription_id)
            
            # 验证业务规则
            validation_passed, error_message = await self._validate_subscription_rules(
                subscription, "change_plan", new_plan_id=params.plan_id, effective_date=params.effective_date
            )
            if not validation_passed:
                self.logger.warning(f"业务规则验证失败: subscription_id={params.subscription_id}, 错误={error_message}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message=error_message
                )
            
            # 获取当前计划
            current_plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not current_plan:
                self.logger.warning(f"当前订阅计划不存在: plan_id={subscription.plan_id}")
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="当前订阅计划不存在"
                )
            
            # 获取目标计划
            new_plan = await self.subscription_plan_repo.get_by_id(params.plan_id)
            if not new_plan:
                self.logger.warning(f"目标订阅计划不存在: plan_id={params.plan_id}")
                return self.create_error_result(
                    error_code=ErrorCode.PLAN_NOT_FOUND,
                    error_message="目标订阅计划不存在"
                )
            
            # 自动判断是升级还是降级
            if new_plan.price > current_plan.price:
                self.logger.info(f"检测到升级操作: current_price={current_plan.price}, new_price={new_plan.price}")
                return await self._execute_upgrade(params, subscription, current_plan, new_plan)
            elif new_plan.price < current_plan.price:
                self.logger.info(f"检测到降级操作: current_price={current_plan.price}, new_price={new_plan.price}")
                return await self._execute_downgrade(params, subscription, current_plan, new_plan)
            else:
                self.logger.warning(f"计划价格相同，无需变更: current_price={current_plan.price}, new_price={new_plan.price}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="新计划价格与当前计划相同，无需变更"
                )
                
        except Exception as e:
            self.logger.error(f"更改订阅计划失败: subscription_id={params.subscription_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更改订阅计划失败: {str(e)}"
            )
    
    async def _execute_upgrade(self, params: 'ChangePlanParams', subscription, current_plan, new_plan) -> Result[SubscriptionResponse]:
        """执行升级逻辑"""
        self.logger.info(f"执行升级操作: subscription_id={params.subscription_id}, current_plan={current_plan.name}, new_plan={new_plan.name}")
        
        # 记录更改前的计划ID和结束时间
        old_plan_id = subscription.plan_id
        old_period_end = subscription.current_period_end
        old_status = subscription.status
        
        # 准备更新数据
        now = get_utc_now_without_tzinfo()
        update_data = {
            "plan_id": params.plan_id,
            "status": "pending",  # 升级后先设置为pending，等待差价账单支付
            "current_period_start": now
        }
        
        # 计算新的结束时间
        if new_plan.interval == "month":
            update_data["current_period_end"] = now + timedelta(days=30 * new_plan.interval_count)
        elif new_plan.interval == "year":
            update_data["current_period_end"] = now + timedelta(days=365 * new_plan.interval_count)
        else:
            update_data["current_period_end"] = now + timedelta(days=30)  # 默认30天
        
        # 清除试用期信息（高级订阅不需要试用）
        update_data["trial_start"] = None
        update_data["trial_end"] = None
        
        # 更新订阅计划
        subscription = await self.subscription_repo.update(
            subscription,
            update_data
        )
        
        if not subscription:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message="升级订阅失败，无法更新订阅"
            )
        
        # 构建响应
        response = SubscriptionResponse.model_validate(subscription)
        response.plan = SubscriptionPlanResponse.model_validate(new_plan)
        
        # 更新缓存
        await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)
        
        # 清除活跃订阅缓存
        await self._clear_active_subscription_cache(subscription.user_id)
        
        # 构建事件数据
        event_data = {
            "subscription_id": subscription.id,
            "user_id": subscription.user_id,
            "old_plan_id": old_plan_id,
            "new_plan_id": params.plan_id,
            "old_status": old_status,
            "new_status": subscription.status,
            "old_period_end": old_period_end.isoformat() if old_period_end else None,
            "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
            "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
            "old_plan_name": current_plan.name,
            "old_plan_price": current_plan.price,
            "new_plan_name": new_plan.name,
            "new_plan_price": new_plan.price
        }
        
        # 生成升级差价账单
        invoice = await self._generate_upgrade_invoice(subscription, current_plan, new_plan, now)
        
        # 将账单ID添加到事件数据中
        if invoice:
            event_data["invoice_id"] = invoice.id
        
        # 触发订阅升级待支付事件
        dispatch(event_names.BILLING_SUBSCRIPTION_UPGRADED, payload=event_data)
        
        self.logger.info(f"订阅已升级: subscription_id={subscription.id}")
        return self.create_success_result(response)
    
    async def _execute_downgrade(self, params: 'ChangePlanParams', subscription, current_plan, new_plan) -> Result[SubscriptionResponse]:
        """执行降级逻辑"""
        downgrade_type = params.downgrade_type or "end_of_period"
        self.logger.info(f"执行降级操作: subscription_id={params.subscription_id}, type={downgrade_type}, current_plan={current_plan.name}, new_plan={new_plan.name}")

        # 记录更改前的状态
        old_plan_id = subscription.plan_id
        old_period_end = subscription.current_period_end
        old_status = subscription.status
        now = get_utc_now_without_tzinfo()

        if downgrade_type == "immediate":
            # 立即降级：立即切换计划，计算退款，保持当前计费周期
            return await self._execute_immediate_downgrade(
                params, subscription, current_plan, new_plan,
                old_plan_id, old_period_end, old_status, now
            )
        else:
            # 周期结束降级：调度在周期结束时执行
            return await self._execute_scheduled_downgrade(
                params, subscription, current_plan, new_plan,
                old_plan_id, old_period_end, old_status, now
            )

    async def _execute_immediate_downgrade(self, params, subscription, current_plan, new_plan,
                                         old_plan_id, old_period_end, old_status, now) -> Result[SubscriptionResponse]:
        """执行立即降级"""
        self.logger.info(f"执行立即降级: subscription_id={subscription.id}")

        # 立即降级只更新计划ID，保持当前计费周期
        update_data = {
            "plan_id": params.plan_id,
            "status": old_status,  # 保持原状态
            # 不修改 current_period_start 和 current_period_end，保持当前计费周期
        }

        # 清除任何待执行的降级
        meta_data = subscription.meta_data or {}
        if "pending_downgrade" in meta_data:
            del meta_data["pending_downgrade"]

        # 记录立即降级的元数据
        meta_data["last_downgrade"] = {
            "type": "immediate",
            "old_plan_id": old_plan_id,
            "new_plan_id": params.plan_id,
            "executed_at": now.isoformat(),
            "refund_calculated": True
        }
        update_data["meta_data"] = meta_data

        # 更新订阅
        subscription = await self.subscription_repo.update(subscription, update_data)

        if not subscription:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message="立即降级失败，无法更新订阅"
            )

        # 处理立即降级退款
        await self._handle_downgrade_refund(subscription, current_plan, new_plan, now, immediate=True)

        # 构建响应和事件数据
        return await self._build_downgrade_response(
            subscription, new_plan, current_plan, params.plan_id,
            old_plan_id, old_period_end, old_status, now, "immediate"
        )

    async def _execute_scheduled_downgrade(self, params, subscription, current_plan, new_plan,
                                         old_plan_id, old_period_end, old_status, now) -> Result[SubscriptionResponse]:
        """执行周期结束降级调度"""
        self.logger.info(f"执行周期结束降级调度: subscription_id={subscription.id}")

        # 确定生效日期
        if params.effective_date:
            try:
                from datetime import datetime
                effective_date = datetime.fromisoformat(params.effective_date.replace('Z', '+00:00'))
            except ValueError:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_INPUT,
                    error_message="无效的生效日期格式，请使用ISO格式"
                )
        else:
            # 默认在当前周期结束时降级
            effective_date = subscription.current_period_end

        # 调度降级
        if not await self._schedule_downgrade(subscription, params.plan_id, effective_date):
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message="调度降级失败"
            )

        # 重新获取更新后的订阅
        subscription = await self.get_resource_by_id(subscription.id)
        if not subscription:
            return self.create_error_result(
                error_code=ErrorCode.SUBSCRIPTION_NOT_FOUND,
                error_message="无法获取更新后的订阅信息"
            )

        # 构建响应（注意：此时订阅计划还没有实际更改）
        response = SubscriptionResponse.model_validate(subscription)
        response.plan = SubscriptionPlanResponse.model_validate(current_plan)  # 仍然是当前计划

        # 更新缓存
        await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)

        # 构建事件数据
        event_data = {
            "subscription_id": subscription.id,
            "user_id": subscription.user_id,
            "old_plan_id": old_plan_id,
            "new_plan_id": params.plan_id,
            "old_status": old_status,
            "new_status": subscription.status,
            "effective_date": effective_date.isoformat(),
            "downgrade_type": "scheduled",
            "old_plan_name": current_plan.name,
            "old_plan_price": current_plan.price,
            "new_plan_name": new_plan.name,
            "new_plan_price": new_plan.price
        }

        # 触发降级调度事件
        dispatch(event_names.BILLING_SUBSCRIPTION_DOWNGRADED, payload=event_data)

        self.logger.info(f"降级已调度: subscription_id={subscription.id}, effective_date={effective_date}")
        return self.create_success_result(response)

    async def _build_downgrade_response(self, subscription, new_plan, current_plan, new_plan_id,
                                      old_plan_id, old_period_end, old_status, executed_at, downgrade_type):
        """构建降级响应"""
        # 构建响应
        response = SubscriptionResponse.model_validate(subscription)
        response.plan = SubscriptionPlanResponse.model_validate(new_plan)

        # 更新缓存
        await self.cache_resource(self._get_resource_cache_key(subscription.id), response, CACHE_TTL)

        # 清除活跃订阅缓存
        await self._clear_active_subscription_cache(subscription.user_id)

        # 构建事件数据
        event_data = {
            "subscription_id": subscription.id,
            "user_id": subscription.user_id,
            "old_plan_id": old_plan_id,
            "new_plan_id": new_plan_id,
            "old_status": old_status,
            "new_status": subscription.status,
            "old_period_end": old_period_end.isoformat() if old_period_end else None,
            "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
            "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
            "effective_date": executed_at.isoformat(),
            "downgrade_type": downgrade_type,
            "old_plan_name": current_plan.name,
            "old_plan_price": current_plan.price,
            "new_plan_name": new_plan.name,
            "new_plan_price": new_plan.price
        }

        # 触发订阅降级事件
        dispatch(event_names.BILLING_SUBSCRIPTION_DOWNGRADED, payload=event_data)

        self.logger.info(f"订阅已降级: subscription_id={subscription.id}, type={downgrade_type}")
        return self.create_success_result(response)

    async def execute_scheduled_downgrades(self) -> int:
        """执行所有到期的调度降级

        这个方法应该被定时任务调用，用于处理周期结束的降级

        Returns:
            int: 执行的降级数量
        """
        executed_count = 0
        try:
            now = get_utc_now_without_tzinfo()

            # 查找所有有待执行降级的订阅
            subscriptions, _ = await self.subscription_repo.get_paginated(
                filters={"meta_data__pending_downgrade__isnull": False},
                page_num=1,
                page_size=100
            )

            for subscription in subscriptions:
                try:
                    pending_downgrade = subscription.meta_data.get("pending_downgrade")
                    if not pending_downgrade:
                        continue

                    # 检查是否到了执行时间
                    effective_date_str = pending_downgrade.get("effective_date")
                    if not effective_date_str:
                        continue

                    from datetime import datetime
                    effective_date = datetime.fromisoformat(effective_date_str.replace('Z', '+00:00'))

                    if now >= effective_date:
                        # 执行降级
                        success = await self._execute_pending_downgrade(subscription, pending_downgrade)
                        if success:
                            executed_count += 1
                            self.logger.info(f"成功执行调度降级: subscription_id={subscription.id}")
                        else:
                            self.logger.error(f"执行调度降级失败: subscription_id={subscription.id}")

                except Exception as e:
                    self.logger.error(f"处理订阅降级失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
                    continue

            self.logger.info(f"调度降级执行完成: 共执行 {executed_count} 个降级")
            return executed_count

        except Exception as e:
            self.logger.error(f"执行调度降级失败: 错误={str(e)}", exc_info=True)
            return executed_count

    async def _execute_pending_downgrade(self, subscription, pending_downgrade) -> bool:
        """执行待处理的降级

        Args:
            subscription: 订阅对象
            pending_downgrade: 待执行的降级信息

        Returns:
            bool: 是否执行成功
        """
        try:
            new_plan_id = pending_downgrade.get("new_plan_id")
            if not new_plan_id:
                self.logger.error(f"降级信息缺少新计划ID: subscription_id={subscription.id}")
                return False

            # 获取新计划
            new_plan = await self.subscription_plan_repo.get_by_id(new_plan_id)
            if not new_plan:
                self.logger.error(f"新计划不存在: plan_id={new_plan_id}")
                return False

            # 获取当前计划
            current_plan = await self.subscription_plan_repo.get_by_id(subscription.plan_id)
            if not current_plan:
                self.logger.error(f"当前计划不存在: plan_id={subscription.plan_id}")
                return False

            now = get_utc_now_without_tzinfo()

            # 计算新的计费周期
            if new_plan.interval == "month":
                new_period_end = now + timedelta(days=30 * new_plan.interval_count)
            elif new_plan.interval == "year":
                new_period_end = now + timedelta(days=365 * new_plan.interval_count)
            else:
                new_period_end = now + timedelta(days=30)

            # 更新订阅
            update_data = {
                "plan_id": new_plan_id,
                "current_period_start": now,
                "current_period_end": new_period_end,
                "status": "active"
            }

            # 清除待执行的降级信息
            meta_data = subscription.meta_data or {}
            if "pending_downgrade" in meta_data:
                del meta_data["pending_downgrade"]

            # 记录执行信息
            meta_data["last_downgrade"] = {
                "type": "scheduled",
                "old_plan_id": subscription.plan_id,
                "new_plan_id": new_plan_id,
                "executed_at": now.isoformat(),
                "scheduled_at": pending_downgrade.get("scheduled_at")
            }
            update_data["meta_data"] = meta_data

            # 执行更新
            updated_subscription = await self.subscription_repo.update(subscription, update_data)
            if not updated_subscription:
                self.logger.error(f"更新订阅失败: subscription_id={subscription.id}")
                return False

            # 清理缓存
            await self.cache_resource(self._get_resource_cache_key(subscription.id), None, 0)
            await self._clear_active_subscription_cache(subscription.user_id)

            # 触发降级完成事件
            event_data = {
                "subscription_id": subscription.id,
                "user_id": subscription.user_id,
                "old_plan_id": subscription.plan_id,
                "new_plan_id": new_plan_id,
                "downgrade_type": "scheduled_executed",
                "executed_at": now.isoformat(),
                "old_plan_name": current_plan.name,
                "old_plan_price": current_plan.price,
                "new_plan_name": new_plan.name,
                "new_plan_price": new_plan.price
            }
            dispatch(event_names.BILLING_SUBSCRIPTION_DOWNGRADED, payload=event_data)

            return True

        except Exception as e:
            self.logger.error(f"执行待处理降级失败: subscription_id={subscription.id}, 错误={str(e)}", exc_info=True)
            return False