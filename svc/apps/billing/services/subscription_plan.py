"""
订阅计划服务模块，提供面向对象形式的订阅计划管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

from datetime import datetime
from typing import Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.models.subscription_plan import SubscriptionPlan
from svc.apps.billing.repositories import SubscriptionPlanRepository
from svc.apps.billing.schemas.subscription_plan import (
    CreatePlanParams, DeletePlanParams, GetPlanByNameParams, GetPlanParams,
    GetPlansParams, SubscriptionPlanBatchDeleteRequest,
    SubscriptionPlanBatchDeleteResponse, SubscriptionPlanBatchUpdateRequest,
    SubscriptionPlanBatchUpdateResponse, SubscriptionPlanListResponse,
    SubscriptionPlanResponse, UpdatePlanParams)
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

# 缓存过期时间（秒）
CACHE_TTL = 3600

class SubscriptionPlanService(BaseService, BatchOperationMixin):
    """订阅计划服务类，提供订阅计划管理相关功能

    该服务类负责：
    1. 处理订阅计划的业务逻辑
    2. 验证请求参数和权限
    3. 调用仓库层方法进行数据库操作
    4. 触发相关事件
    5. 封装结果返回
    """

    def __init__(self, plan_repo: SubscriptionPlanRepository, redis: Optional[Redis] = None, **kwargs):
        """初始化订阅计划服务

        Args:
            plan_repo: 订阅计划仓库
            redis: Redis客户端（可选）
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="subscription_plan",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(plan_repo, config, redis, **kwargs)
        self.plan_repo = plan_repo
    
    async def get_resource_by_id(self, plan_id: int) -> Optional[SubscriptionPlan]:
        """获取指定ID的订阅计划资源
        
        Args:
            plan_id: 订阅计划ID
            
        Returns:
            Optional[SubscriptionPlan]: 订阅计划对象，不存在时返回None
        """
        return await self.plan_repo.get_by_id(plan_id)
    
    async def create_plan(self, params: CreatePlanParams) -> Result:
        """创建订阅计划
        
        Args:
            params: 创建订阅计划参数
            
        Returns:
            订阅计划结果对象
        """
        self.logger.info(f"开始创建订阅计划: name={params.plan_data.name}, price={params.plan_data.price}")
        
        try:
            # 从Pydantic模型中提取数据，确保meta_data字段被正确处理
            plan_in = params.plan_data
            plan_data = plan_in.model_dump()
            
            # 检查同名计划是否已存在
            existing_plan = await self.plan_repo.get_one(name=self.plan_data["name"])
            if existing_plan:
                self.logger.warning(f"创建订阅计划失败: 同名计划已存在, name={plan_data['name']}")
                return self.create_error_result(
                    result_code=ErrorCode.PLAN_EXISTS,
                    result_msg="同名订阅计划已存在"
                )
            
            # 创建订阅计划
            plan = await self.plan_repo.create(
                user_id=plan_data.get("user_id", 1),  # 默认使用管理员ID
                name=plan_data["name"],
                description=plan_data.get("description"),
                price=plan_data["price"],
                currency=plan_data.get("currency", "CNY"),
                interval=plan_data.get("interval", "month"),
                interval_count=plan_data.get("interval_count", 1),
                trial_period_days=plan_data.get("trial_period_days"),
                features=plan_data.get("features", {}),
                meta_data=plan_data.get("meta_data", {}),
                tier=plan_data.get("tier"),
                valid_from=plan_data.get("valid_from"),
                valid_until=plan_data.get("valid_until"),
                max_users=plan_data.get("max_users"),
                max_storage=plan_data.get("max_storage"),
                max_projects=plan_data.get("max_projects"),
                sort_order=plan_data.get("sort_order", 0),
            )
            
            # 构建响应
            response = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存订阅计划
            await self.cache_resource(self._get_plan_cache_key(plan.id), response, CACHE_TTL)
            
            # 触发订阅计划创建事件
            event_data = {
                "plan_id": plan.id,
                "name": plan.name,
                "price": plan.price,
                "currency": plan.currency,
                "interval": plan.interval,
                "interval_count": plan.interval_count,
                "trial_period_days": plan.trial_period_days,
                "features": plan.features,
                "created_at": plan.created_at.isoformat() if plan.created_at else None
            }
            dispatch(event_names.SUBSCRIPTION_PLAN_CREATED, **event_data)
            
            self.logger.info(f"订阅计划创建成功: id={plan.id}, name={plan.name}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"创建订阅计划失败: name={plan_data['name']}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"创建订阅计划失败: {str(e)}"
            )
    
    async def get_plan(self, params: GetPlanParams) -> Result:
        """获取订阅计划
        
        Args:
            params: 获取订阅计划参数
            
        Returns:
            订阅计划结果对象
        """
        self.logger.info(f"获取订阅计划: id={params.plan_id}")
        
        try:
            # 先尝试从缓存获取
            cached_plan = await self._get_cached_plan(params.plan_id)
            if cached_plan:
                self.logger.debug(f"从缓存获取到订阅计划: id={params.plan_id}")
                return self.create_success_result(cached_plan)
            
            # 从数据库获取
            plan = await self.get_resource_by_id(params.plan_id)
            if not plan:
                self.logger.warning(f"订阅计划不存在: id={params.plan_id}")
                return self.resource_not_found_result(params.plan_id)
                
            # 构建响应并缓存
            response = SubscriptionPlanResponse.model_validate(plan)
            await self.cache_resource(self._get_plan_cache_key(plan.id), response, CACHE_TTL)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取订阅计划失败: id={params.plan_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED, 
                error_message=f"获取订阅计划失败: {str(e)}"
            )
    
    async def get_plan_by_name(self, params: GetPlanByNameParams) -> Result:
        """通过名称获取订阅计划
        
        Args:
            params: 通过名称获取订阅计划参数
            
        Returns:
            订阅计划结果对象
        """
        self.logger.info(f"通过名称获取订阅计划: name={params.name}")
        
        try:
            # 尝试从缓存获取
            cache_key = self._get_plan_name_cache_key(params.name)
            cached_data = await self.get_cached_resource(
                cache_key,
                lambda data: SubscriptionPlanResponse.model_validate(data)
            )
            if cached_data:
                self.logger.debug(f"从缓存获取到订阅计划: name={params.name}")
                return self.create_success_result(cached_data)
            
            # 从数据库获取
            plan = await self.plan_repo.get_one(name=self.params.name)
            if not plan:
                self.logger.warning(f"订阅计划不存在: name={params.name}")
                return self.create_error_result(
                    result_code=ErrorCode.PLAN_NOT_FOUND,
                    result_msg="订阅计划不存在"
                )
            
            # 构建响应
            response = SubscriptionPlanResponse.model_validate(plan)
            
            # 缓存结果
            await self.cache_resource(self._get_plan_cache_key(plan.id), response, CACHE_TTL)
                
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"通过名称获取订阅计划失败: name={params.name}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.PLAN_NOT_FOUND,
                result_msg=f"获取订阅计划失败: {str(e)}"
            )
    
    def _get_plans_list_cache_key(self, active_only: bool, page_num: int, page_size: int) -> str:
        """获取订阅计划列表缓存键 (使用 page_num 和 page_size)
        
        Args:
            active_only: 是否只获取活跃的订阅计划
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:list:active_{active_only}:page_{page_num}:size_{page_size}"
    
    async def get_plans(self, params: GetPlansParams) -> Result:
        """获取订阅计划列表
        
        Args:
            params: 获取订阅计划列表参数
            
        Returns:
            Result: 包含分页信息的订阅计划列表结果
        """
        self.logger.info(f"获取订阅计划列表: active_only={params.active_only}, page_num={params.page_num}, page_size={params.page_size}")
        
        try:
            # Prepare filters based on params
            filters = {}
            if params.active_only:
                filters['is_active'] = True

            # 从数据库获取 using get_paginated from BaseRepository
            plans, total = await self.plan_repo.get_paginated(
                page=params.page_num,
                page_size=params.page_size,
                order_by="sort_order", # Define a default order
                order_direction="asc",
                **filters
            )
            
            # 构建响应
            response_list = [SubscriptionPlanResponse.model_validate(plan.to_dict()) for plan in plans]
            
            # 计算总页数
            total_pages = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = SubscriptionPlanListResponse(
                items=response_list,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
            
        except Exception as e:
            self.logger.error(f"获取订阅计划列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取订阅计划列表失败: {str(e)}"
            )
    
    async def update_plan(self, params: UpdatePlanParams) -> Result:
        """更新订阅计划
        
        Args:
            params: 更新订阅计划参数
            
        Returns:
            订阅计划结果对象
        """
        self.logger.info(f"开始更新订阅计划: id={params.plan_id}")
        
        try:
            # 提取更新数据
            plan_in = params.plan_data
            plan_data = plan_in.model_dump(exclude_unset=True)
            
            # 更新订阅计划
            updated_plan = await self.plan_repo.update(
                
                plan_id=params.plan_id,
                **plan_data
            )
            
            if not updated_plan:
                self.logger.warning(f"更新订阅计划失败: 计划不存在, id={params.plan_id}")
                return self.resource_not_found_result(params.plan_id)
            
            # 构建响应
            response = SubscriptionPlanResponse.model_validate(updated_plan)
            
            # 更新缓存
            await self.cache_resource(self._get_plan_cache_key(updated_plan.id), response, CACHE_TTL)
            
            # 触发订阅计划更新事件
            event_data = {
                "plan_id": updated_plan.id,
                "name": updated_plan.name,
                "price": updated_plan.price,
                "is_active": updated_plan.is_active,
                "updated_at": updated_plan.updated_at.isoformat() if updated_plan.updated_at else None
            }
            dispatch(event_names.SUBSCRIPTION_PLAN_UPDATED, **event_data)
            
            self.logger.info(f"订阅计划更新成功: id={updated_plan.id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更新订阅计划失败: id={params.plan_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"更新订阅计划失败: {str(e)}"
            )
    
    async def delete_plan(self, params: DeletePlanParams) -> Result:
        """删除订阅计划
        
        Args:
            params: 删除订阅计划参数
            
        Returns:
            删除结果对象
        """
        self.logger.info(f"开始删除订阅计划: id={params.plan_id}")
        
        try:
            # 获取订阅计划
            plan = await self.get_resource_by_id(params.plan_id)
            if not plan:
                self.logger.warning(f"删除订阅计划失败: 计划不存在, id={params.plan_id}")
                return self.resource_not_found_result(params.plan_id)
            
            # 删除
            await self.plan_repo.delete(self.plan)
            
            # 清除缓存
            await self.delete_resource_cache(self._get_plan_cache_key(params.plan_id))
            await self.delete_resource_cache(self._get_plan_name_cache_key(plan.name))
            
            # 触发删除事件
            event_data = {
                "plan_id": plan.id,
                "name": plan.name,
                "deleted_at": datetime.utcnow().isoformat()
            }
            dispatch(event_names.SUBSCRIPTION_PLAN_DELETED, **event_data)
            
            self.logger.info(f"订阅计划删除成功: id={params.plan_id}")
            return self.create_success_result(True)
            
        except Exception as e:
            self.logger.error(f"删除订阅计划失败: id={params.plan_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"删除订阅计划失败: {str(e)}"
            )
            
    def _get_plan_cache_key(self, plan_id: int) -> str:
        """获取订阅计划缓存键
        
        Args:
            plan_id: 订阅计划ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{plan_id}"
    
    def _get_plan_name_cache_key(self, plan_name: str) -> str:
        """获取订阅计划名称缓存键
        
        Args:
            plan_name: 订阅计划名称
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:name:{plan_name}"
            

    
    async def _get_cached_plan(self, plan_id: int) -> Optional[SubscriptionPlanResponse]:
        """获取缓存的订阅计划
        
        Args:
            plan_id: 订阅计划ID
            
        Returns:
            Optional[SubscriptionPlanResponse]: 订阅计划响应对象，不存在时返回None
        """
        if not self.redis:
            return None
            
        return await self.get_cached_resource(
            self._get_plan_cache_key(plan_id),
            lambda data: SubscriptionPlanResponse.model_validate(data)
        )
    
    # === 批量操作方法 ===
    
    async def batch_update_subscription_plans(self, request: SubscriptionPlanBatchUpdateRequest) -> Result[SubscriptionPlanBatchUpdateResponse]:
        """批量更新订阅计划"""
        self.logger.info(f"批量更新订阅计划: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.plan_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_subscription_plans(self, request: SubscriptionPlanBatchDeleteRequest) -> Result[SubscriptionPlanBatchDeleteResponse]:
        """批量删除订阅计划"""
        self.logger.info(f"批量删除订阅计划: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.plan_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"subscription_plan:{resource_id}" 