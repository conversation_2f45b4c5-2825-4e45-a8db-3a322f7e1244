"""
计费周期计算工具类

这个模块提供准确的计费周期计算功能，处理各种日历边界情况。
"""

import calendar
import logging
from datetime import datetime, timedelta
from typing import NamedTuple, Optional

from ..constants.business_rules import BillingRules
from ..models.subscription import BillingInterval

logger = logging.getLogger(__name__)


class BillingCycle(NamedTuple):
    """计费周期数据结构"""
    start: datetime
    end: datetime
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None


class BillingCalculator:
    """计费周期计算器
    
    提供准确的计费周期计算，考虑实际日历情况
    """
    
    @classmethod
    def calculate_period_end(cls, start_date: datetime, interval: str, interval_count: int = 1) -> datetime:
        """计算计费周期结束时间

        Args:
            start_date: 周期开始时间
            interval: 计费间隔类型 (month, year, week, day)
            interval_count: 间隔数量

        Returns:
            datetime: 周期结束时间
        """
        try:
            if interval == BillingInterval.MONTH:
                return cls._add_months(start_date, interval_count)
            elif interval == BillingInterval.YEAR:
                return cls._add_years(start_date, interval_count)
            elif interval == BillingInterval.WEEK:
                return start_date + timedelta(weeks=interval_count)
            elif interval == BillingInterval.DAY:
                return start_date + timedelta(days=interval_count)
            else:
                logger.warning(f"不支持的计费间隔: {interval}，使用默认月度计费")
                return cls._add_months(start_date, interval_count)
        except Exception as e:
            logger.error(f"计算计费周期结束时间失败: {e}")
            # 降级到简单的天数计算
            days = cls._get_fallback_days(interval, interval_count)
            return start_date + timedelta(days=days)
    
    @classmethod
    def calculate_initial_cycle(cls, plan, start_date: Optional[datetime] = None) -> BillingCycle:
        """计算初始计费周期
        
        Args:
            plan: 订阅计划对象
            start_date: 开始时间，默认为当前时间
            
        Returns:
            BillingCycle: 计费周期信息
        """
        from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
        
        if start_date is None:
            start_date = get_utc_now_without_tzinfo()
        
        # 计算计费周期结束时间
        period_end = cls.calculate_period_end(start_date, plan.interval, plan.interval_count)
        
        # 计算试用期
        trial_start = None
        trial_end = None
        if plan.trial_period_days and plan.trial_period_days > 0:
            trial_start = start_date
            trial_end = start_date + timedelta(days=plan.trial_period_days)
        
        return BillingCycle(
            start=start_date,
            end=period_end,
            trial_start=trial_start,
            trial_end=trial_end
        )
    
    @classmethod
    def calculate_next_cycle(cls, current_end: datetime, interval: str, interval_count: int = 1) -> BillingCycle:
        """计算下一个计费周期
        
        Args:
            current_end: 当前周期结束时间
            interval: 计费间隔类型
            interval_count: 间隔数量
            
        Returns:
            BillingCycle: 下一个计费周期
        """
        next_start = current_end
        next_end = cls.calculate_period_end(next_start, interval, interval_count)
        
        return BillingCycle(
            start=next_start,
            end=next_end
        )
    
    @classmethod
    def calculate_prorated_amount(cls, total_amount: float, period_start: datetime, 
                                period_end: datetime, usage_start: datetime, 
                                usage_end: Optional[datetime] = None) -> float:
        """计算按比例金额
        
        Args:
            total_amount: 总金额
            period_start: 周期开始时间
            period_end: 周期结束时间
            usage_start: 使用开始时间
            usage_end: 使用结束时间，默认为周期结束时间
            
        Returns:
            float: 按比例金额
        """
        if usage_end is None:
            usage_end = period_end
        
        # 确保时间范围有效
        usage_start = max(usage_start, period_start)
        usage_end = min(usage_end, period_end)
        
        if usage_start >= usage_end:
            return 0.0
        
        # 计算总周期时长和使用时长
        total_duration = (period_end - period_start).total_seconds()
        usage_duration = (usage_end - usage_start).total_seconds()
        
        if total_duration <= 0:
            return 0.0
        
        # 计算比例
        ratio = usage_duration / total_duration
        prorated_amount = total_amount * ratio
        
        # 应用精度规则
        precision = BillingRules.PRORATION_PRECISION
        prorated_amount = round(prorated_amount, precision)
        
        # 应用最小金额规则
        min_amount = BillingRules.MIN_PRORATION_AMOUNT
        if prorated_amount < min_amount:
            prorated_amount = 0.0
        
        return prorated_amount
    
    @classmethod
    def calculate_refund_amount(cls, original_amount: float, period_start: datetime,
                              period_end: datetime, refund_date: Optional[datetime] = None) -> float:
        """计算退款金额
        
        Args:
            original_amount: 原始金额
            period_start: 周期开始时间
            period_end: 周期结束时间
            refund_date: 退款日期，默认为当前时间
            
        Returns:
            float: 退款金额
        """
        from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
        
        if refund_date is None:
            refund_date = get_utc_now_without_tzinfo()
        
        # 如果退款日期在周期开始之前，全额退款
        if refund_date <= period_start:
            return original_amount
        
        # 如果退款日期在周期结束之后，不退款
        if refund_date >= period_end:
            return 0.0
        
        # 计算剩余时间的退款
        return cls.calculate_prorated_amount(
            original_amount, period_start, period_end, refund_date, period_end
        )
    
    @classmethod
    def calculate_upgrade_difference(cls, old_plan, new_plan, period_start: datetime,
                                   period_end: datetime, upgrade_date: Optional[datetime] = None) -> float:
        """计算升级差价
        
        Args:
            old_plan: 原计划
            new_plan: 新计划
            period_start: 周期开始时间
            period_end: 周期结束时间
            upgrade_date: 升级日期，默认为当前时间
            
        Returns:
            float: 升级差价
        """
        from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
        
        if upgrade_date is None:
            upgrade_date = get_utc_now_without_tzinfo()
        
        # 计算价格差异
        price_difference = new_plan.price - old_plan.price
        
        if price_difference <= 0:
            return 0.0
        
        # 计算剩余时间的差价
        return cls.calculate_prorated_amount(
            price_difference, period_start, period_end, upgrade_date, period_end
        )
    
    @classmethod
    def is_same_billing_cycle(cls, date1: datetime, date2: datetime, interval: str) -> bool:
        """检查两个日期是否在同一个计费周期内
        
        Args:
            date1: 日期1
            date2: 日期2
            interval: 计费间隔类型
            
        Returns:
            bool: 是否在同一周期
        """
        if interval == BillingInterval.MONTH:
            return date1.year == date2.year and date1.month == date2.month
        elif interval == BillingInterval.YEAR:
            return date1.year == date2.year
        elif interval == BillingInterval.WEEK:
            # 计算周数
            week1 = date1.isocalendar()[1]
            week2 = date2.isocalendar()[1]
            return date1.year == date2.year and week1 == week2
        elif interval == BillingInterval.DAY:
            return date1.date() == date2.date()
        else:
            return False
    
    @classmethod
    def get_cycle_description(cls, start: datetime, end: datetime, interval: str) -> str:
        """获取计费周期描述
        
        Args:
            start: 开始时间
            end: 结束时间
            interval: 计费间隔类型
            
        Returns:
            str: 周期描述
        """
        start_str = start.strftime("%Y-%m-%d")
        end_str = end.strftime("%Y-%m-%d")
        
        if interval == BillingInterval.MONTH:
            return f"月度周期 ({start_str} 至 {end_str})"
        elif interval == BillingInterval.YEAR:
            return f"年度周期 ({start_str} 至 {end_str})"
        elif interval == BillingInterval.WEEK:
            return f"周度周期 ({start_str} 至 {end_str})"
        elif interval == BillingInterval.DAY:
            return f"日度周期 ({start_str} 至 {end_str})"
        else:
            return f"计费周期 ({start_str} 至 {end_str})"
    
    @classmethod
    def _get_fallback_days(cls, interval: str, interval_count: int) -> int:
        """获取降级计算的天数
        
        Args:
            interval: 计费间隔类型
            interval_count: 间隔数量
            
        Returns:
            int: 天数
        """
        if interval == BillingInterval.MONTH:
            return 30 * interval_count
        elif interval == BillingInterval.YEAR:
            return 365 * interval_count
        elif interval == BillingInterval.WEEK:
            return 7 * interval_count
        elif interval == BillingInterval.DAY:
            return interval_count
        else:
            return 30 * interval_count  # 默认月度

    @classmethod
    def _add_months(cls, start_date: datetime, months: int) -> datetime:
        """准确地添加月份，处理月末日期

        Args:
            start_date: 开始日期
            months: 要添加的月数

        Returns:
            datetime: 添加月份后的日期
        """
        # 计算目标年月
        target_month = start_date.month + months
        target_year = start_date.year + (target_month - 1) // 12
        target_month = ((target_month - 1) % 12) + 1

        # 处理月末日期（如1月31日 + 1个月 = 2月28/29日）
        target_day = start_date.day
        max_day_in_target_month = calendar.monthrange(target_year, target_month)[1]
        if target_day > max_day_in_target_month:
            target_day = max_day_in_target_month

        return start_date.replace(
            year=target_year,
            month=target_month,
            day=target_day
        )

    @classmethod
    def _add_years(cls, start_date: datetime, years: int) -> datetime:
        """准确地添加年份，处理闰年

        Args:
            start_date: 开始日期
            years: 要添加的年数

        Returns:
            datetime: 添加年份后的日期
        """
        target_year = start_date.year + years

        # 处理闰年2月29日的情况
        if start_date.month == 2 and start_date.day == 29:
            # 如果目标年不是闰年，则调整为2月28日
            if not calendar.isleap(target_year):
                return start_date.replace(year=target_year, day=28)

        return start_date.replace(year=target_year)
