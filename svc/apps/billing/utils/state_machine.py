"""
订阅状态机工具类

这个模块实现了订阅状态转换的状态机逻辑，确保状态转换的合法性和一致性。
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple

from ..constants.business_rules import SubscriptionRules
from ..models.subscription import SubscriptionEvent, SubscriptionStatus

logger = logging.getLogger(__name__)


class SubscriptionStateMachine:
    """订阅状态机
    
    管理订阅状态转换规则和验证逻辑
    """
    
    # 定义状态转换规则
    TRANSITIONS: Dict[SubscriptionStatus, Dict[SubscriptionEvent, SubscriptionStatus]] = {
        SubscriptionStatus.DRAFT: {
            SubscriptionEvent.ACTIVATE: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.PAYMENT_SUCCESS: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.TRIAL_START: SubscriptionStatus.TRIALING,
            SubscriptionEvent.CANCEL: SubscriptionStatus.CANCELED,
            SubscriptionEvent.EXPIRE: SubscriptionStatus.EXPIRED,
        },
        SubscriptionStatus.TRIALING: {
            SubscriptionEvent.TRIAL_END: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.PAYMENT_SUCCESS: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.PAYMENT_FAILED: SubscriptionStatus.DRAFT,
            SubscriptionEvent.CANCEL: SubscriptionStatus.CANCELED,
            SubscriptionEvent.EXPIRE: SubscriptionStatus.EXPIRED,
            SubscriptionEvent.UPGRADE: SubscriptionStatus.TRIALING,
            SubscriptionEvent.DOWNGRADE: SubscriptionStatus.TRIALING,
        },
        SubscriptionStatus.ACTIVE: {
            SubscriptionEvent.PAYMENT_FAILED: SubscriptionStatus.PAST_DUE,
            SubscriptionEvent.PAYMENT_SUCCESS: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.CANCEL: SubscriptionStatus.CANCELED,
            SubscriptionEvent.EXPIRE: SubscriptionStatus.EXPIRED,
            SubscriptionEvent.UPGRADE: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.DOWNGRADE: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.RENEW: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.SUSPEND: SubscriptionStatus.PAST_DUE,
        },
        SubscriptionStatus.PAST_DUE: {
            SubscriptionEvent.PAYMENT_SUCCESS: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.REACTIVATE: SubscriptionStatus.ACTIVE,
            SubscriptionEvent.CANCEL: SubscriptionStatus.CANCELED,
            SubscriptionEvent.EXPIRE: SubscriptionStatus.EXPIRED,
        },
        SubscriptionStatus.CANCELED: {
            SubscriptionEvent.EXPIRE: SubscriptionStatus.EXPIRED,
            # 取消状态下通常不允许其他转换，除非有特殊业务需求
        },
        SubscriptionStatus.EXPIRED: {
            # 过期状态是终态，不能转换到其他状态
        }
    }
    
    # 定义状态的业务属性
    STATUS_PROPERTIES = {
        SubscriptionStatus.DRAFT: {
            "is_active": False,
            "is_billable": False,
            "allows_service_access": False,
            "requires_payment": True,
        },
        SubscriptionStatus.TRIALING: {
            "is_active": True,
            "is_billable": False,
            "allows_service_access": True,
            "requires_payment": False,
        },
        SubscriptionStatus.ACTIVE: {
            "is_active": True,
            "is_billable": True,
            "allows_service_access": True,
            "requires_payment": False,
        },
        SubscriptionStatus.PAST_DUE: {
            "is_active": True,
            "is_billable": True,
            "allows_service_access": True,  # 宽限期内仍可访问
            "requires_payment": True,
        },
        SubscriptionStatus.CANCELED: {
            "is_active": False,
            "is_billable": False,
            "allows_service_access": False,  # 取消后立即停止服务
            "requires_payment": False,
        },
        SubscriptionStatus.EXPIRED: {
            "is_active": False,
            "is_billable": False,
            "allows_service_access": False,
            "requires_payment": False,
        },
    }
    
    @classmethod
    def can_transition(cls, from_status: SubscriptionStatus, event: SubscriptionEvent) -> bool:
        """检查是否可以进行状态转换
        
        Args:
            from_status: 当前状态
            event: 触发事件
            
        Returns:
            bool: 是否可以转换
        """
        return event in cls.TRANSITIONS.get(from_status, {})
    
    @classmethod
    def get_next_status(cls, from_status: SubscriptionStatus, event: SubscriptionEvent) -> Optional[SubscriptionStatus]:
        """获取下一个状态
        
        Args:
            from_status: 当前状态
            event: 触发事件
            
        Returns:
            Optional[SubscriptionStatus]: 下一个状态，如果转换无效则返回None
        """
        return cls.TRANSITIONS.get(from_status, {}).get(event)
    
    @classmethod
    def get_valid_events(cls, from_status: SubscriptionStatus) -> List[SubscriptionEvent]:
        """获取当前状态下的所有有效事件
        
        Args:
            from_status: 当前状态
            
        Returns:
            List[SubscriptionEvent]: 有效事件列表
        """
        return list(cls.TRANSITIONS.get(from_status, {}).keys())
    
    @classmethod
    def get_reachable_statuses(cls, from_status: SubscriptionStatus) -> List[SubscriptionStatus]:
        """获取从当前状态可以直接到达的所有状态
        
        Args:
            from_status: 当前状态
            
        Returns:
            List[SubscriptionStatus]: 可到达状态列表
        """
        return list(cls.TRANSITIONS.get(from_status, {}).values())
    
    @classmethod
    def is_terminal_status(cls, status: SubscriptionStatus) -> bool:
        """检查是否为终态状态
        
        Args:
            status: 状态
            
        Returns:
            bool: 是否为终态
        """
        return len(cls.TRANSITIONS.get(status, {})) == 0
    
    @classmethod
    def get_status_property(cls, status: SubscriptionStatus, property_name: str) -> bool:
        """获取状态的业务属性
        
        Args:
            status: 状态
            property_name: 属性名
            
        Returns:
            bool: 属性值
        """
        return cls.STATUS_PROPERTIES.get(status, {}).get(property_name, False)
    
    @classmethod
    def allows_service_access(cls, status: SubscriptionStatus) -> bool:
        """检查状态是否允许访问服务
        
        Args:
            status: 状态
            
        Returns:
            bool: 是否允许访问服务
        """
        return cls.get_status_property(status, "allows_service_access")
    
    @classmethod
    def is_billable(cls, status: SubscriptionStatus) -> bool:
        """检查状态是否可计费
        
        Args:
            status: 状态
            
        Returns:
            bool: 是否可计费
        """
        return cls.get_status_property(status, "is_billable")
    
    @classmethod
    def requires_payment(cls, status: SubscriptionStatus) -> bool:
        """检查状态是否需要支付
        
        Args:
            status: 状态
            
        Returns:
            bool: 是否需要支付
        """
        return cls.get_status_property(status, "requires_payment")
    
    @classmethod
    def validate_transition(cls, from_status: SubscriptionStatus, to_status: SubscriptionStatus, 
                          event: SubscriptionEvent) -> Tuple[bool, str]:
        """验证状态转换的完整性
        
        Args:
            from_status: 当前状态
            to_status: 目标状态
            event: 触发事件
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误消息)
        """
        # 检查事件是否有效
        if not cls.can_transition(from_status, event):
            return False, f"状态 {from_status} 下不允许事件 {event}"
        
        # 检查目标状态是否正确
        expected_status = cls.get_next_status(from_status, event)
        if expected_status != to_status:
            return False, f"事件 {event} 应该导致状态转换为 {expected_status}，而不是 {to_status}"
        
        return True, ""
    
    @classmethod
    def get_transition_path(cls, from_status: SubscriptionStatus, 
                          to_status: SubscriptionStatus) -> Optional[List[Tuple[SubscriptionEvent, SubscriptionStatus]]]:
        """获取从一个状态到另一个状态的转换路径
        
        Args:
            from_status: 起始状态
            to_status: 目标状态
            
        Returns:
            Optional[List[Tuple[SubscriptionEvent, SubscriptionStatus]]]: 转换路径，如果无法到达则返回None
        """
        if from_status == to_status:
            return []
        
        # 使用广度优先搜索找到最短路径
        from collections import deque
        
        queue = deque([(from_status, [])])
        visited = {from_status}
        
        while queue:
            current_status, path = queue.popleft()
            
            # 检查所有可能的转换
            for event, next_status in cls.TRANSITIONS.get(current_status, {}).items():
                if next_status == to_status:
                    return path + [(event, next_status)]
                
                if next_status not in visited:
                    visited.add(next_status)
                    queue.append((next_status, path + [(event, next_status)]))
        
        return None  # 无法到达目标状态
