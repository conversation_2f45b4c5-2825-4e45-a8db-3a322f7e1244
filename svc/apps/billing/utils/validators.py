"""
业务规则验证工具类

这个模块提供订阅系统的各种业务规则验证功能。
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from svc.core.exceptions.error_codes import ErrorCode

from ..constants.business_rules import SubscriptionRules, ValidationRules
from ..models.subscription import PlanChangeType, SubscriptionStatus
from ..utils.state_machine import SubscriptionStateMachine

logger = logging.getLogger(__name__)


class SubscriptionValidator:
    """订阅业务规则验证器"""
    
    @classmethod
    def validate_subscription_creation(cls, user_id: int, plan, existing_subscriptions: List = None) -> Tuple[bool, str, ErrorCode]:
        """验证订阅创建

        Args:
            user_id: 用户ID
            plan: 订阅计划
            existing_subscriptions: 现有订阅列表

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        if existing_subscriptions is None:
            existing_subscriptions = []

        # 检查用户是否已有活跃订阅
        active_subscriptions = [
            sub for sub in existing_subscriptions
            if sub.status in SubscriptionStatus.get_active_statuses()
        ]

        if len(active_subscriptions) >= SubscriptionRules.MAX_SUBSCRIPTIONS_PER_USER:
            return False, ErrorCode.get_message(ErrorCode.SUBSCRIPTION_EXISTS), ErrorCode.SUBSCRIPTION_EXISTS

        # 检查是否有多个草稿订阅
        if not SubscriptionRules.ALLOW_MULTIPLE_DRAFT_SUBSCRIPTIONS:
            draft_subscriptions = [
                sub for sub in existing_subscriptions
                if sub.status == SubscriptionStatus.DRAFT
            ]
            if draft_subscriptions:
                return False, "用户已有草稿订阅，请先处理现有订阅", ErrorCode.SUBSCRIPTION_EXISTS

        # 验证计划
        plan_valid, plan_error, plan_error_code = cls.validate_plan(plan)
        if not plan_valid:
            return False, plan_error, plan_error_code

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_plan(cls, plan) -> Tuple[bool, str, ErrorCode]:
        """验证订阅计划

        Args:
            plan: 订阅计划对象

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        if not plan:
            return False, ErrorCode.get_message(ErrorCode.PLAN_NOT_FOUND), ErrorCode.PLAN_NOT_FOUND

        # 检查计划是否可用
        if not getattr(plan, 'is_active', True):
            return False, ErrorCode.get_message(ErrorCode.PLAN_NOT_AVAILABLE), ErrorCode.PLAN_NOT_AVAILABLE

        # 验证价格
        if plan.price < ValidationRules.MIN_PLAN_PRICE or plan.price > ValidationRules.MAX_PLAN_PRICE:
            return False, f"计划价格必须在 {ValidationRules.MIN_PLAN_PRICE} 到 {ValidationRules.MAX_PLAN_PRICE} 之间", ErrorCode.INVALID_INPUT

        # 验证计费间隔
        from ..constants.business_rules import BillingRules
        if plan.interval not in BillingRules.SUPPORTED_INTERVALS:
            return False, f"不支持的计费间隔: {plan.interval}", ErrorCode.INVALID_INPUT

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_plan_change(cls, subscription, new_plan, change_type: PlanChangeType) -> Tuple[bool, str, ErrorCode]:
        """验证计划变更

        Args:
            subscription: 当前订阅
            new_plan: 新计划
            change_type: 变更类型

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        # 检查订阅状态是否允许变更
        if subscription.status not in [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]:
            if subscription.status == SubscriptionStatus.TRIALING and not SubscriptionRules.ALLOW_PLAN_CHANGE_IN_TRIAL:
                return False, "试用期内不允许变更计划", ErrorCode.PLAN_CHANGE_NOT_ALLOWED
            elif subscription.status != SubscriptionStatus.ACTIVE:
                return False, f"订阅状态为 {subscription.status}，不允许变更计划", ErrorCode.PLAN_CHANGE_NOT_ALLOWED

        # 验证新计划
        plan_valid, plan_error, plan_error_code = cls.validate_plan(new_plan)
        if not plan_valid:
            return False, plan_error, plan_error_code

        # 检查是否为相同计划
        if subscription.plan_id == new_plan.id:
            return False, ErrorCode.get_message(ErrorCode.PLAN_SAME_AS_CURRENT), ErrorCode.PLAN_SAME_AS_CURRENT

        # 检查降级限制
        if change_type == PlanChangeType.DOWNGRADE:
            downgrade_valid, downgrade_error, downgrade_error_code = cls.validate_downgrade(subscription)
            if not downgrade_valid:
                return False, downgrade_error, downgrade_error_code

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_downgrade(cls, subscription) -> Tuple[bool, str, ErrorCode]:
        """验证降级操作

        Args:
            subscription: 订阅对象

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        # 检查是否在首月内降级
        if not SubscriptionRules.ALLOW_DOWNGRADE_IN_FIRST_MONTH:
            from svc.core.utils.datetime_utils import \
                get_utc_now_without_tzinfo
            now = get_utc_now_without_tzinfo()
            if subscription.created_at and (now - subscription.created_at).days < 30:
                return False, "订阅首月内不允许降级", ErrorCode.PLAN_DOWNGRADE_RESTRICTED

        # 检查是否有待执行的降级
        if subscription.meta_data and subscription.meta_data.get("pending_downgrade"):
            return False, "已有待执行的降级操作", ErrorCode.PLAN_CHANGE_NOT_ALLOWED

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_cancellation(cls, subscription, user_cancellation_history: List = None) -> Tuple[bool, str, ErrorCode]:
        """验证取消订阅

        Args:
            subscription: 订阅对象
            user_cancellation_history: 用户取消历史

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        if user_cancellation_history is None:
            user_cancellation_history = []

        # 检查订阅状态
        if subscription.status in [SubscriptionStatus.CANCELED, SubscriptionStatus.EXPIRED]:
            return False, ErrorCode.get_message(ErrorCode.SUBSCRIPTION_ALREADY_CANCELED), ErrorCode.SUBSCRIPTION_ALREADY_CANCELED

        # 检查取消频率
        from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
        now = get_utc_now_without_tzinfo()
        recent_cancellations = [
            cancel for cancel in user_cancellation_history
            if (now - cancel.get('canceled_at', now)).days <= 30
        ]

        if len(recent_cancellations) >= SubscriptionRules.MAX_CANCELLATIONS_PER_MONTH:
            return False, "取消订阅过于频繁", ErrorCode.OPERATION_FAILED

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_trial_eligibility(cls, user_id: int, plan, user_trial_history: List = None) -> Tuple[bool, str, ErrorCode]:
        """验证试用期资格

        Args:
            user_id: 用户ID
            plan: 订阅计划
            user_trial_history: 用户试用历史

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        if user_trial_history is None:
            user_trial_history = []

        # 检查计划是否提供试用期
        if not plan.trial_period_days or plan.trial_period_days <= 0:
            return False, ErrorCode.get_message(ErrorCode.TRIAL_NOT_AVAILABLE), ErrorCode.TRIAL_NOT_AVAILABLE

        # 检查用户是否已使用过试用期
        plan_trials = [
            trial for trial in user_trial_history
            if trial.get('plan_id') == plan.id
        ]

        if plan_trials:
            return False, ErrorCode.get_message(ErrorCode.TRIAL_ALREADY_USED), ErrorCode.TRIAL_ALREADY_USED

        return True, "", ErrorCode.INTERNAL_ERROR
    
    @classmethod
    def validate_payment_requirement(cls, subscription, plan) -> Tuple[bool, str, ErrorCode]:
        """验证支付要求

        Args:
            subscription: 订阅对象
            plan: 订阅计划

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        # 免费计划不需要支付
        if plan.price <= 0:
            return True, "", ErrorCode.INTERNAL_ERROR

        # 试用期内不需要立即支付
        if subscription.status == SubscriptionStatus.TRIALING:
            return True, "", ErrorCode.INTERNAL_ERROR

        # 其他情况需要支付
        if SubscriptionStateMachine.requires_payment(subscription.status):
            return False, "需要支付才能继续使用服务", ErrorCode.PAYMENT_REQUIRED

        return True, "", ErrorCode.INTERNAL_ERROR

    @classmethod
    def validate_date_range(cls, start_date: datetime, end_date: datetime) -> Tuple[bool, str, ErrorCode]:
        """验证日期范围

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Tuple[bool, str, ErrorCode]: (是否有效, 错误消息, 错误码)
        """
        if start_date >= end_date:
            return False, "开始日期必须早于结束日期", ErrorCode.INVALID_INPUT

        # 检查日期是否过于久远
        from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
        now = get_utc_now_without_tzinfo()
        max_future = now + timedelta(days=365 * ValidationRules.MAX_FUTURE_DATE_YEARS)

        if end_date > max_future:
            return False, f"结束日期不能超过 {ValidationRules.MAX_FUTURE_DATE_YEARS} 年", ErrorCode.INVALID_INPUT

        # 检查最小持续时间
        min_duration = timedelta(days=ValidationRules.MIN_SUBSCRIPTION_DURATION_DAYS)
        if (end_date - start_date) < min_duration:
            return False, f"订阅持续时间不能少于 {ValidationRules.MIN_SUBSCRIPTION_DURATION_DAYS} 天", ErrorCode.INVALID_INPUT

        return True, "", ErrorCode.INTERNAL_ERROR
