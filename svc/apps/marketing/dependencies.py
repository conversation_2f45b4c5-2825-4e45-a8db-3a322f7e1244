"""
依赖注入模块
提供营销服务的依赖项和配置函数。
"""
# 第三方库导入
from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.dependencies import get_user_service
from svc.apps.marketing.repositories.campaign import CampaignRepository
from svc.apps.marketing.repositories.invitation import InvitationRepository
from svc.apps.marketing.repositories.reward import (
    RewardDistributionChannelRepository, RewardRecordRepository,
    RewardStrategyRepository)
from svc.apps.marketing.services.campaign import CampaignService
from svc.apps.marketing.services.invitation import InvitationService
from svc.apps.marketing.services.reward import (RewardDistributionService,
                                                RewardRecordService,
                                                RewardStrategyService)
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route

# 项目内模块导入

"""营销依赖项模块（不再负责资源注册）。"""


# 可选：如果有资源类型需要注册到权限系统
def setup_marketing_dependencies():
    """保留占位：仅用于依赖组装，不做资源注册。"""
    pass


async def get_campaign_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> CampaignRepository:
    """
    提供CampaignRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        CampaignRepository: 活动仓库实例
    """
    return CampaignRepository(db)


async def get_invitation_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> InvitationRepository:
    """
    提供InvitationRepository实例的依赖
    
    Args:
        db: 数据库会话
        
    Returns:
        InvitationRepository: 邀请仓库实例
    """
    return InvitationRepository(db)


async def get_reward_strategy_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RewardStrategyRepository:
    """
    提供RewardStrategyRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:
        RewardStrategyRepository: 奖励策略仓库实例  
    """
    return RewardStrategyRepository(db)


async def get_reward_record_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RewardRecordRepository:
    """
    提供RewardRecordRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:        
        RewardRecordRepository: 奖励记录仓库实例
    """
    return RewardRecordRepository(db)


async def get_reward_distribution_channel_repository(
    db: AsyncSession = Depends(get_session_for_route)
) -> RewardDistributionChannelRepository:
    """
    提供RewardDistributionChannelRepository实例的依赖

    Args:
        db: 数据库会话

    Returns:        
        RewardDistributionChannelRepository: 奖励分发渠道仓库实例
    """
    return RewardDistributionChannelRepository(db)


async def get_campaign_service(
    db: AsyncSession = Depends(get_session_for_route),
    redis: Redis = Depends(get_redis),
    campaign_repo: CampaignRepository = Depends(get_campaign_repository)
) -> CampaignService:
    """
    提供CampaignService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        campaign_repo: 活动仓库实例
        
    Returns:
        CampaignService: 活动服务实例
    """
    service = CampaignService(campaign_repo=campaign_repo, redis=redis)
    return service





async def get_reward_strategy_service(
    redis: Redis = Depends(get_redis),
    reward_strategy_repo: RewardStrategyRepository = Depends(get_reward_strategy_repository)
) -> RewardStrategyService:
    """
    提供RewardStrategyService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        
    Returns:
        RewardStrategyService: 奖励策略服务实例
    """
    return RewardStrategyService(redis=redis, reward_strategy_repo=reward_strategy_repo)


async def get_reward_record_service(
    redis: Redis = Depends(get_redis),
    reward_record_repo: RewardRecordRepository = Depends(get_reward_record_repository),
    invitation_repo: InvitationRepository = Depends(get_invitation_repository),
    campaign_service: CampaignService = Depends(get_campaign_service),
    strategy_service: RewardStrategyService = Depends(get_reward_strategy_service)
) -> RewardRecordService:
    """
    提供RewardRecordService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        reward_record_repo: 奖励记录仓库实例
    Returns:
        RewardRecordService: 奖励记录服务实例
    """
    return RewardRecordService(redis=redis, reward_record_repo=reward_record_repo, invitation_repo=invitation_repo, campaign_service=campaign_service, strategy_service=strategy_service)


async def get_invitation_service(
    redis: Redis = Depends(get_redis),
    user_service = Depends(get_user_service),
    invitation_repo: InvitationRepository = Depends(get_invitation_repository),
    campaign_service: CampaignService = Depends(get_campaign_service),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service)
) -> InvitationService:
    """
    提供InvitationService实例的依赖
    
    Args:
        db: 数据库会话
        redis: Redis客户端
        user_service: 用户服务实例 # 添加文档
        invitation_repo: 邀请仓库实例
        campaign_service: 活动服务实例
        reward_record_service: 奖励记录服务实例 # 确保 RewardRecordService 类型被导入
        
    Returns:
        InvitationService: 邀请服务实例
    """

    return InvitationService(
        user_service=user_service,
        redis=redis, 
        invitation_repo=invitation_repo, 
        campaign_service=campaign_service, 
        reward_record_service=reward_record_service
    )


async def get_reward_distribution_service(
    redis: Redis = Depends(get_redis),
    channel_repo: RewardDistributionChannelRepository = Depends(get_reward_distribution_channel_repository)
) -> RewardDistributionService:
    """
    提供RewardDistributionService实例的依赖
    
    Args:
        redis: Redis客户端
        channel_repo: 分发渠道仓库实例
        
    Returns:
        RewardDistributionService: 奖励分发服务实例
    """
    return RewardDistributionService(channel_repo=channel_repo, redis=redis)


# 保留为应用启动期显式调用# 保留为应用启动期显式调用