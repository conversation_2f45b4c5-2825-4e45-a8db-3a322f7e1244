"""
潜在客户 (Lead) 相关的事件处理器 (已重构支持 DI, 移除kwargs)
"""
from svc.core.events.event_names import MARKETING_LEAD_CAPTURED, MARKETING_REWARD_ISSUE_REQUESTED, SYSTEM_AUDIT_LOG_RECORDED
from fastapi_events.dispatcher import dispatch
from svc.core.events.local_handlers import local_handler
import logging
from fastapi_events.typing import Event 

logger = logging.getLogger(__name__)




@local_handler.register(event_name=MARKETING_LEAD_CAPTURED)
async def handle_lead_converted(
    event: Event, # Changed signature
    # lead_service: LeadService = Depends(get_lead_service) # Inject if needed
    # lead_id: int, # Now in payload
    # user_id: int, # Now in payload
    # campaign_id: Optional[int] = None, # Now in payload
    # converted_by: Optional[int] = None, # Now in payload
    # source: Optional[str] = None, # Now in payload
):
    """处理潜在客户转化事件 (使用 DI 模式)。"""
    event_name, payload = event
    lead_id = payload.get('lead_id')
    user_id = payload.get('user_id') # The user the lead converted into
    campaign_id = payload.get('campaign_id')
    converted_by = payload.get('converted_by') # User/system triggering conversion
    source = payload.get('source')

    if not lead_id or not user_id:
        logger.error("处理潜在客户转化事件失败: 缺少 lead_id 或 user_id")
        return

    # 实现潜在客户转化后的处理逻辑，例如:
    # - 更新用户状态
    # - 触发营销自动化流程
    # - 发放转化奖励 (如果适用)
    # - 更新统计数据
    logger.info(f"处理潜在客户转化事件: lead_id={lead_id}, user_id={user_id}")

    # 示例: 触发奖励发放请求 (如果转化会给奖励)
    # Use payload data
    # campaign_id = kwargs.get('campaign_id')
    if campaign_id:
        logger.info(f"触发转化奖励请求: user={user_id}, lead={lead_id}, campaign={campaign_id}")
        dispatch(
            MARKETING_REWARD_ISSUE_REQUESTED,
            payload={ # Pass data in payload
                "user_id": user_id,
                "milestone_type": "lead_converted",
                "related_entity_id": lead_id,
                "campaign_id": campaign_id,
                # DI is preferred in the target handler
            }
        )

    # 示例: 触发审计日志
    # Construct metadata from payload data
    metadata = {
        "lead_id": lead_id,
        "user_id": user_id,
        "campaign_id": campaign_id,
        "source": source
    }
    dispatch(
        SYSTEM_AUDIT_LOG_RECORDED,
        payload={ # Pass data in payload
            "user_id": converted_by or user_id, # Use converter or the user
            "action": "lead_converted",
            "resource_type": "lead",
            "resource_id": lead_id,
            "metadata": metadata
        }
    )

    # Placeholder for other logic
    pass