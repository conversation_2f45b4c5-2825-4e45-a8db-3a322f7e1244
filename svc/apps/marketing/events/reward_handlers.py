"""
奖励相关的后台任务 (Arq Tasks) 和本地事件处理器 (pyee)
"""
# 标准库导入
import logging
from typing import Any, Dict, Optional

# 第三方库导入
from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event
from redis.asyncio import Redis

# 项目内模块导入
from svc.apps.marketing.dependencies import get_reward_record_service
from svc.apps.marketing.services.reward import RewardRecordService, RewardStrategyService
from svc.core.events.event_names import (MARKETING_REWARD_FAILED,
                                         MARKETING_REWARD_ISSUED,
                                         MARKETING_REWARD_ISSUE_REQUESTED,
                                         MARKETING_REWARD_RECORD_CREATED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_NOTIFICATION_SEND_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

logger = logging.getLogger(__name__)


# --- Arq Tasks --- 
# Registered in arq_worker.WorkerSettings
@local_handler.register(event_name=MARKETING_REWARD_ISSUE_REQUESTED)
async def handle_reward_issue_requested(
    event: Event,
    reward_record_service: RewardRecordService = Depends(get_reward_record_service)
):
    """处理奖励发放请求 (Arq Task) (使用 DI)。"""
    event_name, payload = event
    invitation_id = payload.get('invitation_id')

    if not invitation_id:
        logger.error("处理奖励发放请求失败: 缺少 invitation_id")
        return

    logger.info(f"[Arq Task] 处理奖励发放请求: invitation_id={invitation_id}")

    try:
        # 调用服务层方法处理奖励发放
        result = await reward_record_service.process_invitation_rewards(invitation_id=invitation_id)
        
        if result.is_success:
            reward_ids = result.data.get('reward_record_ids', [])
            logger.info(f"奖励发放成功 for invitation_id={invitation_id}. Reward IDs: {reward_ids}")
            
            # 触发奖励发放成功事件
            if reward_ids:
                # 为每个奖励记录发送单独的事件
                for reward_id in reward_ids:
                    # 获取奖励记录详情以获取用户ID
                    try:
                        reward_record = await reward_record_service.get_resource_by_id(reward_id)
                        if reward_record:
                            dispatch(
                                MARKETING_REWARD_ISSUED,
                                payload={
                                    "reward_record_id": reward_id,
                                    "user_id": reward_record.user_id,
                                    "campaign_id": reward_record.campaign_id,
                                    "reward_type": reward_record.reward_type,
                                    "description": reward_record.reward_description,
                                    "invitation_id": invitation_id,
                                    "reward_record_ids": reward_ids,
                                    "status": "success",
                                    "processed_at": get_utc_now_without_tzinfo().isoformat()
                                }
                            )
                        else:
                            logger.warning(f"无法获取奖励记录 {reward_id} 的详情")
                    except Exception as e:
                        logger.error(f"获取奖励记录 {reward_id} 详情失败: {e}")
            else:
                logger.info(f"邀请 {invitation_id} 没有适用的奖励策略，跳过奖励发放")
        else:
            logger.error(f"奖励发放失败 for invitation_id={invitation_id}. Reason: {result.result_msg}")
            
            # 根据错误码决定是否重试
            if result.result_code in [ErrorCode.OPERATION_FAILED, ErrorCode.NOT_FOUND, ErrorCode.CONFLICT]:
                logger.warning(f"可重试错误，将重新尝试: invitation_id={invitation_id}, error_code={result.result_code}")
                raise RuntimeError(f"Retriable error issuing reward for invitation {invitation_id}: {result.result_msg}")
            else:
                # 对于非临时性错误，记录 MARKETING_REWARD_FAILED 事件
                dispatch(
                    MARKETING_REWARD_FAILED,
                    payload={
                        "invitation_id": invitation_id,
                        "reason": result.result_msg,
                        "error_code": result.result_code if result.result_code else None,
                        "failed_at": get_utc_now_without_tzinfo().isoformat()
                    }
                )
                return
                
    except Exception as e:
        logger.error(f"处理奖励发放请求时发生意外错误: invitation_id={invitation_id}, 错误={str(e)}", exc_info=True)
        raise 


@local_handler.register(event_name=MARKETING_REWARD_RECORD_CREATED)
async def handle_reward_record_created_local(
    event: Event,
):
    """处理奖励记录创建的本地事件 (示例): 更新统计或触发通知。"""
    event_name, payload = event
    reward_record_id = payload.get('reward_record_id')
    user_id = payload.get('user_id')
    status = payload.get('status')
    campaign_id = payload.get('campaign_id') 
    reward_type = payload.get('reward_type')

    if not reward_record_id or not user_id:
        logger.error("处理奖励记录创建事件失败: 缺少 reward_record_id 或 user_id")
        return

    logger.info(f"[Local Event] 奖励记录已创建: id={reward_record_id}, user_id={user_id}, status={status}")

    # 示例：触发统计更新任务 (后台)
    try:
        # 更新用户维度的统计
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "rewards_total_count",
                "increment_value": 1,
                "metadata": {"campaign_id": campaign_id, "reward_type": reward_type}
            }
        )
        if status == 'pending':
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "user",
                    "entity_id": user_id,
                    "metric_type": "rewards_pending_count",
                    "increment_value": 1,
                    "metadata": {"campaign_id": campaign_id, "reward_type": reward_type}
                }
            )
        # 可以考虑更新活动维度的统计
        if campaign_id:
             dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "campaign",
                    "entity_id": campaign_id,
                    "metric_type": "rewards_generated_count",
                    "increment_value": 1,
                    "metadata": {"user_id": user_id, "reward_type": reward_type}
                }
            )

    except Exception as e:
        logger.error(f"Failed to dispatch stats update for reward record {reward_record_id}: {e}", exc_info=True)

@local_handler.register(event_name=MARKETING_REWARD_ISSUED)
async def handle_reward_issued_local(
    event: Event,
    # reward_record_service: RewardRecordService = Depends(get_reward_record_service) # Inject if needed
):
    """处理奖励发放成功的本地事件 (示例): 清理缓存、更新统计、发送通知。"""
    event_name, payload = event
    user_id = payload.get('user_id')
    reward_record_id = payload.get('reward_record_id')
    campaign_id = payload.get('campaign_id') # Assuming campaign_id is in payload
    reward_type = payload.get('reward_type') # Assuming reward_type is in payload
    reward_description = payload.get('description') # e.g., amount, item name

    if not reward_record_id or not user_id:
        logger.error("处理奖励发放成功事件失败: 缺少 reward_record_id 或 user_id")
        return

    logger.info(f"[Local Event] 奖励已发放: user_id={user_id}, record_id={reward_record_id},{reward_description}")

    # 1. 触发相关缓存失效 (示例)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={
                'resource_type': 'user_rewards',
                'user_id': user_id,
                'metadata': {'campaign_id': campaign_id} # More specific invalidation
            }
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={
                'resource_type': 'user_reward_stats',
                'user_id': user_id,
                'metadata': {'campaign_id': campaign_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for issued reward {reward_record_id}: {e}", exc_info=True)

    # 2. 更新用户统计 (后台任务)
    try:
        # 增加已发放计数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "rewards_issued_count",
                "increment_value": 1,
                "metadata": {"campaign_id": campaign_id, "reward_type": reward_type}
            }
        )
        # 减少待处理计数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "user",
                "entity_id": user_id,
                "metric_type": "rewards_pending_count",
                "increment_value": -1,
                "metadata": {"campaign_id": campaign_id, "reward_type": reward_type}
            }
        )
        # 更新活动维度的统计
        if campaign_id:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "campaign",
                    "entity_id": campaign_id,
                    "metric_type": "rewards_issued_count",
                    "increment_value": 1,
                    "metadata": {"user_id": user_id, "reward_type": reward_type}
                }
            )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for issued reward {reward_record_id}: {e}", exc_info=True)

    # 3. 发送用户通知 (示例)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "user_id": user_id,
                "notification_type": "reward_issued",
                "channel": "in_app", # or email, sms
                "title": "您收到了一份奖励！",
                "message": f"恭喜！您参与活动获得的奖励已发放: {reward_description or '详情请查看奖励记录'}",
                "data": {
                    "reward_record_id": reward_record_id,
                    "campaign_id": campaign_id,
                    "reward_type": reward_type,
                    "reward_details": reward_description
                }
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch user notification for issued reward {reward_record_id}: {e}", exc_info=True)

@local_handler.register(event_name=MARKETING_REWARD_FAILED)
async def handle_reward_failed_local(
    event: Event,
    # reward_record_service: RewardRecordService = Depends(get_reward_record_service) # Inject if needed
):
    """处理奖励发放失败的本地事件 (示例): 记录日志、通知管理员。"""
    event_name, payload = event
    invitation_id = payload.get('invitation_id') # Assuming failure is linked to invitation
    user_id = payload.get('user_id') # Or get user_id if available
    reason = payload.get('reason')
    error_code = payload.get('error_code')
    reward_record_id = payload.get('reward_record_id') # If failure happened after record creation

    log_context = f"invitation_id={invitation_id}" if invitation_id else f"user_id={user_id}" if user_id else f"record_id={reward_record_id}" if reward_record_id else "Unknown context"
    logger.warning(f"[Local Event] 奖励发放失败: Context={log_context}, Reason={reason}, Code={error_code}")

    # 示例: 发送管理员通知 (后台任务)
    try:
        dispatch(
            SYSTEM_NOTIFICATION_SEND_REQUESTED,
            payload={
                "recipient_group": "admin_team", # Or specific admin user IDs
                "channel": "slack", # Example channel
                "title": "奖励发放失败告警",
                "message": f"处理奖励发放失败。上下文: {log_context}. 原因: {reason} (Code: {error_code})",
                "data": payload # Include full payload for context
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch admin notification for failed reward ({log_context}): {e}", exc_info=True)

