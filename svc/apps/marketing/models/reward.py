"""
奖励策略和奖励记录模型
"""
# 标准库导入
import enum
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

# 第三方库导入
from sqlalchemy import (BigInteger, Boolean, Column, DateTime, Float,
                        ForeignKey, Integer, String, Text)
from sqlalchemy.orm import relationship

# 项目内模块导入
from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class RewardType(str, enum.Enum):
    POINTS = "points"           # 积分
    COUPON = "coupon"          # 优惠券
    CASH = "cash"              # 现金
    GIFT = "gift"              # 实物礼品
    DISCOUNT = "discount"      # 折扣

class RewardStrategy(Base, ResourceMixin):
    """
    通用的奖励策略模型，定义奖励规则和条件
    """
    __tablename__ = "reward_strategies"
    __resource_type__ = "reward_strategy"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    campaign_id = Column(BigInteger, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    
    # 基本信息
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, nullable=True, comment="策略描述")
    strategy_type = Column(String(50), nullable=False, comment="策略类型：fixed/percentage/tiered/conditional")
    
    # 触发条件配置
    trigger_config = Column(Text, nullable=False, comment="触发条件配置JSON")
    trigger_events = Column(Text, nullable=True, comment="触发事件列表JSON")
    
    # 目标对象配置
    target_config = Column(Text, nullable=False, comment="目标对象配置JSON")
    
    # 奖励计算配置
    calculation_config = Column(Text, nullable=False, comment="奖励计算配置JSON")
    
    # 限制条件配置
    constraint_config = Column(Text, nullable=True, comment="限制条件配置JSON")
    
    # 分发配置
    distribution_config = Column(Text, nullable=False, comment="分发配置JSON")
    
    # 状态管理
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=0, comment="优先级，数字越大优先级越高")
    
    # 统计信息
    total_issued = Column(Integer, default=0, comment="总发放次数")
    total_value = Column(Float, default=0.0, comment="总发放价值")
    
    # 时间限制
    valid_from = Column(DateTime, nullable=True, comment="生效时间")
    valid_until = Column(DateTime, nullable=True, comment="失效时间")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    campaign = relationship("Campaign", back_populates="reward_strategies")
    reward_records = relationship("RewardRecord", back_populates="strategy", cascade="all, delete-orphan")
    
    def get_trigger_config(self) -> Dict[str, Any]:
        """
        获取触发条件配置
        
        Returns:
            Dict[str, Any]: 触发条件配置字典，解析失败时返回空字典
        """
        try:
            return json.loads(self.trigger_config) if self.trigger_config else {}
        except json.JSONDecodeError:
            return {}
    
    def get_target_config(self) -> Dict[str, Any]:
        """
        获取目标对象配置
        
        Returns:
            Dict[str, Any]: 目标对象配置字典，解析失败时返回空字典
        """
        try:
            return json.loads(self.target_config) if self.target_config else {}
        except json.JSONDecodeError:
            return {}
    
    def get_calculation_config(self) -> Dict[str, Any]:
        """
        获取计算配置
        
        Returns:
            Dict[str, Any]: 计算配置字典，解析失败时返回空字典
        """
        try:
            return json.loads(self.calculation_config) if self.calculation_config else {}
        except json.JSONDecodeError:
            return {}
    
    def get_constraint_config(self) -> Dict[str, Any]:
        """
        获取限制条件配置
        
        Returns:
            Dict[str, Any]: 限制条件配置字典，解析失败时返回空字典
        """
        try:
            return json.loads(self.constraint_config) if self.constraint_config else {}
        except json.JSONDecodeError:
            return {}
    
    def get_distribution_config(self) -> Dict[str, Any]:
        """
        获取分发配置
        
        Returns:
            Dict[str, Any]: 分发配置字典，解析失败时返回空字典
        """
        try:
            return json.loads(self.distribution_config) if self.distribution_config else {}
        except json.JSONDecodeError:
            return {}
    
    def is_valid(self) -> bool:
        """
        检查策略是否有效
        
        检查策略的激活状态和时间有效性。
        
        Returns:
            bool: 策略是否有效
        """
        if not self.is_active:
            return False
            
        now = get_utc_now_without_tzinfo()
        if self.valid_from and self.valid_from > now:
            return False
            
        if self.valid_until and self.valid_until < now:
            return False
            
        return True
    
    def can_trigger(self, context: Dict[str, Any]) -> bool:
        """
        检查是否可以触发
        
        Args:
            context: 触发上下文信息
            
        Returns:
            bool: 是否可以触发
        """
        if not self.is_valid():
            return False
        
        trigger_config = self.get_trigger_config()
        return self._evaluate_trigger_conditions(trigger_config, context)
    
    def calculate_reward(self, context: Dict[str, Any]) -> float:
        """
        计算奖励金额
        
        Args:
            context: 计算上下文信息
            
        Returns:
            float: 计算出的奖励金额
        """
        calculation_config = self.get_calculation_config()
        return self._calculate_reward_amount(calculation_config, context)
    
    def get_target_users(self, context: Dict[str, Any]) -> List[int]:
        """
        获取目标用户列表
        
        Args:
            context: 上下文信息
            
        Returns:
            List[int]: 目标用户ID列表
        """
        target_config = self.get_target_config()
        return self._get_target_users(target_config, context)
    
    def check_constraints(self, context: Dict[str, Any]) -> bool:
        """
        检查限制条件
        
        Args:
            context: 上下文信息
            
        Returns:
            bool: 是否满足限制条件
        """
        constraint_config = self.get_constraint_config()
        return self._check_constraints(constraint_config, context)
    
    def _evaluate_trigger_conditions(self, trigger_config: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        评估触发条件
        
        Args:
            trigger_config: 触发条件配置
            context: 上下文信息
            
        Returns:
            bool: 是否满足触发条件
        """
        trigger_type = trigger_config.get('type', 'event')
        
        if trigger_type == 'event':
            required_events = trigger_config.get('events', [])
            context_events = context.get('events', [])
            return all(event in context_events for event in required_events)
        
        elif trigger_type == 'condition':
            conditions = trigger_config.get('conditions', [])
            return all(self._evaluate_condition(condition, context) for condition in conditions)
        
        elif trigger_type == 'threshold':
            field = trigger_config.get('field')
            operator = trigger_config.get('operator', '>=')
            value = trigger_config.get('value', 0)
            context_value = context.get(field, 0)
            
            if operator == '>=':
                return context_value >= value
            elif operator == '>':
                return context_value > value
            elif operator == '==':
                return context_value == value
            elif operator == '<=':
                return context_value <= value
            elif operator == '<':
                return context_value < value
        
        return False
    
    def _evaluate_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """
        评估单个条件
        
        Args:
            condition: 条件配置
            context: 上下文信息
            
        Returns:
            bool: 是否满足条件
        """
        field = condition.get('field')
        operator = condition.get('operator', '==')
        value = condition.get('value')
        context_value = context.get(field)
        
        if operator == '==':
            return context_value == value
        elif operator == '!=':
            return context_value != value
        elif operator == 'in':
            return context_value in value
        elif operator == 'not_in':
            return context_value not in value
        elif operator == 'contains':
            return value in context_value if context_value else False
        
        return False
    
    def _calculate_reward_amount(self, calculation_config: Dict[str, Any], context: Dict[str, Any]) -> float:
        """
        计算奖励金额
        
        Args:
            calculation_config: 计算配置
            context: 上下文信息
            
        Returns:
            float: 计算出的奖励金额
        """
        strategy_type = calculation_config.get('type', 'fixed')
        
        if strategy_type == 'fixed':
            # 支持 amount 和 value 两种字段名
            return calculation_config.get('amount', calculation_config.get('value', 0.0))
        
        elif strategy_type == 'percentage':
            base_amount = context.get(calculation_config.get('base_field', 'amount'), 0.0)
            # 支持 percentage_rate 和 percentage 两种字段名
            percentage = calculation_config.get('percentage_rate', calculation_config.get('percentage', 0.0))
            return base_amount * percentage / 100
        
        elif strategy_type == 'tiered':
            tiers = calculation_config.get('tiers', [])
            count = context.get(calculation_config.get('count_field', 'count'), 0)
            
            # 找到最高级别的阶梯奖励
            max_reward = 0.0
            for tier in tiers:
                # 支持 threshold 和 min_count 两种字段名
                min_count = tier.get('min_count', tier.get('threshold', 0))
                if count >= min_count:
                    reward = tier.get('reward', 0.0)
                    if reward > max_reward:
                        max_reward = reward
            
            return max_reward
        
        elif strategy_type == 'formula':
            formula = calculation_config.get('formula', '0')
            return self._evaluate_formula(formula, context)
        
        return 0.0
    
    def _evaluate_formula(self, formula: str, context: Dict[str, Any]) -> float:
        """评估公式"""
        try:
            # 简单的公式评估，实际项目中可以使用更安全的表达式引擎
            # 这里仅支持基本的数学运算
            safe_formula = formula.replace('{', '').replace('}', '')
            for key, value in context.items():
                if isinstance(value, (int, float)):
                    safe_formula = safe_formula.replace(key, str(value))
            
            return eval(safe_formula)  # 注意：生产环境中应使用更安全的表达式引擎
        except (ValueError, TypeError, NameError):
            return 0.0
    
    def _get_target_users(self, target_config: Dict[str, Any], context: Dict[str, Any]) -> List[int]:
        """获取目标用户列表"""
        target_type = target_config.get('type', 'single')
        
        if target_type == 'single':
            user_id = context.get(target_config.get('user_field', 'user_id'))
            return [user_id] if user_id else []
        
        elif target_type == 'multiple':
            user_ids = context.get(target_config.get('users_field', 'user_ids'), [])
            return user_ids if isinstance(user_ids, list) else []
        
        elif target_type == 'role':
            role = target_config.get('role')
            # 这里需要调用用户服务获取指定角色的用户
            # return await user_service.get_users_by_role(role)
            return []
        
        elif target_type == 'condition':
            condition = target_config.get('condition', {})
            # 这里需要根据条件查询用户
            # return await user_service.get_users_by_condition(condition)
            return []
        
        return []
    
    def _check_constraints(self, constraint_config: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """检查限制条件"""
        constraints = constraint_config.get('constraints', [])
        
        for constraint in constraints:
            constraint_type = constraint.get('type')
            
            if constraint_type == 'user_limit':
                user_id = context.get('user_id')
                max_rewards = constraint.get('max_rewards')
                if user_id and max_rewards:
                    # 检查用户已获得的奖励次数
                    # current_count = await self._get_user_reward_count(user_id, self.id)
                    # if current_count >= max_rewards:
                    #     return False
                    pass
            
            elif constraint_type == 'time_limit':
                start_time = constraint.get('start_time')
                end_time = constraint.get('end_time')
                now = datetime.now()
                
                if start_time and now < start_time:
                    return False
                if end_time and now > end_time:
                    return False
            
            elif constraint_type == 'total_limit':
                max_total = constraint.get('max_total')
                if max_total and self.total_issued >= max_total:
                    return False
            
            elif constraint_type == 'daily_limit':
                max_daily = constraint.get('max_daily')
                if max_daily:
                    # 检查今日已发放数量
                    # today_count = await self._get_today_reward_count(self.id)
                    # if today_count >= max_daily:
                    #     return False
                    pass
        
        return True
        
    def to_dict(self) -> Dict:
        """将策略对象转换为字典"""
        return {
            "id": self.id,
            "campaign_id": self.campaign_id,
            "name": self.name,
            "description": self.description,
            "strategy_type": self.strategy_type,
            "trigger_config": self.trigger_config,
            "trigger_events": self.trigger_events,
            "target_config": self.target_config,
            "calculation_config": self.calculation_config,
            "constraint_config": self.constraint_config,
            "distribution_config": self.distribution_config,
            "is_active": self.is_active,
            "priority": self.priority,
            "total_issued": self.total_issued,
            "total_value": self.total_value,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class RewardRecord(Base, ResourceMixin):
    """
    通用的奖励记录模型，记录具体的奖励发放情况
    """
    __tablename__ = "reward_records"
    __resource_type__ = "reward_record"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    strategy_id = Column(BigInteger, ForeignKey("reward_strategies.id", ondelete="SET NULL"), nullable=True)
    campaign_id = Column(BigInteger, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    invitation_id = Column(BigInteger, ForeignKey("invitations.id", ondelete="SET NULL"), nullable=True)
    
    # 触发信息
    trigger_event = Column(String(100), nullable=True, comment="触发事件")
    trigger_context = Column(Text, nullable=True, comment="触发上下文JSON")
    
    # 目标用户
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # 奖励信息
    reward_type = Column(String(50), nullable=False, comment="奖励类型")
    reward_value = Column(Float, nullable=False, comment="奖励数值")
    reward_unit = Column(String(20), nullable=True, comment="奖励单位")
    reward_description = Column(String(200), nullable=True, comment="奖励描述")
    
    # 计算信息
    calculation_config = Column(Text, nullable=True, comment="计算配置JSON")
    calculated_value = Column(Float, nullable=True, comment="计算得出的原始值")
    
    # 分发信息
    distribution_channel = Column(String(50), nullable=True, comment="分发渠道")
    distribution_status = Column(String(20), default="pending", comment="分发状态")
    distribution_result = Column(Text, nullable=True, comment="分发结果JSON")
    
    # 状态管理
    status = Column(String(20), default="pending", comment="记录状态：pending/issued/failed/cancelled")
    issued_at = Column(DateTime, nullable=True, comment="发放时间")
    failure_reason = Column(String(200), nullable=True, comment="失败原因")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    strategy = relationship("RewardStrategy", back_populates="reward_records")
    campaign = relationship("Campaign", back_populates="reward_records")
    invitation = relationship("Invitation", back_populates="reward_records")
    user = relationship("User", back_populates="reward_records")
    
    def get_trigger_context(self) -> Dict[str, Any]:
        """获取触发上下文"""
        try:
            return json.loads(self.trigger_context) if self.trigger_context else {}
        except json.JSONDecodeError:
            return {}
    
    def get_calculation_config(self) -> Dict[str, Any]:
        """获取计算配置"""
        try:
            return json.loads(self.calculation_config) if self.calculation_config else {}
        except json.JSONDecodeError:
            return {}
    
    def get_distribution_result(self) -> Dict[str, Any]:
        """获取分发结果"""
        try:
            return json.loads(self.distribution_result) if self.distribution_result else {}
        except json.JSONDecodeError:
            return {}
    
    def to_dict(self) -> Dict:
        """将奖励记录对象转换为字典"""
        return {
            "id": self.id,
            "strategy_id": self.strategy_id,
            "campaign_id": self.campaign_id,
            "invitation_id": self.invitation_id,
            "trigger_event": self.trigger_event,
            "trigger_context": self.trigger_context,
            "user_id": self.user_id,
            "reward_type": self.reward_type,
            "reward_value": self.reward_value,
            "reward_unit": self.reward_unit,
            "reward_description": self.reward_description,
            "calculation_config": self.calculation_config,
            "calculated_value": self.calculated_value,
            "distribution_channel": self.distribution_channel,
            "distribution_status": self.distribution_status,
            "distribution_result": self.distribution_result,
            "status": self.status,
            "issued_at": self.issued_at.isoformat() if self.issued_at else None,
            "failure_reason": self.failure_reason,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class RewardDistributionChannel(Base, ResourceMixin):
    """
    奖励分发渠道模型
    """
    __tablename__ = "reward_distribution_channels"
    __resource_type__ = "reward_distribution_channel"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="渠道名称")
    channel_type = Column(String(50), nullable=False, comment="渠道类型")
    description = Column(Text, nullable=True, comment="渠道描述")
    
    # 渠道配置
    config = Column(Text, nullable=False, comment="渠道配置JSON")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=0, comment="优先级")
    
    # 统计信息
    total_distributed = Column(Integer, default=0, comment="总分发次数")
    total_value = Column(Float, default=0.0, comment="总分发价值")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    def get_config(self) -> Dict[str, Any]:
        """获取渠道配置"""
        try:
            return json.loads(self.config) if self.config else {}
        except json.JSONDecodeError:
            return {}
    
    def to_dict(self) -> Dict:
        """将渠道对象转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "channel_type": self.channel_type,
            "description": self.description,
            "config": self.config,
            "is_active": self.is_active,
            "priority": self.priority,
            "total_distributed": self.total_distributed,
            "total_value": self.total_value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }