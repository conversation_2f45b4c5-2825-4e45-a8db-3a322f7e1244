"""
营销活动数据访问层。
负责活动模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Tuple, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.apps.marketing.models.campaign import Campaign, CampaignStatus
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.models.reward import RewardStrategy
from svc.apps.marketing.schemas.campaign import CampaignCreate, CampaignUpdate


class CampaignRepository(BaseRepository[Campaign, CampaignCreate, CampaignUpdate]):
    """活动仓库类，提供活动数据访问方法
    
    该仓库类实现了Campaign模型的数据访问操作，
    包括活动的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化活动仓库"""
        super().__init__(db,Campaign)

    def _get_default_load_options(self) -> List[Any]:
        """获取活动模型的默认关系加载选项
        
        默认加载：
        - reward_strategies: 奖励策略（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Campaign.reward_strategies)
        ]
    
    async def get_by_name(self, name: str, load_options: Optional[List[Any]] = None) -> Optional[Campaign]:
        """通过名称获取活动
        
        Args:
            name: 活动名称
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[Campaign]: 活动对象，不存在则返回None
        """
        return await self.get_one(name=name, load_options=load_options)
    
    async def get_campaigns(
        self,
        page_num: int = 1,
        page_size: int = 10,
        status: Optional[str] = None,
        search_term: Optional[str] = None,
        order_by: str = "created_at",
        order_direction: str = "desc",
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Campaign], int]:
        """获取活动列表及总数
        
        Args:
            page_num: 页码
            page_size: 每页大小
            status: 活动状态筛选
            search_term: 搜索关键词
            order_by: 排序字段
            order_direction: 排序方向
            load_options: SQLAlchemy加载选项
            
        Returns:
            Tuple[List[Campaign], int]: 活动列表和总记录数
        """
        filters = {}
        if status:
            filters["status"] = status
            
        # 统一使用基类分页，search_term 用 __or__
        adv_filters: Dict[str, Any] = {}
        if search_term:
            adv_filters["__or__"] = {
                "name__ilike": f"%{search_term}%",
                "description__ilike": f"%{search_term}%",
            }

        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction,
            filters=adv_filters or None,
            load_options=load_options,
            **filters,
        )
        return items, (total or 0)
    
    async def get_by_creator_id(self, creator_id: int, page_num: int = 1, page_size: int = 100, order_by: str = "created_at", order_desc: bool = True, load_options: Optional[List[Any]] = None) -> Tuple[List[Campaign], int]:
        """获取创建者的所有活动（分页）
        
        Args:
            creator_id: 创建者ID
            page_num: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_desc: 是否降序
            load_options: SQLAlchemy加载选项
            
        Returns:
            Tuple[List[Campaign], int]: 活动列表和总数
        """
        return await self.get_paginated(
            page_num=page_num, 
            page_size=page_size, 
            order_by=order_by, 
            order_direction="desc" if order_desc else "asc", 
            creator_id=creator_id,
            load_options=load_options
        )
    
    async def get_active_campaigns(self, limit: int = 10, skip: int = 0) -> Tuple[List[Campaign], int]:
        """获取所有活跃状态的活动（分页）
        
        Args:
            db: 数据库会话
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Campaign], int]: 活动列表和总数
        """
        now = get_utc_now_without_tzinfo()
        # 移除直接使用 or_() 的代码，使用 filters 参数
        
        items, total = await self.get_paginated(
            page_num=(skip // limit) + 1,
            page_size=limit,
            order_by="created_at",
            order_direction="desc",
            filters={
                "status": CampaignStatus.ACTIVE,
                "__and__": [
                    {"start_date__is_null": True},
                    {"start_date__lte": now},
                ],
            },
        )
        return items, (total or 0)
    
    async def count_active_campaigns(self) -> int:
        """计算活跃状态的活动数量
        
        Args:
            无

        Returns:
            int: 活动数量
        """
        now = get_utc_now_without_tzinfo()
        return await self.count(filters={
            "status": CampaignStatus.ACTIVE,
            "__and__": [
                {"start_date__is_null": True},
                {"start_date__lte": now},
            ],
        })
    
    async def count_participants(self, campaign_id: int) -> int:
        """计算活动参与人数
        
        Args:
            db: 数据库会话
            campaign_id: 活动ID
            
        Returns:
            int: 参与人数
        """
        campaign = await self.get_by_id(campaign_id)
        return campaign.participant_count if campaign else 0
    
    async def check_user_participation(self, campaign_id: int, user_id: int) -> bool:
        """检查用户是否已参与活动
        
        Args:
            campaign_id: 活动ID
            user_id: 用户ID
            
        Returns:
            bool: 是否已参与
        """
        # 移除跨仓库调用，应在服务层处理
        # 返回 False 表示需要服务层通过其他方式检查
        return False
    
    async def get_campaign_with_invitations(
        self,
        campaign_id: int,
        skip: int = 0,
        limit: int = 20
    ) -> Optional[Campaign]:
        """获取活动（邀请记录应在服务层处理）
        
        Args:
            campaign_id: 活动ID
            skip: 跳过记录数
            limit: 返回记录数上限
            
        Returns:
            Optional[Campaign]: 活动对象
        """
        # 移除跨仓库调用，只返回活动对象
        return await self.get_by_id(campaign_id)
    
    async def get_campaign_invitations(
        self, 
        campaign_id: int
    ) -> List[Invitation]:
        """获取活动的所有邀请记录
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            List[Invitation]: 邀请记录列表
        """
        # 移除跨仓库调用，应在服务层处理
        return []
    
    async def get_campaign_reward_strategies(
        self, 
        campaign_id: int
    ) -> List[RewardStrategy]:
        """获取活动的所有奖励策略
        
        Args:
            campaign_id: 活动ID
            
        Returns:
            List[RewardStrategy]: 奖励策略列表
        """
        # 移除跨仓库调用，应在服务层处理
        return []
    

    
    async def increment_reward_count(
        self, 
        
        campaign_id: int,
        reward_value: float = 0.0
    ) -> Optional[Campaign]:
        """增加活动奖励次数和总价值
        
        Args:
            db: 数据库会话
            campaign_id: 活动ID
            reward_value: 奖励价值
            
        Returns:
            Optional[Campaign]: 更新后的活动对象，不存在则返回None
        """
        campaign = await self.get_by_id(campaign_id)
        if not campaign:
            return None
            
        update_data = {
            "reward_count": campaign.reward_count + 1,
            "total_reward_value": campaign.total_reward_value + reward_value
        }
        return await self.update(campaign, update_data) 