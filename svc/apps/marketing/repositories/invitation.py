"""
邀请数据访问层。
负责邀请模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession


from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.schemas.invitation import InvitationCreate, InvitationUpdate

class InvitationRepository(BaseRepository[Invitation, InvitationCreate, InvitationUpdate]):
    """邀请仓库类，提供邀请数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化邀请仓库"""
        super().__init__(db,Invitation)
    

    

    

    

    
    async def mark_as_used(
        self, 
        
        invitation_id: int,
        invitee_id: int, 
        invitee_ip: str, 
        invitee_device: str
    ) -> Optional[Invitation]:
        """
        标记邀请已使用
        
        Args:
            db: 数据库会话
            invitation_id: 邀请ID
            invitee_id: 被邀请人ID
            invitee_ip: 被邀请人IP
            invitee_device: 被邀请人设备
            
        Returns:
            Optional[Invitation]: 更新后的邀请记录对象，不存在则返回None
        """
        update_data = {
            "invitee_id": invitee_id,
            "invitee_ip": invitee_ip,
            "invitee_device": invitee_device,
            "is_used": True,
            "used_at": get_utc_now_without_tzinfo()
        }
        
        return await self.update_by_id(invitation_id, update_data)
    

    
    async def get_by_campaign(
        self, 
        
        campaign_id: int,
        is_used: Optional[bool] = None,
        page_num: int = 1,
        page_size: int = 100,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Invitation], int]:
        """
        获取活动的邀请记录 (分页)
        
        Args:
            db: 数据库会话
            campaign_id: 活动ID
            is_used: 是否已使用，可选
            page_num: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[Invitation], int]: 邀请记录列表和总数
        """
        filters = {"campaign_id": campaign_id}
        
        if is_used is not None:
            filters["is_used"] = is_used
            
        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            direction="desc" if order_desc else "asc",
            **filters
        )

    async def get_user_invitations(
        self,
       
        user_id: int,
        campaign_id: Optional[int] = None,
        page_num: int = 1,
        page_size: int = 100,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Invitation], int]:
        """
        获取指定用户的邀请列表（作为邀请人），并包含总数。

        Args:
            db: 数据库会话
            user_id: 邀请人ID
            campaign_id: 活动ID（可选）
            page_num: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[Invitation], int]: 包含邀请列表和总记录数的元组
        """
        filters = {"inviter_id": user_id}
        if campaign_id is not None:
            filters["campaign_id"] = campaign_id

        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            direction="desc" if order_desc else "asc",
            **filters
        )

    async def get_invitations(
        self,
        page_num: int = 1,
        page_size: int = 10,
        campaign_id: Optional[int] = None,
        inviter_id: Optional[int] = None,
        is_used: Optional[bool] = None,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Invitation], int]:
        """获取邀请列表（通用，带过滤和分页）

        Args:
            page_num: 页码
            page_size: 每页数量
            campaign_id: 活动ID过滤
            inviter_id: 邀请人ID过滤
            is_used: 是否已使用过滤
            order_by: 排序字段
            order_desc: 是否降序

        Returns:
            Tuple[List[Invitation], int]: 邀请列表和总记录数
        """
        filters = {}
        if campaign_id is not None:
            filters["campaign_id"] = campaign_id
        if inviter_id is not None:
            filters["inviter_id"] = inviter_id
        if is_used is not None:
            filters["is_used"] = is_used

        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            direction="desc" if order_desc else "asc",
            **filters
        )

    async def check_ip_usage_for_campaign(self, campaign_id: int, ip_address: str) -> bool:
        """
        检查指定 IP 是否已在某活动的已使用邀请记录中存在。

        Args:
            db: 数据库会话
            campaign_id: 活动 ID
            ip_address: 要检查的 IP 地址

        Returns:
            bool: 如果 IP 已被使用则返回 True，否则返回 False。
        """
        count = await self.count(
                                 campaign_id=campaign_id, 
                                 invitee_ip=ip_address, 
                                 is_used=True)
        return count > 0

    async def check_device_usage_for_campaign(self, campaign_id: int, device_info: str) -> bool:
        """
        检查指定设备信息是否已在某活动的已使用邀请记录中存在。

        Args:
            db: 数据库会话
            campaign_id: 活动 ID
            device_info: 要检查的设备信息 (User-Agent)

        Returns:
            bool: 如果设备信息已被使用则返回 True，否则返回 False。
        """
        # 注意: User-Agent 可能不是唯一的设备标识符，谨慎使用
        if not device_info: # 如果设备信息为空，则不进行检查
            return False
        count = await self.count(
                                 campaign_id=campaign_id, 
                                 invitee_device=device_info, 
                                 is_used=True)
        return count > 0

    async def record_invitation_usage(
        self, 
        
        invitation_id: int,
        invitee_id: int, 
        invitee_ip: str, 
        invitee_device: str
    ) -> bool:
        """
        记录邀请使用情况，但不标记邀请码为已使用，支持多次使用同一邀请码。
        可以在将来扩展为使用单独的邀请使用记录表。
        
        Args:
            db: 数据库会话
            invitation_id: 邀请ID
            invitee_id: 被邀请人ID
            invitee_ip: 被邀请人IP
            invitee_device: 被邀请人设备
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 这里可以在未来创建一个新表来记录每次使用情况
            # 目前仅增加打开计数，不更改邀请状态
            invitation = await self.get_by_id(invitation_id)
            if not invitation:
                return False
                
            # 更新打开计数
            new_count = invitation.opened_count + 1
            await self.update_by_id(invitation_id, {"opened_count": new_count})
            
            # 可以在这里添加记录到新表的逻辑
            # 例如: await self.create_invitation_usage_record(invitation_id, invitee_id, invitee_ip, invitee_device)
            
            return True
        except Exception as e:
            # 记录错误但不抛出异常
            return False 