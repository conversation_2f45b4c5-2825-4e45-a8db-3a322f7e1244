"""
奖励数据访问层。
负责奖励策略和奖励记录的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Dict, Any, Tuple, Union, TypeVar, Generic
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from datetime import datetime

from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.apps.marketing.models.reward import RewardStrategy, RewardRecord, RewardDistributionChannel, RewardType
from svc.apps.marketing.schemas.reward import (
    RewardStrategyCreate, 
    RewardStrategyUpdate,
    RewardRecordCreate,
    RewardRecordUpdate,
    RewardDistributionChannelCreate,
    RewardDistributionChannelUpdate
)

class RewardStrategyRepository(BaseRepository[RewardStrategy, RewardStrategyCreate, RewardStrategyUpdate]):
    """奖励策略仓库类，提供奖励策略数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化奖励策略仓库"""
        super().__init__(db, RewardStrategy)
    

    

    




class RewardRecordRepository(BaseRepository[RewardRecord, RewardRecordCreate, RewardRecordUpdate]):
    """奖励记录仓库类，提供奖励记录数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化奖励记录仓库"""
        super().__init__(db, RewardRecord)
    
    async def get_user_rewards(
        self, 
        user_id: int,
        campaign_id: Optional[int] = None,
        status: Optional[str] = None,
        page_num: int = 1,
        page_size: int = 20,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[RewardRecord], int]:
        """
        获取用户的奖励记录 (分页)
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            status: 记录状态（可选）
            page_num: 页码
            page_size: 每页数量
            order_by: 排序字段
            order_desc: 是否降序
            
        Returns:
            Tuple[List[RewardRecord], int]: 奖励记录列表和总数
        """
        filters = {"user_id": user_id}
        if campaign_id is not None:
            filters["campaign_id"] = campaign_id
        if status is not None:
            filters["status"] = status

        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            direction="desc" if order_desc else "asc",
            **filters
        )
    
    async def create_reward(
        self,
        user_id: int,
        campaign_id: int,
        strategy_id: Optional[int],
        trigger_event: Optional[str],
        trigger_context: Optional[str],
        reward_type: str,
        reward_value: float,
        calculation_config: Optional[str],
        calculated_value: Optional[float],
        distribution_channel: Optional[str],
        reward_description: Optional[str] = None,
        invitation_id: Optional[int] = None
    ) -> RewardRecord:
        """
        创建奖励记录
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID
            strategy_id: 策略ID
            trigger_event: 触发事件
            trigger_context: 触发上下文
            reward_type: 奖励类型
            reward_value: 奖励数值
            calculation_config: 计算配置
            calculated_value: 计算得出的原始值
            distribution_channel: 分发渠道
            reward_description: 奖励描述
            invitation_id: 邀请ID
            
        Returns:
            RewardRecord: 创建的奖励记录对象
        """
        record = RewardRecord(
            user_id=user_id,
            campaign_id=campaign_id,
            strategy_id=strategy_id,
            trigger_event=trigger_event,
            trigger_context=trigger_context,
            reward_type=reward_type,
            reward_value=reward_value,
            calculation_config=calculation_config,
            calculated_value=calculated_value,
            distribution_channel=distribution_channel,
            reward_description=reward_description,
            invitation_id=invitation_id
        )
        
        self.db.add(record)
        # 事务提交应由服务层或装饰器统一管理
        return record
    
    async def get_rewards_by_trigger_event(
        self, 
        trigger_event: str, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[RewardRecord]:
        """
        根据触发事件获取奖励记录
        
        Args:
            trigger_event: 触发事件
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[RewardRecord]: 奖励记录列表
        """
        query = select(RewardRecord).where(
            RewardRecord.trigger_event == trigger_event,
            RewardRecord.created_at >= start_time,
            RewardRecord.created_at <= end_time
        ).order_by(RewardRecord.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_rewards_by_distribution_channel(
        self, 
        distribution_channel: str, 
        status: str
    ) -> List[RewardRecord]:
        """
        根据分发渠道获取奖励记录
        
        Args:
            distribution_channel: 分发渠道
            status: 记录状态
            
        Returns:
            List[RewardRecord]: 奖励记录列表
        """
        query = select(RewardRecord).where(
            RewardRecord.distribution_channel == distribution_channel,
            RewardRecord.status == status
        ).order_by(RewardRecord.created_at.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def update_reward_status(
        self, 
        record_id: int, 
        status: str, 
        issued_at: Optional[datetime] = None, 
        failure_reason: Optional[str] = None
    ) -> None:
        """
        更新奖励记录状态
        
        Args:
            record_id: 记录ID
            status: 新状态
            issued_at: 发放时间
            failure_reason: 失败原因
        """
        update_data = {"status": status}
        if issued_at is not None:
            update_data["issued_at"] = issued_at
        if failure_reason is not None:
            update_data["failure_reason"] = failure_reason
        
        await self.update_by_id(record_id, update_data)
    
    async def update_distribution_status(
        self, 
        record_id: int, 
        distribution_status: str, 
        distribution_result: Optional[str] = None
    ) -> None:
        """
        更新分发状态
        
        Args:
            record_id: 记录ID
            distribution_status: 分发状态
            distribution_result: 分发结果
        """
        update_data = {"distribution_status": distribution_status}
        if distribution_result is not None:
            update_data["distribution_result"] = distribution_result
        
        await self.update_by_id(record_id, update_data)
    
    async def get_reward_records(
        self,
        page: int = 1,
        page_size: int = 20,
        campaign_id: Optional[int] = None,
        user_id: Optional[int] = None,
        reward_type: Optional[str] = None,
        status: Optional[str] = None,
        strategy_id: Optional[int] = None,
        trigger_event: Optional[str] = None,
        distribution_channel: Optional[str] = None
    ) -> Tuple[List[RewardRecord], int]:
        """
        获取奖励记录列表
        
        Args:
            page: 页码
            page_size: 每页数量
            campaign_id: 活动ID过滤
            user_id: 用户ID过滤
            reward_type: 奖励类型过滤
            status: 状态过滤
            strategy_id: 策略ID过滤
            trigger_event: 触发事件过滤
            distribution_channel: 分发渠道过滤
            
        Returns:
            Tuple[List[RewardRecord], int]: 记录列表和总数
        """
        filters = {}
        if campaign_id is not None:
            filters["campaign_id"] = campaign_id
        if user_id is not None:
            filters["user_id"] = user_id
        if reward_type is not None:
            filters["reward_type"] = reward_type
        if status is not None:
            filters["status"] = status
        if strategy_id is not None:
            filters["strategy_id"] = strategy_id
        if trigger_event is not None:
            filters["trigger_event"] = trigger_event
        if distribution_channel is not None:
            filters["distribution_channel"] = distribution_channel

        return await self.get_paginated(
            page_num=page,
            page_size=page_size,
            order_by="created_at",
            direction="desc",
            **filters
        ) 


class RewardDistributionChannelRepository(BaseRepository[RewardDistributionChannel, RewardDistributionChannelCreate, RewardDistributionChannelUpdate]):
    """奖励分发渠道仓库类，提供分发渠道数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化分发渠道仓库"""
        super().__init__(db, RewardDistributionChannel)
    
    async def get_active_channels(self) -> List[RewardDistributionChannel]:
        """获取活跃的分发渠道"""
        query = select(RewardDistributionChannel).where(
            RewardDistributionChannel.is_active == True
        ).order_by(RewardDistributionChannel.priority.desc())
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_channel_by_type(self, channel_type: str) -> Optional[RewardDistributionChannel]:
        """根据类型获取渠道"""
        query = select(RewardDistributionChannel).where(
            RewardDistributionChannel.channel_type == channel_type,
            RewardDistributionChannel.is_active == True
        )
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def update_channel_stats(
        self, 
        channel_id: int, 
        distributed_count: int, 
        distributed_value: float
    ) -> None:
        """更新渠道统计信息"""
        query = update(RewardDistributionChannel).where(
            RewardDistributionChannel.id == channel_id
        ).values(
            total_distributed=RewardDistributionChannel.total_distributed + distributed_count,
            total_value=RewardDistributionChannel.total_value + distributed_value
        )
        
        await self.db.execute(query)
        # 事务提交应由服务层或装饰器统一管理