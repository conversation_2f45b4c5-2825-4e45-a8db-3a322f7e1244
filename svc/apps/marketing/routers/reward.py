"""
奖励相关的API路由
"""
# 标准库导入
from datetime import datetime
from typing import Any, Dict, List, Optional

# 第三方库导入
from fastapi import APIRouter, Body, Depends, Path, Query

# 项目内模块导入
from svc.apps.auth.dependencies import (get_current_active_user,
                                        get_current_superuser, has_permission)
from svc.apps.auth.models.user import User
from svc.apps.marketing.dependencies import (get_reward_distribution_service,
                                             get_reward_record_service,
                                             get_reward_strategy_service)
from svc.apps.marketing.schemas import (GetRewardRecordsParams,
                                        RewardDistributionChannelCreate,
                                        RewardDistributionChannelResponse,
                                        RewardDistributionChannelUpdate,
                                        RewardRecordListResponse,
                                        RewardStatsResponse,
                                        RewardStrategyListResponse)
from svc.apps.marketing.services.reward import (RewardDistributionService,
                                                RewardRecordService,
                                                RewardStrategyService)
from svc.core.exceptions.route_error_handler import (REWARD_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result

router = APIRouter(
    tags=["奖励"]
)

# === 管理端路由 (Admin Routes) ===
# Paths prefixed with /admin within this router

@router.get("/admin/records/list", response_model=Result[RewardRecordListResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def admin_list_reward_records(
    page_num: int = Query(1, ge=1, description="页码", alias="pageNum"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量", alias="pageSize"),
    campaign_id: Optional[int] = Query(None, description="活动ID过滤", alias="campaignId"),
    order_by: Optional[str] = Query("created_at", description="排序字段", alias="orderBy"),
    order_direction: str = Query("desc", description="排序方向: asc/desc", alias="orderDirection"),
    user_id: Optional[int] = Query(None, description="用户ID过滤", alias="userId"),
    reward_type: Optional[str] = Query(None, description="奖励类型过滤", alias="rewardType"),
    status: Optional[str] = Query(None, description="记录状态过滤", alias="status"),
    strategy_id: Optional[int] = Query(None, description="奖励策略ID过滤", alias="strategyId"),
    trigger_event: Optional[str] = Query(None, description="触发事件过滤", alias="triggerEvent"),
    distribution_channel: Optional[str] = Query(None, description="分发渠道过滤", alias="distributionChannel"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward:read"))
) -> Result[RewardRecordListResponse]:
    """获取奖励记录列表 (管理端)"""
    params = GetRewardRecordsParams(
        page_num=page_num,
        page_size=page_size,
        campaign_id=campaign_id,
        user_id=user_id,
        reward_type=reward_type,
        status=status,
        strategy_id=strategy_id,
        trigger_event=trigger_event,
        distribution_channel=distribution_channel,
        order_by=order_by,
        order_direction=order_direction
    )
    result = await reward_record_service.get_reward_records(params=params)
    return result

@router.get("/admin/strategies/", response_model=Result[Dict[str, Any]])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def admin_get_campaign_strategies(
    campaign_id: int = Query(..., description="活动ID", alias="campaignId"),
    page_num: int = Query(1, ge=1, description="页码", alias="pageNum"),
    page_size: int = Query(100, ge=1, le=100, description="每页记录数", alias="pageSize"),
    strategy_type: Optional[str] = Query(None, description="策略类型过滤", alias="strategyType"),
    order_by: Optional[str] = Query("priority", description="排序字段", alias="orderBy"),
    order_direction: str = Query("asc", description="排序方向: asc/desc", alias="orderDirection"),
    is_active: Optional[bool] = Query(None, description="是否启用过滤", alias="isActive"),
    reward_strategy_service: RewardStrategyService = Depends(get_reward_strategy_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_strategy:read"))
) -> Result[Dict[str, Any]]:
    """获取活动的奖励策略列表 (管理端)"""
    result = await reward_strategy_service.get_campaign_strategies(
        campaign_id=campaign_id,
        strategy_type=strategy_type,
        is_active=is_active,
        page_num=page_num,
        page_size=page_size,
        order_by=order_by,
        order_direction=order_direction
    )
    return result

@router.get("/admin/strategies/by-event/", response_model=Result[List[Any]])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_strategies_by_event(
    campaign_id: int = Query(..., description="活动ID", alias="campaignId"),
    trigger_event: str = Query(..., description="触发事件", alias="triggerEvent"),
    reward_strategy_service: RewardStrategyService = Depends(get_reward_strategy_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_strategy:read"))
) -> Result[List[Any]]:
    """根据触发事件获取策略 (管理端)"""
    result = await reward_strategy_service.get_strategies_by_event(campaign_id, trigger_event)
    return result

@router.put("/admin/strategies/{strategy_id}/priority/", response_model=Result[Any])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def update_strategy_priority(
    strategy_id: int = Path(..., description="策略ID"),
    priority: int = Body(..., description="优先级"),
    reward_strategy_service: RewardStrategyService = Depends(get_reward_strategy_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_strategy:update"))
) -> Result[Any]:
    """更新策略优先级 (管理端)"""
    result = await reward_strategy_service.update_strategy_priority(strategy_id, priority)
    return result

@router.post("/admin/records/distribute/", response_model=Result[Dict[str, Any]])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def distribute_rewards(
    record_ids: List[int] = Body(..., description="记录ID列表"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward:distribute"))
) -> Result[Dict[str, Any]]:
    """分发奖励 (管理端)"""
    result = await reward_record_service.distribute_rewards(record_ids)
    return result

@router.put("/admin/records/{record_id}/status/", response_model=Result[Any])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def update_reward_status(
    record_id: int = Path(..., description="记录ID"),
    status: str = Body(..., description="状态"),
    issued_at: Optional[datetime] = Body(None, description="发放时间"),
    failure_reason: Optional[str] = Body(None, description="失败原因"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward:update"))
) -> Result[Any]:
    """更新奖励记录状态 (管理端)"""
    result = await reward_record_service.update_reward_status(record_id, status, issued_at, failure_reason)
    return result

@router.get("/admin/records/by-event/", response_model=Result[List[Any]])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_rewards_by_trigger_event(
    trigger_event: str = Query(..., description="触发事件", alias="triggerEvent"),
    start_time: datetime = Query(..., description="开始时间", alias="startTime"),
    end_time: datetime = Query(..., description="结束时间", alias="endTime"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward:read"))
) -> Result[List[Any]]:
    """根据触发事件获取奖励记录 (管理端)"""
    result = await reward_record_service.get_rewards_by_trigger_event(trigger_event, start_time, end_time)
    return result

@router.get("/admin/channels/", response_model=Result[List[RewardDistributionChannelResponse]])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_distribution_channels(
    reward_distribution_service: RewardDistributionService = Depends(get_reward_distribution_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_channel:read"))
) -> Result[List[RewardDistributionChannelResponse]]:
    """获取分发渠道列表 (管理端)"""
    result = await reward_distribution_service.get_distribution_channels()
    return result

@router.post("/admin/channels/", response_model=Result[RewardDistributionChannelResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def create_distribution_channel(
    channel: RewardDistributionChannelCreate,
    reward_distribution_service: RewardDistributionService = Depends(get_reward_distribution_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_channel:create"))
) -> Result[RewardDistributionChannelResponse]:
    """创建分发渠道 (管理端)"""
    result = await reward_distribution_service.create_distribution_channel(channel)
    return result

# === 客户端路由 (Client Routes) ===
# Paths relative to /rewards

@router.get("/mine", response_model=Result[RewardRecordListResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_my_rewards(
    page_num: int = Query(1, ge=1, description="页码", alias="pageNum"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数", alias="pageSize"),
    campaign_id: Optional[int] = Query(None, description="按活动ID过滤", alias="campaignId"),
    order_by: Optional[str] = Query("created_at", description="排序字段", alias="orderBy"),
    order_direction: str = Query("desc", description="排序方向: asc/desc", alias="orderDirection"),
    status: Optional[str] = Query(None, description="按记录状态过滤", alias="status"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[RewardRecordListResponse]:
    """获取当前用户的奖励列表 (客户端)"""
    result = await reward_record_service.get_user_rewards(
        user_id=current_user.id, 
        campaign_id=campaign_id,
        status=status,
        page_num=page_num,
        page_size=page_size,
        order_by=order_by,
        order_direction=order_direction
    )
    return result

@router.get("/stats", response_model=Result[RewardStatsResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_my_reward_stats(
    campaign_id: Optional[int] = Query(None, alias="campaignId"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[RewardStatsResponse]:
    """获取当前用户的奖励统计 (客户端)"""
    stats = await reward_record_service.get_reward_stats(current_user.id, campaign_id)
    return stats

