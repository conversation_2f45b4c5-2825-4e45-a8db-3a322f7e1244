"""
营销模块的Pydantic模型定义。
包含所有请求、响应和参数模型。
"""


from svc.apps.marketing.schemas.campaign import (
    CampaignBase, CampaignCreate, CampaignUpdate, CampaignResponse,
    CampaignConfigResponse, CampaignListResponse,
    RewardStrategyBase, RewardStrategyResponse
)
from svc.apps.marketing.schemas.invitation import (
    InvitationCodeResponse, InvitationResponse,
    InvitationStatsResponse, InvitationListResponse,
    GetInvitationsParams,
    InvitationBatchUpdateRequest, InvitationBatchUpdateResponse,
    InvitationBatchDeleteRequest, InvitationBatchDeleteResponse
)
from svc.apps.marketing.schemas.reward import (
    GetRewardRecordsParams,
    RewardStrategyCreate, RewardStrategyUpdate,
    RewardRecordResponse, RewardStatsResponse, RewardRecordListResponse, RewardStrategyListResponse,
    RewardRecordBatchUpdateRequest, RewardRecordBatchUpdateResponse,
    RewardRecordBatchDeleteRequest, RewardRecordBatchDeleteResponse,
    RewardDistributionChannelResponse, RewardDistributionChannelCreate, RewardDistributionChannelUpdate
)

__all__ = [
    # 基础Schema
    "CampaignBase", "CampaignCreate", "CampaignUpdate", "CampaignResponse",
    "CampaignConfigResponse", "CampaignListResponse",
    "RewardStrategyBase", "RewardStrategyResponse",
    
    # Invitation schemas
    "InvitationCodeResponse", "InvitationResponse",
    "InvitationStatsResponse", "InvitationListResponse",
    "GetInvitationsParams",
    "InvitationBatchUpdateRequest", "InvitationBatchUpdateResponse",
    "InvitationBatchDeleteRequest", "InvitationBatchDeleteResponse",
    
    # Reward schemas
    "RewardRecordResponse", "RewardStatsResponse", "RewardRecordListResponse",
    "RewardStrategyListResponse", "RewardStrategyCreate", "RewardStrategyUpdate",
    "GetRewardRecordsParams",
    "RewardRecordBatchUpdateRequest", "RewardRecordBatchUpdateResponse",
    "RewardRecordBatchDeleteRequest", "RewardRecordBatchDeleteResponse",
    "RewardDistributionChannelResponse", "RewardDistributionChannelCreate", "RewardDistributionChannelUpdate"
] 