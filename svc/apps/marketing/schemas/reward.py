# 标准库导入
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

# 第三方库导入
from pydantic import BaseModel, ConfigDict, Field, field_validator

# 项目内模块导入
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.batch import (BatchDeleteRequest, BatchDeleteResponse,
                                   BatchUpdateRequest, BatchUpdateResponse)
from svc.core.schemas.base import PaginatedResponse

class RewardBase(CamelCaseModel):
    user_id: int = Field(..., description="用户ID")
    campaign_id: int = Field(..., description="活动ID")
    reward_type: str = Field(..., description="奖励类型 (e.g., points, discount)")
    value: float = Field(..., description="奖励值")
    is_claimed: bool = Field(False, description="是否已领取")

class RewardCreate(RewardBase):
    pass

class RewardUpdate(CamelCaseModel):
    is_claimed: Optional[bool] = Field(None, description="是否已领取")

class RewardResponse(RewardBase):
    id: int = Field(..., description="奖励ID")
    claimed_at: Optional[datetime] = Field(None, description="领取时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

class RewardRecordResponse(BaseModel):
    """奖励记录响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "invitation_id": 1,
                "strategy_id": 1,
                "user_id": 101,
                "reward_type": "cash",
                "reward_value": 200.0,
                "reward_description": "SHOEI头盔邀请奖励",
                "trigger_event": "invitation.completed",
                "trigger_context": "{\"inviter_id\": 101, \"invitee_id\": 102}",
                "calculation_config": "{\"type\": \"tiered\", \"tiers\": [{\"min_count\": 1, \"reward\": 100}]}",
                "calculated_value": 100.0,
                "distribution_channel": "wallet",
                "distribution_status": "success",
                "distribution_result": "{\"status\": \"success\", \"transaction_id\": \"tx_123\"}",
                "status": "issued",
                "issued_at": "2024-03-24T15:30:00",
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00"
            }
        }
    )
    
    id: int = Field(description="奖励记录ID")
    strategy_id: Optional[int] = Field(default=None, description="策略ID")
    campaign_id: int = Field(description="活动ID")
    invitation_id: Optional[int] = Field(default=None, description="邀请记录ID")
    trigger_event: Optional[str] = Field(default=None, description="触发事件")
    trigger_context: Optional[str] = Field(default=None, description="触发上下文")
    user_id: int = Field(description="用户ID")
    reward_type: str = Field(description="奖励类型")
    reward_value: float = Field(description="奖励值")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    calculation_config: Optional[str] = Field(default=None, description="计算配置")
    calculated_value: Optional[float] = Field(default=None, description="计算得出的原始值")
    distribution_channel: Optional[str] = Field(default=None, description="分发渠道")
    distribution_status: Optional[str] = Field(default=None, description="分发状态")
    distribution_result: Optional[str] = Field(default=None, description="分发结果")
    status: str = Field(description="记录状态")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class RewardRecordCreate(BaseModel):
    """奖励记录创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    campaign_id: int = Field(description="活动ID")
    user_id: int = Field(description="用户ID")
    reward_type: str = Field(description="奖励类型")
    reward_value: float = Field(description="奖励值")
    strategy_id: Optional[int] = Field(default=None, description="策略ID")
    invitation_id: Optional[int] = Field(default=None, description="邀请记录ID")
    trigger_event: Optional[str] = Field(default=None, description="触发事件")
    trigger_context: Optional[str] = Field(default=None, description="触发上下文")
    calculation_config: Optional[str] = Field(default=None, description="计算配置")
    calculated_value: Optional[float] = Field(default=None, description="计算得出的原始值")
    distribution_channel: Optional[str] = Field(default=None, description="分发渠道")
    distribution_status: Optional[str] = Field(default="pending", description="分发状态")
    distribution_result: Optional[str] = Field(default=None, description="分发结果")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    status: Optional[str] = Field(default="pending", description="记录状态")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")

class RewardRecordUpdate(BaseModel):
    """奖励记录更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    reward_value: Optional[float] = Field(default=None, description="奖励值")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    status: Optional[str] = Field(default=None, description="记录状态")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")
    distribution_status: Optional[str] = Field(default=None, description="分发状态")
    distribution_result: Optional[str] = Field(default=None, description="分发结果")

class RewardStatsResponse(BaseModel):
    """奖励统计响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "total_rewards": 89,
                "total_value": 17800.0,
                "issued_rewards": 67,
                "issued_value": 13400.0,
                "pending_rewards": 22,
                "pending_value": 4400.0,
                "failed_rewards": 0,
                "failed_value": 0.0
            }
        }
    )
    
    total_rewards: int = Field(description="总奖励数量")
    total_value: float = Field(description="总奖励价值")
    issued_rewards: int = Field(description="已发放奖励数量")
    issued_value: float = Field(description="已发放奖励价值")
    pending_rewards: int = Field(description="待发放奖励数量")
    pending_value: float = Field(description="待发放奖励价值")
    failed_rewards: int = Field(description="发放失败奖励数量")
    failed_value: float = Field(description="发放失败奖励价值")

class RewardRecordListResponse(PaginatedResponse[RewardRecordResponse]):
    """奖励记录列表响应模型"""
    pass

class RewardStrategyResponse(BaseModel):
    """奖励策略响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "name": "邀请奖励策略",
                "description": "邀请好友获得奖励",
                "strategy_type": "tiered",
                "trigger_config": "{\"type\": \"event\", \"events\": [\"invitation.completed\"]}",
                "trigger_events": "[\"invitation.completed\"]",
                "target_config": "{\"type\": \"multiple\", \"users_field\": \"inviter_id,invitee_id\"}",
                "calculation_config": "{\"type\": \"tiered\", \"count_field\": \"invitation_count\", \"tiers\": [{\"min_count\": 1, \"reward\": 100}]}",
                "constraint_config": "{\"constraints\": [{\"type\": \"user_limit\", \"max_rewards\": 10}]}",
                "distribution_config": "{\"channels\": [\"wallet\"], \"priority\": \"wallet\"}",
                "is_active": True,
                "priority": 0,
                "total_issued": 50,
                "total_value": 5000.0,
                "valid_from": "2024-01-01T00:00:00",
                "valid_until": "2024-12-31T23:59:59",
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="策略ID")
    campaign_id: int = Field(description="活动ID")
    name: str = Field(description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    strategy_type: str = Field(description="策略类型")
    trigger_config: str = Field(description="触发条件配置")
    trigger_events: Optional[str] = Field(default=None, description="触发事件列表")
    target_config: str = Field(description="目标对象配置")
    calculation_config: str = Field(description="奖励计算配置")
    constraint_config: Optional[str] = Field(default=None, description="限制条件配置")
    distribution_config: str = Field(description="分发配置")
    is_active: bool = Field(description="是否启用")
    priority: int = Field(description="优先级")
    total_issued: int = Field(description="总发放次数")
    total_value: float = Field(description="总发放价值")
    valid_from: Optional[datetime] = Field(default=None, description="生效时间")
    valid_until: Optional[datetime] = Field(default=None, description="失效时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class RewardStrategyListResponse(PaginatedResponse[RewardStrategyResponse]):
    """奖励策略列表响应模型"""
    pass

class RewardStrategyCreate(BaseModel):
    """奖励策略创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    strategy_type: str = Field(description="策略类型")
    trigger_config: str = Field(description="触发条件配置")
    trigger_events: Optional[str] = Field(default=None, description="触发事件列表")
    target_config: str = Field(description="目标对象配置")
    calculation_config: str = Field(description="奖励计算配置")
    constraint_config: Optional[str] = Field(default=None, description="限制条件配置")
    distribution_config: str = Field(description="分发配置")
    priority: int = Field(default=0, description="优先级")
    valid_from: Optional[datetime] = Field(default=None, description="生效时间")
    valid_until: Optional[datetime] = Field(default=None, description="失效时间")
    
    @field_validator('trigger_config', 'target_config', 'calculation_config', 'distribution_config')
    @classmethod
    def validate_json_config(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid JSON configuration')
        return v
    
    @field_validator('constraint_config')
    @classmethod
    def validate_constraint_config(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid constraint JSON configuration')
        return v
    
    @field_validator('trigger_events')
    @classmethod
    def validate_trigger_events(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid trigger events JSON configuration')
        return v

class RewardStrategyUpdate(BaseModel):
    """奖励策略更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: Optional[str] = Field(default=None, description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    strategy_type: Optional[str] = Field(default=None, description="策略类型")
    trigger_config: Optional[str] = Field(default=None, description="触发条件配置")
    trigger_events: Optional[str] = Field(default=None, description="触发事件列表")
    target_config: Optional[str] = Field(default=None, description="目标对象配置")
    calculation_config: Optional[str] = Field(default=None, description="奖励计算配置")
    constraint_config: Optional[str] = Field(default=None, description="限制条件配置")
    distribution_config: Optional[str] = Field(default=None, description="分发配置")
    priority: Optional[int] = Field(default=None, description="优先级")
    is_active: Optional[bool] = Field(default=None, description="是否启用")
    valid_from: Optional[datetime] = Field(default=None, description="生效时间")
    valid_until: Optional[datetime] = Field(default=None, description="失效时间")
    
    @field_validator('trigger_config', 'target_config', 'calculation_config', 'distribution_config')
    @classmethod
    def validate_json_config(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid JSON configuration')
        return v
    
    @field_validator('constraint_config')
    @classmethod
    def validate_constraint_config(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid constraint JSON configuration')
        return v
    
    @field_validator('trigger_events')
    @classmethod
    def validate_trigger_events(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid trigger events JSON configuration')
        return v

class GetRewardRecordsParams(BaseModel):
    """获取奖励记录列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'  # 忽略未定义的查询参数
    )

    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    campaign_id: Optional[int] = Field(default=None, description="活动ID过滤")
    user_id: Optional[int] = Field(default=None, description="用户ID过滤")
    reward_type: Optional[str] = Field(default=None, description="奖励类型过滤")
    status: Optional[str] = Field(default=None, description="记录状态过滤")
    strategy_id: Optional[int] = Field(default=None, description="奖励策略ID过滤")
    trigger_event: Optional[str] = Field(default=None, description="触发事件过滤")
    distribution_channel: Optional[str] = Field(default=None, description="分发渠道过滤")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_direction: Optional[str] = Field(default="desc", description="排序方向: asc/desc")


# === 批量操作Schema ===

class RewardRecordBatchUpdate(CamelCaseModel):
    """奖励记录批量更新模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    status: Optional[str] = Field(None, description="记录状态")
    distribution_status: Optional[str] = Field(None, description="分发状态")

class RewardRecordBatchUpdateRequest(BatchUpdateRequest[RewardRecordBatchUpdate]):
    """奖励记录批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {"status": "issued"}
            }
        }
    )

class RewardRecordBatchUpdateResponse(BatchUpdateResponse):
    """奖励记录批量更新响应模型"""
    pass

class RewardRecordBatchDeleteRequest(BatchDeleteRequest):
    """奖励记录批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )

class RewardRecordBatchDeleteResponse(BatchDeleteResponse):
    """奖励记录批量删除响应模型"""
    pass


# === 分发渠道Schema ===

class RewardDistributionChannelResponse(BaseModel):
    """奖励分发渠道响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "钱包渠道",
                "channel_type": "wallet",
                "description": "分发到用户钱包",
                "config": "{\"api_endpoint\": \"https://wallet-api.example.com\"}",
                "is_active": True,
                "priority": 1,
                "total_distributed": 100,
                "total_value": 10000.0,
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
        }
    )
    
    id: int = Field(description="渠道ID")
    name: str = Field(description="渠道名称")
    channel_type: str = Field(description="渠道类型")
    description: Optional[str] = Field(default=None, description="渠道描述")
    config: str = Field(description="渠道配置")
    is_active: bool = Field(description="是否启用")
    priority: int = Field(description="优先级")
    total_distributed: int = Field(description="总分发次数")
    total_value: float = Field(description="总分发价值")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class RewardDistributionChannelCreate(BaseModel):
    """奖励分发渠道创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="渠道名称")
    channel_type: str = Field(description="渠道类型")
    description: Optional[str] = Field(default=None, description="渠道描述")
    config: str = Field(description="渠道配置")
    is_active: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=0, description="优先级")
    
    @field_validator('config')
    @classmethod
    def validate_config(cls, v):
        try:
            json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError('Invalid channel configuration JSON')

class RewardDistributionChannelUpdate(BaseModel):
    """奖励分发渠道更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: Optional[str] = Field(default=None, description="渠道名称")
    channel_type: Optional[str] = Field(default=None, description="渠道类型")
    description: Optional[str] = Field(default=None, description="渠道描述")
    config: Optional[str] = Field(default=None, description="渠道配置")
    is_active: Optional[bool] = Field(default=None, description="是否启用")
    priority: Optional[int] = Field(default=None, description="优先级")
    
    @field_validator('config')
    @classmethod
    def validate_config(cls, v):
        if v is not None:
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                raise ValueError('Invalid channel configuration JSON')
        return v




