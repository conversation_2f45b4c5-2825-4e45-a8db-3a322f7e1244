import random
import string
import time
from typing import Any, Dict, Generic, List, Optional, TypeVar

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.marketing.models.campaign import AntiAbuseStrategy, Campaign
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.repositories.invitation import InvitationRepository
from svc.apps.marketing.schemas.invitation import (GetInvitationsParams,
                                                   InvitationListResponse,
                                                   InvitationResponse,
                                                   InvitationStatsResponse)
from svc.apps.marketing.services.campaign import CampaignService
# 使用新的事件接口
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 假设 RewardRecordService 存在
# from svc.apps.rewards.services import RewardRecordService # 确保导入路径正确

# 定义泛型类型变量
T = TypeVar('T')

# 缓存配置
CACHE_VERSION = "v1"  # 缓存版本号
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）

# 邀请码配置
CODE_LENGTH = 8  # 邀请码长度
CODE_CHARS = string.ascii_uppercase + string.digits  # 邀请码字符集
CODE_PREFIX = "INV"  # 邀请码前缀
MAX_RETRY = 3  # 生成邀请码最大重试次数

class InvitationService(BaseService, BatchOperationMixin):
    """
    邀请服务，负责生成、验证邀请码和处理邀请流程
    """
    resource_type = "邀请"
    
    def __init__(
        self,
        invitation_repo: InvitationRepository,
        user_service: Optional[Any] = None,
        campaign_service: Optional[CampaignService] = None,
        reward_record_service: Optional[Any] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化邀请服务

        Args:
            invitation_repo: 邀请仓库实例
            user_service: 用户服务实例（可选）
            campaign_service: 活动服务实例（可选）
            reward_record_service: 奖励记录服务实例（可选）
            redis: Redis客户端（可选）
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="invitation",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(invitation_repo, config, redis, **kwargs)

        self.invitation_repo = invitation_repo
        self.user_service = user_service
        self.campaign_service = campaign_service
        self.reward_record_service = reward_record_service
    
    def _get_invitation_cache_key(self, invitation_id: int) -> str:
        """获取邀请缓存键
        
        Args:
            invitation_id: 邀请ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:{invitation_id}"
    
    def _get_code_cache_key(self, code: str) -> str:
        """获取邀请码缓存键
        
        Args:
            code: 邀请码
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:code:{code}"
    
    def _get_user_invitations_cache_key(self, user_id: int, campaign_id: Optional[int] = None) -> str:
        """获取用户邀请列表缓存键
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            
        Returns:
            str: 缓存键
        """
        if campaign_id:
            return f"{self.resource_type}:{CACHE_VERSION}:user:{user_id}:campaign:{campaign_id}"
        return f"{self.resource_type}:{CACHE_VERSION}:user:{user_id}"
    
    def _get_cache_stats_key(self) -> str:
        """获取缓存统计信息的键
        
        Returns:
            str: 缓存统计键
        """
        return f"{self.resource_type}:{CACHE_VERSION}:stats"
    
    async def _increment_cache_stats(self, stat_type: str) -> None:
        """增加缓存统计计数
        
        Args:
            stat_type: 统计类型（hit/miss/error）
        """
        if not self.redis:
            return
            
        try:
            stats_key = self._get_cache_stats_key()
            await self.redis.hincrby(stats_key, stat_type, 1)
        except Exception as e:
            self.logger.warning(f"更新缓存统计失败: stat_type={stat_type}, 错误={str(e)}")
    

    
    async def _get_cached_invitation(self, invitation_id: int) -> Optional[Invitation]:
        """从缓存获取邀请信息
        
        Args:
            invitation_id: 邀请ID
            
        Returns:
            Optional[Invitation]: 缓存的邀请对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_invitation_cache_key(invitation_id)
            cached_data = await self.get_cached_resource(
                key,
                lambda data: Invitation.from_dict(data)
            )
            
            if cached_data: # 如果缓存命中
                await self._increment_cache_stats("hit")
                self.logger.debug(f"从缓存获取邀请成功: invitation_id={invitation_id}")
                return cached_data
            else:
                await self._increment_cache_stats("miss")
                return None
                
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"从缓存获取邀请失败: invitation_id={invitation_id}, 错误={str(e)}")
            return None
    
    async def _get_cached_invitation_by_code(self, code: str) -> Optional[Invitation]:
        """从缓存获取邀请码信息
        
        Args:
            code: 邀请码
            
        Returns:
            Optional[Invitation]: 缓存的邀请对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_code_cache_key(code)
            cached_data = await self.get_cached_resource(
                key,
                lambda data: Invitation.from_dict(data)
            )
            
            if cached_data:
                await self._increment_cache_stats("hit")
                self.logger.debug(f"从缓存获取邀请码成功: code={code}")
                return cached_data
            else:
                await self._increment_cache_stats("miss")
                return None
                
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"从缓存获取邀请码失败: code={code}, 错误={str(e)}")
            return None
    
    async def _delete_invitation_cache(self, invitation: Invitation) -> None:
        """
        删除邀请缓存
        
        Args:
            invitation: 需要删除缓存的邀请对象
        """
        if not self.redis:
            return
            
        try:
            id_cache_key = self._get_invitation_cache_key(invitation.id)
            code_cache_key = self._get_code_cache_key(invitation.code)
            
            await self.redis.delete(id_cache_key)
            await self.redis.delete(code_cache_key)
            
            self.logger.debug(f"已删除邀请缓存: {id_cache_key}, {code_cache_key}")
        except Exception as e:
            self.logger.error(f"删除邀请缓存失败: {str(e)}")
    
    async def get_resource_by_id(self, invitation_id: int) -> Optional[Invitation]:
        """
        获取指定ID的邀请
        
        Args:
            invitation_id: 邀请ID
            
        Returns:
            Optional[Invitation]: 邀请对象，不存在时返回None
        """
        # 先尝试从缓存获取
        self.logger.info(f"获取邀请 {invitation_id}")
        cached_invitation = await self._get_cached_invitation(invitation_id)
        if cached_invitation:
            return cached_invitation
            
        # 缓存未命中，从数据库获取
        invitation = await self.invitation_repo.get_by_id( invitation_id)
        
        # 如果找到邀请，则缓存它
        if invitation:
            await self.cache_resource(self._get_invitation_cache_key(invitation.id), invitation, CACHE_TTL)
            
        return invitation
    
    async def generate_invitation_code(self, user_id: int, campaign_id: int) -> Result:
        """
        生成邀请码。每个活动每个用户只能生成一个邀请码，重复请求返回已有邀请码。
        
        Args:
            user_id: 邀请人ID
            campaign_id: 活动ID
            
        Returns:
            Result[InvitationResponse]: 结果对象，包含生成的邀请码
        """
        self.logger.info(f"为用户 {user_id} 生成活动 {campaign_id} 的邀请码")
        
        try:
            # 先检查用户是否已经为该活动生成过邀请码
            existing_invitations, _ = await self.invitation_repo.get_paginated(
                page_num=1,
                page_size=1,
                inviter_id=user_id,
                campaign_id=campaign_id
            )
            
            # 如果已存在，则直接返回已有的邀请码
            if existing_invitations and len(existing_invitations) > 0:
                existing_invitation = existing_invitations[0]
                self.logger.info(f"用户 {user_id} 已为活动 {campaign_id} 生成过邀请码: {existing_invitation.code}")
                return self.create_success_result({
                    "code": existing_invitation.code,
                    "invitation_id": existing_invitation.id
                })
            
            # 检查活动资格
            campaign_result = await self.campaign_service.check_campaign_eligibility(campaign_id, user_id)
            if not campaign_result.is_success or not campaign_result.data.get("is_eligible"):
                reason = campaign_result.data.get("reason", "未知原因") if campaign_result.data else "活动检查失败"
                self.logger.warning(f"用户 {user_id} 无法生成邀请码: {reason}")
                return self.create_error_result(
                    error_code=ErrorCode.INVITATION_GENERATE_ERROR, 
                    error_message=f"无法生成邀请码: {reason}"
                )
                
            # 生成唯一邀请码
            code = await self._generate_unique_code()
            
            # 创建邀请记录
            invitation_data = {
                "campaign_id": campaign_id,
                "inviter_id": user_id,
                "code": code
            }
            invitation = await self.invitation_repo.create(invitation_data)
        
            dispatch(
                event_names.INVITATION_CREATED, # 使用常量
                invitation_id=invitation.id,
                inviter_id=user_id,
                campaign_id=campaign_id,
                code=code
            )
            
            self.logger.info(f"成功为用户 {user_id} 生成邀请码: {code}")
            return self.create_success_result({
                "code": code,
                "invitation_id": invitation.id
            })
        except Exception as e:
            self.logger.exception(f"生成邀请码失败: {str(e)}")
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.INVITATION_GENERATE_ERROR,
                error_message=f"生成邀请码失败: {str(e)}"
            )
    
    async def _generate_unique_code(self) -> str:
        """
        生成唯一邀请码
        
        Returns:
            str: 生成的邀请码
        """
        for _ in range(MAX_RETRY):
            # 生成随机码
            random_part = ''.join(random.choices(CODE_CHARS, k=CODE_LENGTH))
            code = f"{CODE_PREFIX}{random_part}"
            
            # 检查是否已存在
            existing = await self._get_cached_invitation_by_code(code)
            if not existing:
                existing = await self.invitation_repo.get_one(code=code)
                if not existing:
                    return code
        
        # 如果重试次数用完仍未生成唯一码，使用时间戳
        timestamp = int(time.time())
        return f"{CODE_PREFIX}{timestamp:x}"
    
    async def _check_anti_abuse(self, campaign: Campaign, ip: Optional[str], device_info: Optional[str]) -> Optional[str]:
        """执行防刷检查

        Args:
            campaign: 活动对象
            ip: IP地址
            device_info: 设备信息

        Returns:
            Optional[str]: 如果触发防刷规则，返回原因；否则返回 None
        """
        strategy = campaign.anti_abuse_strategy
        if strategy == AntiAbuseStrategy.NONE:
            return None

        ip_used = False
        device_used = False

        if strategy in [AntiAbuseStrategy.IP_LIMIT, AntiAbuseStrategy.BOTH] and ip:
            ip_used = await self.invitation_repo.check_ip_usage_for_campaign( campaign.id, ip)
            if ip_used:
                self.logger.warning(f"防刷触发 (IP): campaign={campaign.id}, ip={ip}")
                return "IP地址已参与过此活动"

        if strategy in [AntiAbuseStrategy.DEVICE_LIMIT, AntiAbuseStrategy.BOTH] and device_info:
            # 简单处理，忽略常见的通用 User-Agent 或空值
            if device_info and len(device_info) > 10 and 'bot' not in device_info.lower():
                device_used = await self.invitation_repo.check_device_usage_for_campaign( campaign.id, device_info)
                if device_used:
                    self.logger.warning(f"防刷触发 (设备): campaign={campaign.id}, device={device_info}")
                    return "设备已参与过此活动"
            else:
                 self.logger.debug(f"跳过设备检查 (信息不足或疑似爬虫): campaign={campaign.id}, device={device_info}")


        return None # 未触发防刷

    async def validate_invitation_code(self, code: str, ip: str = None, device_info: str = None) -> Optional[Dict[str, Any]]:
        """
        验证邀请码，包含防刷检查。邀请码在活动到期前不会失效，可重复使用。
        
        Args:
            code: 邀请码
            ip: 验证请求的IP地址
            device_info: 验证请求的设备信息 (User-Agent)
            
        Returns:
            Optional[Dict[str, Any]]: 验证结果字典，包含 valid (bool), reason (str), invitation, campaign, inviter 等信息。
                                     如果邀请码不存在返回 None。
        """
        self.logger.info(f"验证邀请码: {code}, IP: {ip}, Device: {device_info}")
        
        # 尝试从缓存获取邀请信息
        invitation = await self._get_cached_invitation_by_code(code)
        if not invitation:
            # 缓存未命中，从数据库获取
            invitation = await self.invitation_repo.get_one(code=code)
            if not invitation:
                self.logger.warning(f"邀请码不存在: {code}")
                return None # Code not found directly returns None
            # 缓存数据库查找到的邀请信息
            await self.cache_resource(self._get_invitation_cache_key(invitation.id), invitation, CACHE_TTL)
        
        # 获取关联的活动信息
        # 确保 CampaignService 实例可用
        if not self.campaign_service:
             self.logger.error("CampaignService 未初始化，无法执行防刷检查")
             # 或者抛出异常，或者返回验证失败
             return {"valid": False, "reason": "内部服务错误，无法验证活动信息"}
             
        campaign_result = await self.campaign_service.get_campaign(invitation.campaign_id)
        if not campaign_result.is_success or not campaign_result.data:
            self.logger.error(f"无法获取邀请码 {code} 关联的活动 {invitation.campaign_id}")
            return {"valid": False, "reason": "关联活动无效"}
            
        campaign = campaign_result.data
        # if isinstance(campaign_result.data, dict):
        #     campaign = Campaign(**campaign_result.data) # 示例
        # else:
        #     campaign = campaign_result.data
        
        # 检查活动状态和有效期
        # 只在活动结束时失效，不限制使用次数
        now = get_utc_now_without_tzinfo()
        if campaign.status != "active" or \
           (hasattr(campaign, 'start_date') and campaign.start_date and now < campaign.start_date) or \
           (hasattr(campaign, 'end_date') and campaign.end_date and now > campaign.end_date):
            self.logger.info(f"邀请码验证失败: 活动 {campaign.id} 无效或不在有效期内")
            return {"valid": False, "reason": "活动无效或已结束", "error_code": "CAMPAIGN_INVALID"}

        # --- 执行防刷检查 --- (IP和设备限制，根据活动设置)
        anti_abuse_reason = await self._check_anti_abuse(campaign, ip, device_info)
        if anti_abuse_reason:
            return {"valid": False, "reason": anti_abuse_reason, "error_code": "ANTI_ABUSE_CHECK_FAILED"}
        # --- 防刷检查结束 ---
        
        inviter = None
        # 获取邀请人信息
        if self.user_service:
            inviter_result = await self.user_service.get_user(invitation.inviter_id)
            if inviter_result.is_success and inviter_result.data:
                inviter = inviter_result.data # 假设返回 User 模型或兼容字典
                # if isinstance(inviter_result.data, dict):
                #     inviter = User(**inviter_result.data) # 示例转换
                # else:
                #     inviter = inviter_result.data
        
        # 增加邀请码打开次数 (异步执行，不阻塞主流程)
        # asyncio.create_task(self.invitation_repo.increment_field(invitation.id, "opened_count"))
        # 或者直接调用，如果仓库是异步的
        await self.invitation_repo.increment_field(invitation.id, "opened_count")

        self.logger.info(f"邀请码验证成功: {code}")
        return {
            "valid": True,
            "invitation": invitation,
            "campaign": campaign,
            "inviter": inviter
        }

    async def complete_invitation(self, code: str, invitee_id: int, ip: str = None, device_info: str = None) -> Result[InvitationResponse]:
        """
        完成邀请流程，包含防刷检查和触发奖励。邀请码在活动到期前可重复使用。
        
        Args:
            code: 邀请码
            invitee_id: 被邀请人用户ID
            ip: 被邀请人的IP地址
            device_info: 被邀请人的设备信息 (User-Agent)
            
        Returns:
            Result[InvitationResponse]: 操作结果
        """
        self.logger.info(f"尝试完成邀请: code={code}, invitee_id={invitee_id}, IP={ip}, Device={device_info}")
        
        # 1. 验证邀请码基础信息 (复用 validate 但不再次检查 IP/设备是否已使用)
        #    或者直接查询数据库获取邀请和活动信息
        #    这里选择直接查询以避免重复逻辑和潜在的重复打开计数
        invitation = await self.invitation_repo.get_one(code=code)
        if not invitation:
            self.logger.warning(f"完成邀请失败: 邀请码 {code} 不存在")
            return ResultFactory.resource_not_found(self.resource_type, code)
        
        # 检查是否自邀请
        if invitation.inviter_id == invitee_id:
            self.logger.warning(f"完成邀请失败: 用户 {invitee_id} 尝试使用自己的邀请码 {code}")
            return ResultFactory.validation_error("不能使用自己的邀请码")

        # 获取关联的活动信息
        if not self.campaign_service:
             self.logger.error("CampaignService 未初始化，无法执行防刷检查")
             return ResultFactory.error("内部服务错误")
             
        campaign_result = await self.campaign_service.get_campaign(invitation.campaign_id)
        if not campaign_result.is_success or not campaign_result.data:
            self.logger.error(f"无法获取邀请码 {code} 关联的活动 {invitation.campaign_id}")
            return ResultFactory.error("关联活动无效")
        campaign = campaign_result.data

        # 检查活动状态和有效期
        now = get_utc_now_without_tzinfo()
        # 使用 start_date 和 end_date 进行比较，直接比较 datetime 对象
        if campaign.status != "active" or \
           (hasattr(campaign, 'start_date') and campaign.start_date and now < campaign.start_date) or \
           (hasattr(campaign, 'end_date') and campaign.end_date and now > campaign.end_date):
            self.logger.info(f"完成邀请失败: 活动 {campaign.id} 无效或不在有效期内")
            return ResultFactory.validation_error("活动无效或已结束")

        # --- 执行防刷检查 --- (完成邀请时再次检查，确保状态最新)
        anti_abuse_reason = await self._check_anti_abuse(campaign, ip, device_info)
        if anti_abuse_reason:
            return ResultFactory.validation_error(anti_abuse_reason)
        # --- 防刷检查结束 ---

        # 2. 记录本次邀请使用情况，但不标记邀请码为已使用
        usage_recorded = await self.invitation_repo.record_invitation_usage(
            
            invitation.id,
            invitee_id,
            ip,
            device_info
        )
        
        if not usage_recorded:
            self.logger.error(f"记录邀请 {invitation.id} 使用情况失败")
            # 这里可以选择不中断流程，因为邀请还是有效的，只是使用记录失败
            # 或者也可以选择返回错误
            # return ResultFactory.error("记录邀请使用情况失败")
            
        # 3. 触发奖励计算 (通过事件或直接调用)
        #    这里假设通过触发 arq 任务
        self.logger.info(f"邀请 {invitation.id} 被用户 {invitee_id} 使用，准备触发邀请完成任务")
        # arq_client = await get_arq_redis()
        dispatch(
            event_names.INVITATION_COMPLETED, # 使用常量
            invitation_id=invitation.id,
            campaign_id=invitation.campaign_id,
            inviter_id=invitation.inviter_id,
            invitee_id=invitee_id
        )
        
        # 提交事务
        await self.db.commit()
        
        return ResultFactory.success(data={
            "invitation_id": invitation.id,
            "inviter_id": invitation.inviter_id,
            "code": invitation.code,
            "campaign_id": invitation.campaign_id
        })
    
    async def get_user_invitations(
        self, 
        user_id: int, 
        campaign_id: Optional[int] = None,
        page_num: int = 1,
        page_size: int = 20
    ) -> Result[InvitationListResponse]:
        """
        获取用户的邀请列表
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            Result[InvitationListResponse]: 包含分页信息的邀请列表结果
        """
        try:
            self.logger.info(f"获取用户 {user_id} 的邀请列表: page={page_num}, size={page_size}, campaign_id={campaign_id}")
            
            # 直接从数据库获取
            invitations, total = await self.invitation_repo.get_user_invitations(
                
                user_id=user_id,
                campaign_id=campaign_id,
                page_num=page_num,
                page_size=page_size
            )
            
            # 构建响应列表
            invitation_responses = [InvitationResponse.model_validate(inv.to_dict()) for inv in invitations]
            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = InvitationListResponse(
                items=invitation_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
                
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.exception(f"获取用户邀请列表失败: user_id={user_id}, 错误={str(e)}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取用户邀请列表失败: {str(e)}"
            )
    
    async def get_invitation_stats(self, user_id: int, campaign_id: Optional[int] = None) -> Result[InvitationStatsResponse]:
        """获取邀请统计信息
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            
        Returns:
            Result[InvitationStatsResponse]: 结果对象，包含统计信息
        """
        self.logger.info(f"获取邀请统计: user_id={user_id}, campaign_id={campaign_id}")
        
        try:
            # 获取邀请记录
            result = await self.get_user_invitations(user_id, campaign_id)
            if not result.is_success:
                return result
                
            invitations = result.data
            
            # 计算统计数据
            total_count = invitations.total
            completed_count = sum(1 for inv in invitations.items if inv.is_used)
            opened_count = sum(inv.opened_count for inv in invitations.items)
            
            # 计算待处理邀请数
            pending_count = total_count - completed_count
            
            # 计算完成率 (转化率)
            completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0
            
            # 构建符合 InvitationStatsResponse 的统计信息
            stats_data = {
                "total_invitations": total_count,
                "successful_invitations": completed_count,
                "pending_invitations": pending_count,
                "total_opened": opened_count,
                "conversion_rate": round(completion_rate, 2) 
            }
            
            self.logger.info(f"获取邀请统计成功: user_id={user_id}, stats={stats_data}")
            # 使用 Pydantic 模型进行验证和返回
            stats_response = InvitationStatsResponse.model_validate(stats_data)
            return self.create_success_result(stats_response)
            
        except Exception as e:
            self.logger.error(f"获取邀请统计失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INVITATION_NOT_FOUND,
                error_message=f"获取邀请统计失败: {str(e)}"
            )
            
    async def warm_up_cache(self, campaign_id: Optional[int] = None) -> None:
        """预热缓存
        
        Args:
            campaign_id: 活动ID（可选），如果提供则只预热指定活动的邀请
        """
        if not self.redis:
            return
            
        try:
            self.logger.info(f"开始预热邀请缓存: campaign_id={campaign_id}")
            
            # 获取最近的邀请记录
            invitations, total = await self.invitation_repo.get_paginated(
                page_num=1,
                page_size=100, 
                order_by="created_at", 
                direction="desc", 
                **({"campaign_id": campaign_id} if campaign_id else {})
            )
            
            # 批量缓存
            for invitation in invitations:
                await self.cache_resource(self._get_invitation_cache_key(invitation.id), invitation, CACHE_TTL)
                
            self.logger.info(f"邀请缓存预热完成: count={len(invitations)}")
            
        except Exception as e:
            self.logger.error(f"邀请缓存预热失败: campaign_id={campaign_id}, 错误={str(e)}", exc_info=True)
            
    async def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, int]: 缓存统计数据
        """
        if not self.redis:
            return {"hit": 0, "miss": 0, "error": 0}
            
        try:
            stats_key = self._get_cache_stats_key()
            stats = await self.redis.hgetall(stats_key)
            return {
                "hit": int(stats.get(b"hit", 0)),
                "miss": int(stats.get(b"miss", 0)), 
                "error": int(stats.get(b"error", 0))
            }
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: 错误={str(e)}", exc_info=True)
            return {"hit": 0, "miss": 0, "error": 0}

    async def get_invitations(self, params: GetInvitationsParams) -> Result[InvitationListResponse]:
        """获取邀请列表（通用）

        Args:
            params: 查询参数对象，包含分页和过滤条件

        Returns:
            Result[InvitationListResponse]: 包含分页信息的邀请列表结果
        """
        try:
            page_num = params.page_num
            page_size = params.page_size
            self.logger.info(f"获取邀请列表: page={page_num}, size={page_size}, filters={params.model_dump(exclude={'page_num', 'page_size'})}")

            filters = {}
            if params.campaign_id:
                filters["campaign_id"] = params.campaign_id
            if params.inviter_id:
                filters["inviter_id"] = params.inviter_id
            if params.is_used is not None:
                filters["is_used"] = params.is_used
            
            invitations, total = await self.invitation_repo.get_paginated(
                filters=filters,
                page_num=page_num,
                page_size=page_size
            )

            invitation_responses = [InvitationResponse.model_validate(inv.to_dict()) for inv in invitations]

            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0

            paginated_response = InvitationListResponse(
                items=invitation_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )

            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取邀请列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取邀请列表失败: {str(e)}"
            )

    async def process_new_invitation_relationship(
        self,
        inviter_id: int,
        invitee_id: int,
        client_ip: Optional[str] = None,
        client_device: Optional[str] = None,
        campaign_id: Optional[int] = None
    ) -> Result[InvitationResponse]:
        """处理新的邀请关系

        Args:
            inviter_id: 邀请人ID
            invitee_id: 被邀请人ID
            client_ip: 客户端IP地址
            client_device: 客户端设备信息

        Returns:
            Result[InvitationResponse]: 结果对象，包含处理后的邀请关系
        """
        try:
            # 检查邀请人是否存在
            inviter = await self.user_service.get_resource_by_id(inviter_id)
            if not inviter:
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message=f"邀请人ID {inviter_id} 不存在"
                )
            

            # 检查被邀请人是否存在
            invitee = await self.user_service.get_resource_by_id(invitee_id)
            if not invitee:
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message=f"被邀请人ID {invitee_id} 不存在"
                )
            # 创建邀请记录
            invitation_data = {
                "campaign_id": campaign_id,
                "inviter_id": inviter_id,
                "invitee_id": invitee_id,
                "code": f"UID{inviter_id}",
                "invitee_ip": client_ip,
                "invitee_device": client_device
            }
            invitation = await self.invitation_repo.create(invitation_data)

            payload={
                "invitation_id":invitation.id,
                "campaign_id":invitation.campaign_id,
                "inviter_id":invitation.inviter_id,
                "invitee_id":invitation.invitee_id
            }

            dispatch(
                event_names.MARKETING_INVITATION_COMPLETED, 
                payload=payload
            )
            
            # 返回成功结果
            return self.create_success_result(InvitationResponse.model_validate(invitation.to_dict()))

        except Exception as e:
            self.logger.error(f"处理新的邀请关系失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"处理新的邀请关系失败: {str(e)}"
            )
