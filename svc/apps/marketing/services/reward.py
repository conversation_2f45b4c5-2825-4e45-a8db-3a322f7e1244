# 标准库导入
import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, TypeVar

# 第三方库导入
from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.marketing.models.campaign import Campaign
from svc.apps.marketing.models.invitation import Invitation
# 项目内模块导入
from svc.apps.marketing.models.reward import (RewardDistributionChannel,
                                              RewardRecord, RewardStrategy,
                                              RewardType)
from svc.apps.marketing.repositories.invitation import InvitationRepository
from svc.apps.marketing.repositories.reward import (
    RewardDistributionChannelRepository, RewardRecordRepository,
    RewardStrategyRepository)
from svc.apps.marketing.schemas.reward import (
    GetRewardRecordsParams, RewardDistributionChannelCreate,
    RewardDistributionChannelResponse, RewardDistributionChannelUpdate,
    RewardRecordCreate, RewardRecordListResponse, RewardRecordResponse,
    RewardStatsResponse, RewardStrategyCreate, RewardStrategyListResponse,
    RewardStrategyResponse, RewardStrategyUpdate)
from svc.apps.marketing.services.campaign import CampaignService
from svc.core.database.exceptions import (DatabaseError,
                                          ForeignKeyConstraintError,
                                          RecordNotFoundError,
                                          UniqueConstraintError,
                                          create_user_friendly_message,
                                          is_retryable_error,
                                          is_user_input_error,
                                          map_sqlalchemy_exception)
from svc.core.events.event_names import (MARKETING_REWARD_ISSUED,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result, ResultFactory
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 定义泛型类型变量
T = TypeVar('T')

CACHE_TTL = 3600  # 1小时缓存过期时间

class RewardStrategyService(BaseService, BatchOperationMixin):
    """
    奖励策略服务，负责奖励策略的创建、查询和管理

    提供奖励策略的完整生命周期管理，包括创建、更新、删除、查询等功能。
    支持配置驱动的奖励策略，通过JSON配置实现灵活的奖励规则。
    """

    def __init__(
        self,
        reward_strategy_repo: RewardStrategyRepository,
        campaign_service: Optional[CampaignService] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """
        初始化奖励策略服务

        Args:
            reward_strategy_repo: 奖励策略仓库实例
            campaign_service: 活动服务实例，用于验证活动存在性
            redis: Redis客户端，用于缓存操作
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="reward_strategy",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(reward_strategy_repo, config, redis, **kwargs)

        self.reward_strategy_repo = reward_strategy_repo
        self.campaign_service = campaign_service

    def _get_strategy_cache_key(self, strategy_id: int) -> str:
        """
        获取策略缓存键
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            str: 缓存键字符串
        """
        return f"reward_strategy:{strategy_id}"

    async def get_resource_by_id(self, resource_id: int) -> Optional[RewardStrategy]:
        """
        根据ID获取奖励策略资源
        
        Args:
            resource_id: 策略ID
            
        Returns:
            Optional[RewardStrategy]: 找到的策略对象，未找到时返回None
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            return await self.reward_strategy_repo.get_by_id(resource_id)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"获取奖励策略失败: {db_error.message}")
            return None
    
    async def update_by_id(self, strategy_id: int, update_data: Dict[str, Any]) -> Optional[RewardStrategy]:
        """
        根据ID更新奖励策略
        
        Args:
            strategy_id: 策略ID
            update_data: 更新数据字典
            
        Returns:
            Optional[RewardStrategy]: 更新后的策略对象，更新失败时返回None
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            return await self.reward_strategy_repo.update_by_id(strategy_id, update_data)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"更新奖励策略失败: {db_error.message}")
            return None
    
    def resource_not_found_result(self, resource_id: int, resource_type: str = "RewardStrategy") -> Result:
        """
        创建资源未找到的错误结果
        
        Args:
            resource_id: 资源ID
            resource_type: 资源类型名称
            
        Returns:
            Result: 包含错误信息的结果对象
        """
        return self.create_error_result(
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            error_message=f"{resource_type} with ID {resource_id} not found"
        )
    
    def create_error_result(self, error_code: str, error_message: str) -> Result:
        """
        创建错误结果对象
        
        Args:
            error_code: 错误代码
            error_message: 错误消息
            
        Returns:
            Result: 包含错误信息的结果对象
        """
        return Result.error(error_code, error_message)
    
    async def get_campaign_strategies(
        self, 
        campaign_id: int,
        *,
        for_inviter: Optional[bool] = None,
        for_invitee: Optional[bool] = None,
        page_num: int = 1,
        page_size: int = 100
    ) -> Result[RewardStrategyResponse]:
        """
        获取活动的所有奖励策略
        
        Args:
            campaign_id: 活动ID
            for_inviter: 是否筛选邀请人的策略
            for_invitee: 是否筛选被邀请人的策略
            page_num: 页码，从1开始
            page_size: 每页数量
            
        Returns:
            Result: 包含策略列表和分页信息的响应结果
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            self.logger.info(f"获取活动 {campaign_id} 的奖励策略: page={page_num}, size={page_size}, 过滤条件: for_inviter={for_inviter}, for_invitee={for_invitee}")
            
            filters = {"campaign_id": campaign_id}
            if for_inviter is not None:
                filters["for_inviter"] = for_inviter
            if for_invitee is not None:
                filters["for_invitee"] = for_invitee
            
            strategies, total = await self.reward_strategy_repo.get_paginated(
                filters=filters,
                page_num=page_num,
                page_size=page_size
            )
            
            # 缓存所有策略
            for strategy in strategies:
                cache_key = self._get_strategy_cache_key(strategy.id)
                await self.cache_resource(cache_key, strategy, CACHE_TTL)
            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            strategy_responses = [RewardStrategyResponse.model_validate(strategy) for strategy in strategies]
            paginated_response = {
                "items": strategy_responses,
                "total": total,
                "page": page_num,
                "size": page_size,
                "pages": total_pages
            }
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"获取活动 {campaign_id} 的奖励策略失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_strategy(self, strategy_id: int) -> Optional[RewardStrategy]:
        """
        获取指定ID的奖励策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Optional[RewardStrategy]: 策略对象，不存在时返回None
        """
        self.logger.info(f"获取奖励策略 {strategy_id}")
        return await self.get_resource_by_id(strategy_id)
    
    async def create_strategy(
        self,
        campaign_id: int,
        name: str,
        description: str,
        strategy_type: str,
        trigger_config: str,
        target_config: str,
        calculation_config: str,
        distribution_config: str,
        constraint_config: Optional[str] = None,
        priority: int = 0,
        trigger_events: Optional[str] = None
    ) -> Result[RewardStrategyResponse]:
        """
        创建奖励策略
        
        Args:
            campaign_id: 活动ID
            name: 策略名称
            description: 策略描述
            strategy_type: 策略类型
            trigger_config: 触发条件配置
            target_config: 目标对象配置
            calculation_config: 奖励计算配置
            distribution_config: 分发配置
            constraint_config: 限制条件配置
            priority: 优先级
            trigger_events: 触发事件列表
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，包含创建的策略
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        # 验证活动是否存在
        self.logger.info(f"创建活动 {campaign_id} 的奖励策略: {name}")
        
        try:
            campaign = await self.campaign_service.get_campaign(campaign_id)
            if not campaign:
                self.logger.warning(f"活动 {campaign_id} 不存在")
                return self.create_error_result(
                    error_code=ErrorCode.CAMPAIGN_NOT_FOUND,
                    error_message="活动不存在"
                )
            
            # 验证配置格式
            validation_result = await self.validate_strategy_config(
                trigger_config, target_config, calculation_config, distribution_config, constraint_config
            )
            if not validation_result.is_success:
                return validation_result
            
            # 创建策略
            strategy_data = {
                "campaign_id": campaign_id,
                "name": name,
                "description": description,
                "strategy_type": strategy_type,
                "trigger_config": trigger_config,
                "target_config": target_config,
                "calculation_config": calculation_config,
                "distribution_config": distribution_config,
                "constraint_config": constraint_config,
                "priority": priority,
                "trigger_events": trigger_events
            }
            strategy = await self.reward_strategy_repo.create(strategy_data)
            
            # 提交事务 - 事务管理应由装饰器统一处理
            await self.reward_strategy_repo.db.commit()
            await self.reward_strategy_repo.db.refresh(strategy)
            
            # 发送事件
            event_data = { 
                "strategy_id": strategy.id,
                "campaign_id": campaign_id,
                "name": name,
                "strategy_type": strategy_type
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("created_by"), # Assuming created_by in event_data or context
                 "action": "reward_strategy_created",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy.id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy.id})
            
            self.logger.info(f"成功创建奖励策略 {strategy.id}")
            return self.create_success_result(strategy)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"创建奖励策略失败: {db_error.message}")
            # 事务回滚应由装饰器统一处理
            await self.reward_strategy_repo.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def validate_strategy_config(
        self, 
        trigger_config: str, 
        target_config: str, 
        calculation_config: str, 
        distribution_config: str,
        constraint_config: Optional[str] = None
    ) -> Result[bool]:
        """
        验证策略配置格式
        
        Args:
            trigger_config: 触发条件配置JSON字符串
            target_config: 目标对象配置JSON字符串
            calculation_config: 奖励计算配置JSON字符串
            distribution_config: 分发配置JSON字符串
            constraint_config: 限制条件配置JSON字符串（可选）
            
        Returns:
            Result[bool]: 验证结果，成功时返回True
            
        Raises:
            ValueError: 配置格式错误时抛出
        """
        try:
            # 验证JSON格式
            json.loads(trigger_config)
            json.loads(target_config)
            json.loads(calculation_config)
            json.loads(distribution_config)
            
            if constraint_config:
                json.loads(constraint_config)
            
            return self.create_success_result(True)
        except json.JSONDecodeError as e:
            return self.create_error_result(
                error_code=ErrorCode.VALIDATION_ERROR,
                error_message=f"配置格式错误: {str(e)}"
            )
        except ValueError as e:
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"配置验证失败: {str(e)}"
            )
    
    async def get_applicable_strategies(
        self, 
        campaign_id: int, 
        trigger_event: Optional[str] = None
    ) -> Result[List[RewardStrategy]]:
        """
        获取适用的奖励策略
        
        根据活动ID和触发事件获取所有适用的奖励策略，并按优先级排序。
        
        Args:
            campaign_id: 活动ID
            trigger_event: 触发事件，可选
            
        Returns:
            Result[List[RewardStrategy]]: 包含适用策略列表的结果对象
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            filters = {"campaign_id": campaign_id, "is_active": True}
            if trigger_event:
                filters["trigger_events__contains"] = trigger_event
            
            strategies, _ = await self.reward_strategy_repo.get_paginated(filters=filters, page_size=1000)
            
            # 过滤有效策略
            valid_strategies = [strategy for strategy in strategies if strategy.is_valid()]
            return self.create_success_result(valid_strategies)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_strategies_by_event(
        self, 
        campaign_id: int, 
        trigger_event: str
    ) -> Result[List[RewardStrategy]]:
        """
        根据触发事件获取策略
        
        Args:
            campaign_id: 活动ID
            trigger_event: 触发事件名称
            
        Returns:
            Result[List[RewardStrategy]]: 包含策略列表的结果对象
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            filters = {"campaign_id": campaign_id, "is_active": True}
            if trigger_event:
                filters["trigger_events__contains"] = trigger_event
            
            strategies, _ = await self.reward_strategy_repo.get_paginated(filters=filters, page_size=1000)
            
            # 过滤包含指定事件的策略
            event_strategies = []
            for strategy in strategies:
                trigger_events = strategy.get_trigger_config().get('events', [])
                if trigger_event in trigger_events:
                    event_strategies.append(strategy)
            
            return self.create_success_result(event_strategies)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def update_strategy_priority(
        self, 
        strategy_id: int, 
        priority: int
    ) -> Result[RewardStrategyResponse]:
        """
        更新策略优先级
        
        Args:
            strategy_id: 策略ID
            priority: 新的优先级值
            
        Returns:
            Result[RewardStrategyResponse]: 包含更新后策略的结果对象
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            strategy = await self.get_resource_by_id(strategy_id)
            if not strategy:
                return self.resource_not_found_result(strategy_id)
            
            strategy = await self.reward_strategy_repo.update_by_id(
                strategy_id, {"priority": priority}
            )
            
            return self.create_success_result(strategy)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_campaign_strategies(
        self,
        campaign_id: int,
        strategy_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        page_num: int = 1,
        page_size: int = 100
    ) -> Result[Dict[str, Any]]:
        """
        获取活动的奖励策略列表（分页）
        
        Args:
            campaign_id: 活动ID
            strategy_type: 策略类型过滤
            is_active: 是否启用过滤
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            Result: 包含策略列表和分页信息的结果对象
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            filters = {"campaign_id": campaign_id}
            if strategy_type:
                filters["strategy_type"] = strategy_type
            if is_active is not None:
                filters["is_active"] = is_active
            
            strategies, total = await self.reward_strategy_repo.get_paginated(
                filters=filters,
                page_num=page_num,
                page_size=page_size
            )
            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建响应
            strategy_responses = [RewardStrategyResponse.model_validate(strategy) for strategy in strategies]
            return self.create_success_result({
                "items": strategy_responses,
                "total": total,
                "page_num": page_num,
                "page_size": page_size,
                "page_count": total_pages
            })
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def update_strategy(
        self,
        strategy_id: int,
        name: Optional[str] = None,
        description: Optional[str] = None,
        strategy_type: Optional[str] = None,
        trigger_config: Optional[str] = None,
        target_config: Optional[str] = None,
        calculation_config: Optional[str] = None,
        constraint_config: Optional[str] = None,
        distribution_config: Optional[str] = None,
        priority: Optional[int] = None,
        is_active: Optional[bool] = None,
        trigger_events: Optional[str] = None
    ) -> Result[RewardStrategyResponse]:
        """
        更新奖励策略
        
        Args:
            strategy_id: 策略ID
            name: 策略名称（可选）
            description: 策略描述（可选）
            strategy_type: 策略类型（可选）
            trigger_config: 触发条件配置（可选）
            target_config: 目标对象配置（可选）
            calculation_config: 奖励计算配置（可选）
            constraint_config: 限制条件配置（可选）
            distribution_config: 分发配置（可选）
            priority: 优先级（可选）
            is_active: 是否启用（可选）
            trigger_events: 触发事件列表（可选）
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，包含更新后的策略
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        self.logger.info(f"更新奖励策略 {strategy_id}")
        
        try:
            strategy = await self.get_resource_by_id(strategy_id)
            if not strategy:
                self.logger.warning(f"奖励策略 {strategy_id} 不存在")
                return self.resource_not_found_result(strategy_id)
            
            # 构建更新数据
            update_data = {}
            if name is not None:
                update_data["name"] = name
            if description is not None:
                update_data["description"] = description
            if strategy_type is not None:
                update_data["strategy_type"] = strategy_type
            if trigger_config is not None:
                update_data["trigger_config"] = trigger_config
            if target_config is not None:
                update_data["target_config"] = target_config
            if calculation_config is not None:
                update_data["calculation_config"] = calculation_config
            if constraint_config is not None:
                update_data["constraint_config"] = constraint_config
            if distribution_config is not None:
                update_data["distribution_config"] = distribution_config
            if priority is not None:
                update_data["priority"] = priority
            if is_active is not None:
                update_data["is_active"] = is_active
            if trigger_events is not None:
                update_data["trigger_events"] = trigger_events
            
            if not update_data:
                return self.create_success_result(strategy)
            
            # 更新策略
            updated_strategy = await self.update_by_id(strategy_id, update_data)
            
            # 发送事件
            event_data = {
                "strategy_id": strategy_id,
                "updated_fields": list(update_data.keys()),
                "strategy_name": updated_strategy.name if updated_strategy else strategy.name,
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("updated_by"),
                 "action": "reward_strategy_updated",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy_id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy_id})
            
            self.logger.info(f"成功更新奖励策略 {strategy_id}")
            return self.create_success_result(updated_strategy)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"更新奖励策略失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def delete_strategy(self, strategy_id: int) -> Result[RewardStrategyResponse]:
        """
        删除奖励策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Result[RewardStrategyResponse]: 结果对象，包含删除的策略信息
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        self.logger.info(f"删除奖励策略 {strategy_id}")
        
        try:
            strategy = await self.get_resource_by_id(strategy_id)
            if not strategy:
                self.logger.warning(f"奖励策略 {strategy_id} 不存在")
                return self.resource_not_found_result(strategy_id)
            
            # 删除策略
            await self.reward_strategy_repo.delete_by_id(strategy_id)
            
            # 发送事件
            event_data = {
                "strategy_id": strategy_id,
                "strategy_name": strategy.name,
                "campaign_id": strategy.campaign_id
            }
            dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
                 "user_id": event_data.get("deleted_by"),
                 "action": "reward_strategy_deleted",
                 "resource_type": "reward_strategy",
                 "resource_id": strategy_id,
                 "metadata": event_data
            })
            
            dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={"resource_type": "reward_strategy", "resource_id": strategy_id})
            
            self.logger.info(f"成功删除奖励策略 {strategy_id}")
            return self.create_success_result(strategy)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"删除奖励策略失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )

class RewardRecordService(BaseService, BatchOperationMixin):
    """
    奖励记录服务，负责计算、发放和管理奖励记录
    """
    resource_type = "奖励记录"

    def __init__(
        self,
        reward_record_repo: RewardRecordRepository,
        user_service: Optional[Any] = None,
        strategy_service: Optional[RewardStrategyService] = None,
        campaign_service: Optional[CampaignService] = None,
        invitation_repo: Optional[InvitationRepository] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化奖励记录服务

        Args:
            reward_record_repo: 奖励记录仓库实例
            user_service: 用户服务实例（可选）
            strategy_service: 奖励策略服务实例（可选）
            campaign_service: 活动服务实例（可选）
            invitation_repo: 邀请仓库实例（可选）
            redis: Redis客户端（可选）
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="reward_record",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(reward_record_repo, config, redis, **kwargs)

        self.reward_record_repo = reward_record_repo
        self.user_service = user_service
        self.strategy_service = strategy_service
        self.campaign_service = campaign_service
        self.invitation_repo = invitation_repo

    


    async def _get_cached_record(self, record_id: int) -> Optional[RewardRecord]:
        """
        从缓存获取奖励记录
        
        Args:
            record_id: 奖励记录ID
            
        Returns:
            Optional[RewardRecord]: 缓存的奖励记录对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            cache_key = f"reward_record:{record_id}"
            cached_data = await self.redis.get(cache_key)
            
            if cached_data:
                self.logger.debug(f"从缓存获取奖励记录: {cache_key}")
                record_data = json.loads(cached_data)
                return RewardRecord.from_dict(record_data)
        except Exception as e:
            self.logger.error(f"从缓存获取奖励记录失败: {str(e)}")
            
        return None
    
    async def get_resource_by_id(self, resource_id: int) -> Optional[RewardRecord]:
        """根据ID获取资源"""
        try:
            return await self.reward_record_repo.get_by_id(resource_id)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"获取奖励记录失败: {db_error.message}")
            return None

    async def update_by_id(self, record_id: int, update_data: Dict[str, Any]) -> Optional[RewardRecord]:
        """根据ID更新记录"""
        try:
            return await self.reward_record_repo.update_by_id(record_id, update_data)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"更新奖励记录失败: {db_error.message}")
            return None
    
    def resource_not_found_result(self, resource_id: int) -> Result:
        """创建资源不存在的结果"""
        return self.create_error_result(
            ErrorCode.RESOURCE_NOT_FOUND,
            f"奖励记录不存在: ID={resource_id}"
        )
    
    async def clear_resource_cache(self, resource_id: int) -> None:
        """清除资源缓存"""
        if self.redis:
            cache_key = f"reward_record:{resource_id}"
            await self.redis.delete(cache_key)
    
    def create_error_result(self, error_code: str, error_message: str) -> Result:
        """创建错误结果"""
        return Result.error(error_code, error_message)

    async def _calculate_reward_amount(
        self,
        strategy: RewardStrategy,
        context: Dict[str, Any]
    ) -> Optional[float]:
        """
        计算单个策略的奖励金额
        
        Args:
            strategy: 奖励策略对象
            context: 触发上下文信息
            
        Returns:
            Optional[float]: 计算出的奖励金额，计算失败时返回None
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            # 调用策略的计算方法
            reward_amount = strategy.calculate_reward(context)
            return reward_amount
        except Exception as e:
            self.logger.error(
                f"计算策略 {strategy.id} 奖励失败: error={str(e)}", exc_info=True
            )
            return None

    async def _create_reward_record(
        self,
        user_id: int,
        strategy: RewardStrategy,
        trigger_event: str,
        trigger_context: Dict[str, Any],
        reward_amount: float,
        calculation_config: Dict[str, Any]
    ) -> Optional[RewardRecord]:
        """
        创建奖励记录
        
        Args:
            user_id: 用户ID
            strategy: 奖励策略对象
            trigger_event: 触发事件
            trigger_context: 触发上下文
            reward_amount: 奖励金额
            calculation_config: 计算配置
            
        Returns:
            Optional[RewardRecord]: 创建的奖励记录对象，创建失败时返回None
            
        Raises:
            SQLAlchemyError: 数据库操作异常
        """
        try:
            # 获取分发配置
            distribution_config = strategy.get_distribution_config()
            distribution_channel = distribution_config.get('channels', ['wallet'])[0] if distribution_config.get('channels') else 'wallet'
            
            record = await self.reward_record_repo.create_reward(
                user_id=user_id,
                campaign_id=strategy.campaign_id,
                strategy_id=strategy.id,
                trigger_event=trigger_event,
                trigger_context=json.dumps(trigger_context),
                reward_type=calculation_config.get('reward_type', 'points'),
                reward_value=reward_amount,
                calculation_config=json.dumps(calculation_config),
                calculated_value=reward_amount,
                distribution_channel=distribution_channel,
                reward_description=strategy.description
            )
            
            # 提交事务 - 事务管理应由装饰器统一处理
            await self.reward_record_repo.db.commit()
            await self.reward_record_repo.db.refresh(record)
            
            # 发送事件
            dispatch(
                MARKETING_REWARD_ISSUED,
                payload={
                    "reward_record_id": record.id,
                    "user_id": user_id,
                    "campaign_id": strategy.campaign_id,
                    "strategy_id": strategy.id,
                    "amount": reward_amount,
                    "status": record.status,
                    "description": record.reward_description,
                }
            )
            
            self.logger.info(f"成功创建奖励记录: id={record.id}, user={user_id}, strategy={strategy.id}, amount={reward_amount}")
            return record
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(
                f"创建奖励记录失败: user_id={user_id}, strategy_id={strategy.id}, error={db_error.message}", exc_info=True
            )
            return None

    async def _calculate_and_create_rewards_for_target(
        self,
        target_user_id: int,
        campaign: Campaign,
        context: Dict[str, Any]
    ) -> Result[List[RewardRecord]]:
        """
        计算并创建目标用户的奖励记录

        Args:
            target_user_id: 目标用户ID
            campaign: 活动对象
            context: 上下文信息

        Returns:
            Result[List[RewardRecord]]: 包含成功创建的奖励记录列表的结果对象
        """
        created_records = []

        # 1. 获取适用的策略
        strategies_result = await self.strategy_service.get_applicable_strategies(campaign.id)
        if not strategies_result.is_success:
            return strategies_result

        strategies = strategies_result.data

        # 2. 遍历策略并计算/创建奖励
        for strategy in strategies:
            # 检查资格
            is_eligible = strategy.check_constraints(context)
            if not is_eligible:
                continue

            # 计算奖励金额
            reward_amount = await self._calculate_reward_amount(strategy, context)
            if reward_amount is None or reward_amount <= 0:
                self.logger.info(f"策略 {strategy.id} 计算奖励为 0 或无效，跳过创建记录。")
                continue

            # 创建奖励记录
            created_record = await self._create_reward_record(
                user_id=target_user_id,
                strategy=strategy,
                trigger_event=context.get('trigger_event', 'unknown'),
                trigger_context=context,
                reward_amount=reward_amount,
                calculation_config=strategy.get_calculation_config()
            )
            if created_record:
                created_records.append(created_record)

        if not created_records:
             self.logger.info(f"没有为用户 {target_user_id} (campaign: {campaign.id}) 创建任何奖励记录。")
             return self.create_success_result([])

        return self.create_success_result(created_records)

    async def process_invitation_rewards(self, invitation_id: int) -> Result[Dict[str, Any]]:
        """
        处理邀请奖励发放
        
        Args:
            invitation_id: 邀请ID
            
        Returns:
            Result[Dict[str, Any]]: 包含奖励记录ID列表的结果
        """
        self.logger.info(f"开始处理邀请奖励: invitation_id={invitation_id}")
        
        try:
            # 1. 获取邀请信息
            if not self.invitation_repo:
                return self.create_error_result(
                    ErrorCode.OPERATION_FAILED,
                    "邀请仓库未初始化"
                )
            
            invitation = await self.invitation_repo.get_by_id(invitation_id)
            if not invitation:
                return self.create_error_result(
                    ErrorCode.RESOURCE_NOT_FOUND,
                    f"邀请记录不存在: {invitation_id}"
                )
            
            # 2. 获取活动信息
            if not self.campaign_service:
                return self.create_error_result(
                    ErrorCode.OPERATION_FAILED,
                    "活动服务未初始化"
                )
            
            campaign_result = await self.campaign_service.get_campaign(invitation.campaign_id)
            if not campaign_result.is_success or not campaign_result.data:
                return self.create_error_result(
                    ErrorCode.RESOURCE_NOT_FOUND,
                    f"关联活动不存在: {invitation.campaign_id}"
                )
            
            campaign = campaign_result.data
            
            # 3. 构建上下文信息
            context = {
                "invitation_id": invitation_id,
                "inviter_id": invitation.inviter_id,
                "invitee_id": invitation.invitee_id,
                "campaign_id": invitation.campaign_id,
                # 移除不可序列化的campaign对象，只保留基本信息
                "campaign_name": campaign.name if campaign else None,
                "campaign_status": campaign.status if campaign else None,
                "trigger_event": "invitation_completed",
                "invitation_code": invitation.code,
                # 添加基础金额，用于百分比类型策略的计算
                "base_amount": 100.0,  # 默认基础金额
                "amount": 100.0  # 兼容性字段
            }
            
            # 4. 为邀请人和被邀请人分别处理奖励
            reward_record_ids = []
            
            # 处理邀请人奖励
            if invitation.inviter_id:
                inviter_result = await self._calculate_and_create_rewards_for_target(
                    target_user_id=invitation.inviter_id,
                    campaign=campaign,
                    context=context
                )
                
                if inviter_result.is_success:
                    inviter_records = inviter_result.data
                    reward_record_ids.extend([record.id for record in inviter_records])
                    self.logger.info(f"邀请人 {invitation.inviter_id} 获得 {len(inviter_records)} 个奖励记录")
                else:
                    self.logger.warning(f"邀请人奖励处理失败: {inviter_result.result_msg}")
            
            # 处理被邀请人奖励
            if invitation.invitee_id:
                invitee_result = await self._calculate_and_create_rewards_for_target(
                    target_user_id=invitation.invitee_id,
                    campaign=campaign,
                    context=context
                )
                
                if invitee_result.is_success:
                    invitee_records = invitee_result.data
                    reward_record_ids.extend([record.id for record in invitee_records])
                    self.logger.info(f"被邀请人 {invitation.invitee_id} 获得 {len(invitee_records)} 个奖励记录")
                else:
                    self.logger.warning(f"被邀请人奖励处理失败: {invitee_result.result_msg}")
            
            # 5. 更新邀请记录状态（可选）
            try:
                await self.invitation_repo.update_by_id(
                    invitation_id,
                    {"is_used": True, "used_at": get_utc_now_without_tzinfo()}
                )
            except Exception as e:
                self.logger.warning(f"更新邀请记录状态失败: {str(e)}")
            
            self.logger.info(f"邀请奖励处理完成: invitation_id={invitation_id}, 创建记录数={len(reward_record_ids)}")
            return self.create_success_result({"reward_record_ids": reward_record_ids})
            
        except Exception as e:
            self.logger.exception(f"处理邀请奖励失败: invitation_id={invitation_id}, error={str(e)}")
            return self.create_error_result(
                ErrorCode.OPERATION_FAILED,
                f"处理邀请奖励失败: {str(e)}"
            )

    async def distribute_rewards(self, record_ids: List[int]) -> Result[Dict[str, Any]]:
        """分发奖励"""
        try:
            distributed_count = 0
            failed_count = 0
            failed_records = []
            
            for record_id in record_ids:
                try:
                    record = await self.get_resource_by_id(record_id)
                    if not record:
                        failed_count += 1
                        failed_records.append({"record_id": record_id, "reason": "记录不存在"})
                        continue
                    
                    # 检查记录状态
                    if record.status == "issued":
                        self.logger.warning(f"奖励记录 {record_id} 已经发放，跳过")
                        continue
                    
                    if record.status == "failed":
                        self.logger.warning(f"奖励记录 {record_id} 发放失败，跳过")
                        continue
                    
                    # 调用分发服务
                    if hasattr(self, 'distribution_service') and self.distribution_service:
                        distribution_result = await self.distribution_service.distribute_reward(record)
                        if distribution_result.is_success:
                            distributed_count += 1
                            self.logger.info(f"奖励记录 {record_id} 分发成功")
                        else:
                            failed_count += 1
                            failed_records.append({
                                "record_id": record_id, 
                                "reason": distribution_result.result_msg
                            })
                            self.logger.error(f"奖励记录 {record_id} 分发失败: {distribution_result.result_msg}")
                    else:
                        # 如果没有分发服务，直接更新状态为已发放
                        await self.reward_record_repo.update_reward_status(
                            record_id=record_id,
                            status="issued",
                            issued_at=get_utc_now_without_tzinfo()
                        )
                        distributed_count += 1
                        self.logger.info(f"奖励记录 {record_id} 状态更新为已发放")
                        
                except Exception as e:
                    failed_count += 1
                    failed_records.append({"record_id": record_id, "reason": str(e)})
                    self.logger.exception(f"处理奖励记录 {record_id} 时发生异常: {str(e)}")
            
            return self.create_success_result({
                "distributed_count": distributed_count,
                "failed_count": failed_count,
                "total_count": len(record_ids),
                "failed_records": failed_records
            })
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"分发奖励失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def update_reward_status(
        self, 
        record_id: int, 
        status: str, 
        issued_at: Optional[datetime] = None, 
        failure_reason: Optional[str] = None
    ) -> Result[RewardRecordResponse]:
        """更新奖励记录状态"""
        try:
            record = await self.get_resource_by_id(record_id)
            if not record:
                return self.resource_not_found_result(record_id)
            
            # 验证状态值
            valid_statuses = ["pending", "issued", "failed", "cancelled"]
            if status not in valid_statuses:
                return self.create_error_result(
                    ErrorCode.VALIDATION_ERROR,
                    f"无效的状态值: {status}，有效状态: {valid_statuses}"
                )
            
            # 状态转换验证
            if record.status == "issued" and status != "issued":
                return self.create_error_result(
                    ErrorCode.VALIDATION_ERROR,
                    "已发放的奖励记录不能修改状态"
                )
            
            # 准备更新数据
            update_data = {"status": status}
            
            if status == "issued":
                update_data["issued_at"] = issued_at or get_utc_now_without_tzinfo()
                update_data["distribution_status"] = "success"
                update_data["failure_reason"] = None
            elif status == "failed":
                update_data["failure_reason"] = failure_reason or "未知错误"
                update_data["distribution_status"] = "failed"
            elif status == "cancelled":
                update_data["failure_reason"] = failure_reason or "已取消"
                update_data["distribution_status"] = "cancelled"
            
            # 更新记录
            await self.reward_record_repo.update_reward_status(
                record_id=record_id,
                status=status,
                issued_at=update_data.get("issued_at"),
                failure_reason=update_data.get("failure_reason")
            )
            
            # 清除缓存
            await self.clear_resource_cache(record_id)
            
            # 重新获取更新后的记录
            updated_record = await self.get_resource_by_id(record_id)
            if not updated_record:
                return self.create_error_result(
                    ErrorCode.OPERATION_FAILED,
                    "更新后无法获取记录"
                )
            
            # 转换为响应模型
            record_response = RewardRecordResponse(
                id=updated_record.id,
                strategy_id=updated_record.strategy_id,
                campaign_id=updated_record.campaign_id,
                invitation_id=updated_record.invitation_id,
                trigger_event=updated_record.trigger_event,
                trigger_context=updated_record.trigger_context,
                user_id=updated_record.user_id,
                reward_type=updated_record.reward_type,
                reward_value=updated_record.reward_value,
                reward_unit=updated_record.reward_unit,
                reward_description=updated_record.reward_description,
                calculation_config=updated_record.calculation_config,
                calculated_value=updated_record.calculated_value,
                distribution_channel=updated_record.distribution_channel,
                distribution_status=updated_record.distribution_status,
                distribution_result=updated_record.distribution_result,
                status=updated_record.status,
                issued_at=updated_record.issued_at,
                failure_reason=updated_record.failure_reason,
                created_at=updated_record.created_at,
                updated_at=updated_record.updated_at
            )
            
            self.logger.info(f"奖励记录 {record_id} 状态更新为 {status}")
            return self.create_success_result(record_response)
            
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"更新奖励状态失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_rewards_by_trigger_event(
        self, 
        trigger_event: str, 
        start_time: datetime, 
        end_time: datetime
    ) -> Result[List[RewardRecordResponse]]:
        """根据触发事件获取奖励记录"""
        try:
            records = await self.reward_record_repo.get_rewards_by_trigger_event(
                trigger_event=trigger_event,
                start_time=start_time,
                end_time=end_time
            )
            
            return self.create_success_result(records)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_rewards_by_distribution_channel(
        self, 
        distribution_channel: str, 
        status: str
    ) -> Result[List[RewardRecordResponse]]:
        """根据分发渠道获取奖励记录"""
        try:
            records = await self.reward_record_repo.get_rewards_by_distribution_channel(
                distribution_channel=distribution_channel,
                status=status
            )
            
            return self.create_success_result(records)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )



    async def process_rewards(self, trigger_event: str, context: Dict[str, Any]) -> Result[Dict[str, List[int]]]:
        """
        处理奖励事件，根据触发事件和上下文发放奖励

        Args:
            trigger_event: 触发事件
            context: 事件上下文

        Returns:
            Result[Dict[str, List[int]]]: 操作结果，data 包含 'reward_record_ids'
        """
        self.logger.info(f"开始处理奖励事件: {trigger_event}")
        all_created_record_ids = []

        try:
            # 1. 获取活动信息
            campaign_id = context.get('campaign_id')
            if not campaign_id:
                return self.create_error_result(
                    error_code=ErrorCode.VALIDATION_ERROR,
                    error_message="缺少活动ID"
                )
            
            campaign_result = await self.campaign_service.get_campaign(campaign_id)
            if not campaign_result.is_success or not campaign_result.data:
                return self.create_error_result(
                    campaign_result.error_code or ErrorCode.CAMPAIGN_NOT_FOUND,
                    f"无法获取活动信息: {campaign_result.error_message}"
                )
            campaign = campaign_result.data

            # 2. 获取适用的策略
            strategies_result = await self.strategy_service.get_strategies_by_event(campaign_id, trigger_event)
            if not strategies_result.is_success:
                return strategies_result

            strategies = strategies_result.data
            if not strategies:
                self.logger.info(f"活动 {campaign_id} 没有适用于事件 {trigger_event} 的策略")
                return self.create_success_result({"reward_record_ids": []})

            # 3. 为每个策略处理奖励
            for strategy in strategies:
                # 检查策略是否可以触发
                if not strategy.can_trigger(context):
                    self.logger.debug(f"策略 {strategy.id} 不满足触发条件")
                    continue

                # 检查限制条件
                if not strategy.check_constraints(context):
                    self.logger.debug(f"策略 {strategy.id} 不满足限制条件")
                    continue

                # 获取目标用户
                target_users = strategy.get_target_users(context)
                if not target_users:
                    self.logger.debug(f"策略 {strategy.id} 没有目标用户")
                    continue

                # 为每个目标用户创建奖励
                for user_id in target_users:
                    # 计算奖励金额
                    reward_amount = strategy.calculate_reward(context)
                    if reward_amount <= 0:
                        self.logger.debug(f"策略 {strategy.id} 计算奖励为 0，跳过")
                        continue

                    # 创建奖励记录
                    record = await self._create_reward_record(
                        user_id=user_id,
                        strategy=strategy,
                        trigger_event=trigger_event,
                        trigger_context=context,
                        reward_amount=reward_amount,
                        calculation_config=strategy.get_calculation_config()
                    )
                    if record:
                        all_created_record_ids.append(record.id)

            self.logger.info(f"奖励事件处理完成: {trigger_event}, 创建记录数: {len(all_created_record_ids)}")
            return self.create_success_result({"reward_record_ids": all_created_record_ids})

        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"处理奖励事件失败: {trigger_event}, error={db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )



    async def issue_reward(self, record_id: int) -> Result[RewardRecordResponse]:
        """
        发放奖励
        
        Args:
            record_id: 奖励记录ID
            
        Returns:
            Result[RewardRecordResponse]: 结果对象，包含发放状态
        """
        try:
            record = await self.get_resource_by_id(record_id)
            if not record:
                return self.resource_not_found_result(record_id)
                
            if record.status == "issued":
                return self.create_error_result(
                    error_code=ErrorCode.REWARD_ALREADY_ISSUED,
                    error_message="奖励已发放"
                )
            
            # 模拟发放奖励的过程
            # 在实际应用中，这里可能会调用支付服务或积分服务等
            record = await record.update(
                status="issued",
                issued_at=get_utc_now_without_tzinfo()
            )
            
            # 发送事件
            event_data = {
                "record_id": record.id,
                "user_id": record.user_id,
                "campaign_id": record.campaign_id,
                "reward_value": record.reward_value,
                "reward_type": record.reward_type,
            }
            dispatch(MARKETING_REWARD_ISSUED, payload=event_data)
            
            self.logger.info(f"奖励记录已发放: {record_id}")
            return self.create_success_result({
                "record_id": record.id,
                "issued": True,
                "issued_at": record.issued_at
            })
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"发放奖励失败: {db_error.message}")
            # 事务回滚应由装饰器统一处理
            await self.db.rollback()
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_reward_records(self, params: GetRewardRecordsParams) -> Result[RewardRecordListResponse]:
        """获取奖励记录列表（通用）

        Args:
            params: 查询参数对象，包含分页和过滤条件

        Returns:
            Result[RewardRecordListResponse]: 包含分页信息的奖励记录列表结果
        """
        try:
            page_num = params.page_num
            page_size = params.page_size

            self.logger.info(f"获取奖励记录列表: page={page_num}, size={page_size}, filters={params.model_dump(exclude={'page_num', 'page_size'})}")

            # 调用仓库方法获取记录
            records, total = await self.reward_record_repo.get_reward_records(
                page=page_num,
                page_size=page_size,
                campaign_id=params.campaign_id,
                user_id=params.user_id,
                reward_type=params.reward_type,
                status=params.status,
                strategy_id=params.strategy_id,
                trigger_event=params.trigger_event,
                distribution_channel=params.distribution_channel
            )

            record_responses = [RewardRecordResponse.model_validate(rec) for rec in records]

            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0

            paginated_response = RewardRecordListResponse(
                items=record_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )

            return self.create_success_result(paginated_response)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"获取奖励记录列表失败: 错误={db_error.message}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )

    async def get_user_rewards(
        self, 
        user_id: int,
        campaign_id: Optional[int] = None,
        status: Optional[str] = None,
        page_num: int = 1,
        page_size: int = 20
    ) -> Result:
        """
        获取用户的奖励记录
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            status: 记录状态（可选）
            page_num: 页码
            page_size: 每页数量
            
        Returns:
            Result[RewardRecordListResponse]: 包含奖励记录列表和分页信息的响应结果
        """
        try:
            records, total = await self.reward_record_repo.get_user_rewards(
                user_id=user_id,
                campaign_id=campaign_id,
                status=status,
                page_num=page_num,
                page_size=page_size
            )
            
            record_responses = [RewardRecordResponse.model_validate(rec) for rec in records]
            
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            paginated_response = RewardRecordListResponse(
                items=record_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.error(f"获取用户奖励记录失败: 错误={db_error.message}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def get_reward_stats(
        self, 
        user_id: int,
        campaign_id: Optional[int] = None
    ) -> Result:
        """
        获取用户的奖励统计信息
        
        Args:
            user_id: 用户ID
            campaign_id: 活动ID（可选）
            
        Returns:
            Result[RewardRecordResponse]: 结果对象，包含奖励统计信息
        """
        try:
            # 获取用户奖励记录列表(不分页，获取所有)
            filters = {"user_id": user_id}
            if campaign_id is not None:
                filters["campaign_id"] = campaign_id
                
            records, total = await self.reward_record_repo.get_paginated(
                page_num=1,
                page_size=10000,  # 设置较大的限制，实际项目中可能需要分批处理
                **filters
            )
            
            # 计算总记录数
            total_count = len(records)
            
            # 计算不同状态的记录数
            issued_count = sum(1 for record in records if record.status == "issued")
            pending_count = sum(1 for record in records if record.status == "pending")
            failed_count = sum(1 for record in records if record.status == "failed")
            
            # 计算总奖励值
            total_value = sum(record.reward_value for record in records)
            
            # 计算不同状态的奖励值
            issued_value = sum(record.reward_value for record in records if record.status == "issued")
            pending_value = sum(record.reward_value for record in records if record.status == "pending")
            failed_value = sum(record.reward_value for record in records if record.status == "failed")
            
            # 按奖励类型分组统计
            by_type = {}
            for record in records:
                reward_type = record.reward_type
                if reward_type not in by_type:
                    by_type[reward_type] = {"count": 0, "value": 0}
                
                by_type[reward_type]["count"] += 1
                by_type[reward_type]["value"] += record.reward_value
            
            self.logger.info(f"用户 {user_id} 的奖励统计: 总数={total_count}, 总值={total_value}, 已发放={issued_count}(值={issued_value}), 未发放={pending_count}(值={pending_value})")
            
            return self.create_success_result(RewardStatsResponse(
                total_rewards=total_count,
                total_value=total_value,
                issued_rewards=issued_count,
                issued_value=issued_value,
                pending_rewards=pending_count,
                pending_value=pending_value,
                failed_rewards=failed_count,
                failed_value=failed_value
            ))
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"获取用户奖励统计失败: {db_error.message}")
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )


class RewardDistributionService(BaseService):
    """奖励分发服务"""

    def __init__(self, channel_repo: RewardDistributionChannelRepository, redis: Optional[Redis] = None, **kwargs):
        config = ServiceConfig(
            resource_type="reward_distribution",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=False  # 分发服务通常不需要批量操作
        )

        super().__init__(channel_repo, config, redis, **kwargs)
        self.channel_repo = channel_repo
    
    async def get_distribution_channels(self) -> Result[List[RewardDistributionChannelResponse]]:
        """获取分发渠道列表"""
        try:
            if not self.channel_repo:
                return self.create_error_result(
                    ErrorCode.OPERATION_FAILED,
                    "分发渠道仓库未初始化"
                )
            
            channels = await self.channel_repo.get_active_channels()
            
            # 转换为响应模型
            channel_responses = []
            for channel in channels:
                channel_responses.append(RewardDistributionChannelResponse(
                    id=channel.id,
                    name=channel.name,
                    channel_type=channel.channel_type,
                    description=channel.description,
                    config=channel.config,
                    is_active=channel.is_active,
                    priority=channel.priority,
                    total_distributed=channel.total_distributed,
                    total_value=channel.total_value,
                    created_at=channel.created_at,
                    updated_at=channel.updated_at
                ))
            
            return self.create_success_result(channel_responses)
            
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"获取分发渠道列表失败: {db_error.message}")
            return self.create_error_result(
                ErrorCode.OPERATION_FAILED,
                create_user_friendly_message(db_error)
            )
    
    async def create_distribution_channel(self, channel_data: RewardDistributionChannelCreate) -> Result[RewardDistributionChannelResponse]:
        """创建分发渠道"""
        try:
            if not self.channel_repo:
                return self.create_error_result(
                    ErrorCode.OPERATION_FAILED,
                    "分发渠道仓库未初始化"
                )
            
            # 验证配置格式
            try:
                json.loads(channel_data.config)
            except json.JSONDecodeError:
                return self.create_error_result(
                    ErrorCode.VALIDATION_ERROR,
                    "渠道配置格式不正确，必须是有效的JSON"
                )
            
            # 创建渠道
            channel = await self.channel_repo.create(channel_data)
            
            # 转换为响应模型
            channel_response = RewardDistributionChannelResponse(
                id=channel.id,
                name=channel.name,
                channel_type=channel.channel_type,
                description=channel.description,
                config=channel.config,
                is_active=channel.is_active,
                priority=channel.priority,
                total_distributed=channel.total_distributed,
                total_value=channel.total_value,
                created_at=channel.created_at,
                updated_at=channel.updated_at
            )
            
            return self.create_success_result(channel_response)
            
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            self.logger.exception(f"创建分发渠道失败: {db_error.message}")
            return self.create_error_result(
                ErrorCode.OPERATION_FAILED,
                create_user_friendly_message(db_error)
            )
    
    async def distribute_reward(self, record: RewardRecord) -> Result[Dict[str, Any]]:
        """分发单个奖励"""
        try:
            # 获取分发渠道
            channel = await self._get_distribution_channel(record.distribution_channel)
            if not channel:
                return self.create_error_result(
                    error_code=ErrorCode.RESOURCE_NOT_FOUND,
                    error_message="分发渠道不存在"
                )
            
            # 执行分发
            result = await self._execute_distribution(channel, record)
            
            # 更新记录状态
            await self._update_record_status(record, result)
            
            return self.create_success_result(result)
        except Exception as e:
            # 映射 SQLAlchemy 异常为自定义数据库异常
            db_error = map_sqlalchemy_exception(e)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=create_user_friendly_message(db_error)
            )
    
    async def _get_distribution_channel(self, channel_type: str) -> Optional[RewardDistributionChannel]:
        """获取分发渠道"""
        if self.channel_repo:
            return await self.channel_repo.get_channel_by_type(channel_type)
        return None
    
    async def _execute_distribution(self, channel: RewardDistributionChannel, record: RewardRecord) -> Dict[str, Any]:
        """执行分发"""
        channel_config = channel.get_config()
        
        # 根据渠道类型执行不同的分发逻辑
        if channel.channel_type == RewardType.CASH:
            return await self._distribute_to_wallet(channel_config, record)
        elif channel.channel_type == RewardType.POINTS:
            return await self._distribute_to_points(channel_config, record)
        elif channel.channel_type == RewardType.COUPON:
            return await self._distribute_to_coupon(channel_config, record)
        else:
            raise ValueError(f"Unsupported channel type: {channel.channel_type}")
    
    async def _distribute_to_wallet(self, config: Dict[str, Any], record: RewardRecord) -> Dict[str, Any]:
        """分发到钱包"""
        # 实现钱包分发逻辑
        return {"status": "success", "channel": RewardType.CASH, "transaction_id": "tx_123"}
    
    async def _distribute_to_points(self, config: Dict[str, Any], record: RewardRecord) -> Dict[str, Any]:
        """分发到积分"""
        # 实现积分分发逻辑
        return {"status": "success", "channel": RewardType.POINTS, "points_added": record.reward_value}
    
    async def _distribute_to_coupon(self, config: Dict[str, Any], record: RewardRecord) -> Dict[str, Any]:
        """分发优惠券"""
        # 实现优惠券分发逻辑
        return {"status": "success", "channel": RewardType.COUPON, "coupon_code": "COUPON123"}
    
    async def _update_record_status(self, record: RewardRecord, result: Dict[str, Any]) -> None:
        """更新记录状态"""
        if result.get("status") == "success":
            record.status = "issued"
            record.distribution_status = "success"
            record.issued_at = get_utc_now_without_tzinfo()
        else:
            record.status = "failed"
            record.distribution_status = "failed"
            record.failure_reason = result.get("error", "Unknown error")
        
        record.distribution_result = json.dumps(result)
        # 事务管理应由装饰器统一处理


