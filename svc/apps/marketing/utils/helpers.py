"""
邀请功能相关的工具函数
"""
# 标准库导入
import logging
import random
import string
from datetime import datetime, timedelta

# 项目内模块导入
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

logger = logging.getLogger(__name__)

def generate_invitation_code(length: int = 8) -> str:
    """生成随机邀请码
    
    Args:
        length: 邀请码长度，默认8位
        
    Returns:
        随机生成的邀请码
    """
    # 使用大写字母和数字，排除容易混淆的字符（如0和O，1和l）
    chars = ''.join(set(string.ascii_uppercase + string.digits) - set('01IO'))
    return ''.join(random.choices(chars, k=length))

def calculate_expiry_time(days: int = 7) -> datetime:
    """计算邀请过期时间
    
    Args:
        days: 有效期天数，默认7天
        
    Returns:
        过期时间
    """
    return get_utc_now_without_tzinfo() + timedelta(days=days)

def is_invitation_expired(expires_at: datetime) -> bool:
    """检查邀请是否已过期
    
    Args:
        expires_at: 过期时间
        
    Returns:
        是否已过期
    """
    if not expires_at:
        return False
    
    return get_utc_now_without_tzinfo() > expires_at 