"""
商品管理模块

该模块提供商品管理相关功能，包括：
- 商品信息管理
- 商品分类管理
- 商品库存管理
- 商品价格管理
- 商品状态管理

模块结构：
- models: 数据模型定义
- schemas: 请求/响应模式定义
- repositories: 数据访问层
- services: 业务逻辑层
- routers: API路由定义
- events: 事件处理器
- utils: 工具函数
"""

from .models import *
from .schemas import *
from .services import *
from .repositories import *

# 导入事件处理器（确保事件处理器被注册）
from .events import product_events, category_events, inventory_events

__version__ = "1.0.0"
__author__ = "Product Team"
