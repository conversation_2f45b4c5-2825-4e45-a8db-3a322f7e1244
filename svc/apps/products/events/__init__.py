"""
商品管理模块事件处理器

该模块包含商品管理相关的事件处理器：
- ProductEventHandler: 商品事件处理器
- CategoryEventHandler: 分类事件处理器
- InventoryEventHandler: 库存事件处理器
"""

# 导入事件处理器模块，确保事件处理器被注册
from . import product_events
from . import category_events
from . import inventory_events

from .product_events import ProductEventHandler
from .category_events import CategoryEventHandler
from .inventory_events import InventoryEventHandler

__all__ = [
    "product_events",
    "category_events",
    "inventory_events",
    "ProductEventHandler",
    "CategoryEventHandler",
    "InventoryEventHandler"
]
