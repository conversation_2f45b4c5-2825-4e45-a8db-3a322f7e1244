"""
SKU（产品可售单元）模型，支持与规格选项多对多、图片一对多关联
"""
import enum
from typing import Dict

from sqlalchemy import (BigInteger, Column, DateTime, ForeignKey,
                        Integer, String, Table, Index, UniqueConstraint)
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 多对多中间表：SKU-规格选项
sku_option_table = Table(
    "sku_options",
    Base.metadata,
    Column("sku_id", BigInteger, ForeignKey("skus.id", ondelete="CASCADE"), primary_key=True),
    Column("spec_option_id", BigInteger, ForeignKey("spec_options.id", ondelete="CASCADE"), primary_key=True),
    Column("created_at", DateTime, default=get_utc_now_without_tzinfo, nullable=False),
    Column("updated_at", DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo, nullable=False),
    # 添加索引以提升查询性能
    Index("idx_sku_option_sku_id", "sku_id"),
    Index("idx_sku_option_spec_option_id", "spec_option_id"),
    # 添加唯一约束防止重复关联
    UniqueConstraint("sku_id", "spec_option_id", name="uq_sku_option")
)

class SKUStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"

class ProductSKU(Base, ResourceMixin):
    """
    SKU（产品可售单元）模型，支持与规格选项多对多、图片一对多关联
    """
    __tablename__ = "skus"
    __resource_type__ = "sku"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False, comment="所属产品ID")
    sku = Column(String(100), unique=True, nullable=False, comment="SKU编码")
    price = Column(Integer, nullable=False, default=0, comment="SKU售价（分）")
    stock_quantity = Column(Integer, nullable=False, default=0, comment="SKU库存")
    status = Column(String(20), nullable=False, default=SKUStatus.ACTIVE, comment="SKU状态")
    sort_order = Column(Integer, nullable=False, default=0, comment="排序权重")
    image_url = Column(String(500), nullable=True, comment="SKU主图URL")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, nullable=False, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")

    # 关系
    product = relationship("Product", back_populates="skus", lazy="selectin", foreign_keys=[product_id])
    spec_options = relationship("SpecOption", secondary="sku_options", back_populates="skus", lazy="selectin")

    def is_active(self) -> bool:
        return self.status == SKUStatus.ACTIVE

    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "product_id": self.product_id,
            "sku": self.sku,
            "price": self.price,
            "stock_quantity": self.stock_quantity,
            "status": self.status,
            "sort_order": self.sort_order,
            "image_url": self.image_url,
            "meta_data": self.meta_data,
            # "spec_options": [option.to_user_dict() for option in self.spec_options] if self.spec_options else [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }
