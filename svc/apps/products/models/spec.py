"""
商品规格相关模型，定义规格、规格选项等
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Integer, ForeignKey, Enum, Table, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from typing import Dict

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class SpecStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"

class Spec(Base, ResourceMixin):
    """
    规格模型，支持与分类多对多关联
    """
    __tablename__ = "specs"
    __resource_type__ = "spec"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="规格名称")
    description = Column(String, nullable=True, comment="规格描述")
    sort_order = Column(Integer, nullable=False, default=0, comment="排序权重")
    status = Column(String(20), nullable=False, default=SpecStatus.ACTIVE, comment="规格状态")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, nullable=False, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")

    # 关系 - 使用字符串引用避免循环导入
    categories = relationship("Category", secondary="category_specs", back_populates="specs", lazy="selectin")

    def is_active(self) -> bool:
        return self.status == SpecStatus.ACTIVE

    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "sort_order": self.sort_order,
            "status": self.status,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }

class SpecOptionStatus(str, enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DELETED = "deleted"

class SpecOption(Base, ResourceMixin):
    """
    规格选项模型，支持与SKU多对多关联
    """
    __tablename__ = "spec_options"
    __resource_type__ = "spec_option"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    spec_id = Column(BigInteger, ForeignKey("specs.id"), nullable=False, comment="所属规格ID")
    value = Column(String(200), nullable=False, comment="规格值")
    sort_order = Column(Integer, nullable=False, default=0, comment="排序权重")
    status = Column(String(20), nullable=False, default=SpecOptionStatus.ACTIVE, comment="规格选项状态")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, nullable=False, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")

    # 关系
    spec = relationship("Spec", backref="options", lazy="selectin", foreign_keys=[spec_id])
    skus = relationship("ProductSKU", secondary="sku_options", back_populates="spec_options", lazy="selectin")

    def is_active(self) -> bool:
        return self.status == SpecOptionStatus.ACTIVE

    def to_dict(self) -> Dict:
        return {
            "id": self.id,
            "spec_id": self.spec_id,
            "value": self.value,
            "sort_order": self.sort_order,
            "status": self.status,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }

    def to_user_dict(self) -> Dict:
        return {
            "id": self.id,
            "spec_id": self.spec_id,
            "value": self.value
        } 