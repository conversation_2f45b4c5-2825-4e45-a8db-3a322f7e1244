"""
分类数据访问层。
负责分类模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.products.models.category import Category, CategoryStatus
from svc.apps.products.schemas.category import CategoryCreate, CategoryUpdate
from svc.core.repositories import BaseRepository


class CategoryRepository(BaseRepository[Category, CategoryCreate, CategoryUpdate]):
    """分类仓库类，提供分类数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化分类仓库"""
        super().__init__(db, Category)

    def _get_default_load_options(self) -> List[Any]:
        """获取分类模型的默认关系加载选项
        
        默认加载：
        - parent: 父分类（selectin关系）
        - children: 子分类（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Category.parent),
            selectinload(Category.children)
        ]



    async def get_featured_categories(
        self, 
        page_num: int = 1,
        page_size: int = 50,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Category], int]:
        """获取推荐分类（分页）"""
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            is_featured=True,
            status=CategoryStatus.ACTIVE,
            order_by="sort_order",
            order_direction="asc",
            load_options=load_options
        )
        return items, total

    async def get_categories_by_level(
        self, 
        level: int, 
        page_num: int = 1,
        page_size: int = 50,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Category], int]:
        """根据层级获取分类（分页）"""
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            level=level,
            status=CategoryStatus.ACTIVE,
            order_by="sort_order",
            order_direction="asc",
            load_options=load_options
        )
        return items, total

    async def get_children_categories(
        self, 
        parent_id: int, 
        page_num: int = 1,
        page_size: int = 50,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Category], int]:
        """获取子分类（分页）"""
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            parent_id=parent_id,
            status=CategoryStatus.ACTIVE,
            order_by="sort_order",
            order_direction="asc",
            load_options=load_options
        )
        return items, total

    async def search_categories(
        self,
        search_term: str,
        limit: int = 100,
        load_options: Optional[List[Any]] = None
    ) -> List[Category]:
        """搜索分类"""
        search_fields = ["name", "description", "slug"]
        return await self.search(
            term=search_term,
            fields=search_fields,
            limit=limit,
            load_options=load_options
        )
