"""
库存数据访问层。
负责库存模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import List, Optional, Tuple, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.products.models.inventory import Inventory, InventoryStatus
from svc.apps.products.schemas.inventory import (InventoryCreate,
                                                 InventoryUpdate)
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class InventoryRepository(BaseRepository[Inventory, InventoryCreate, InventoryUpdate]):
    """库存仓库类，提供库存数据访问方法
    
    该仓库类实现了Inventory模型的数据访问操作，
    包括库存的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化库存仓库"""
        super().__init__(db, Inventory)

    def _get_default_load_options(self) -> List[Any]:
        """获取库存模型的默认关系加载选项
        
        默认加载：
        - product: 所属商品（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Inventory.product)
        ]
    
    async def get_inventories(
        self,
        page_num: int = 1,
        page_size: int = 10,
        product_id: Optional[int] = None,
        variant_id: Optional[int] = None,
        status: Optional[str] = None,
        warehouse_location: Optional[str] = None,
        supplier_id: Optional[int] = None,
        low_stock_only: Optional[bool] = None,
        expired_only: Optional[bool] = None,
        order_by: str = "created_at",
        order_direction: str = "desc",
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Inventory], int]:
        """获取库存列表及总数
        
        Args:
            page_num: 页码
            page_size: 每页记录数
            product_id: 商品ID筛选
            variant_id: 变体ID筛选
            status: 库存状态筛选
            warehouse_location: 仓库位置筛选
            supplier_id: 供应商ID筛选
            low_stock_only: 仅显示低库存
            expired_only: 仅显示过期库存
            order_by: 排序字段
            order_desc: 是否降序排序
            load_options: SQLAlchemy加载选项
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总记录数
        """
        simple_filters = {}
        if product_id:
            simple_filters["product_id"] = product_id
        if variant_id:
            simple_filters["variant_id"] = variant_id
        if status:
            simple_filters["status"] = status
        if warehouse_location:
            simple_filters["warehouse_location"] = warehouse_location
        if supplier_id:
            simple_filters["supplier_id"] = supplier_id

        adv_filters = {}
        # 低库存筛选（固定阈值 10）
        if low_stock_only:
            adv_filters["available_quantity__lte"] = 10
            adv_filters["available_quantity__gt"] = 0

        # 过期库存筛选
        if expired_only:
            now = get_utc_now_without_tzinfo()
            adv_filters["expiry_date__is_not_null"] = True
            adv_filters["expiry_date__lte"] = now

        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction,
            filters=adv_filters or None,
            load_options=load_options,
            **simple_filters,
        )
        return items, total

    
    async def get_expired_inventory(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Inventory], int]:
        """获取过期库存
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        now = get_utc_now_without_tzinfo()
        conditions = [
            self.model.expiry_date.is_not(None),
            self.model.expiry_date <= now,
            self.model.status != InventoryStatus.EXPIRED
        ]
        
        items, total = await self.get_paginated(
            page_num=(skip // limit) + 1,
            page_size=limit,
            order_by="expiry_date",
            order_direction="asc",
            filters={
                "expiry_date__is_not_null": True,
                "expiry_date__lte": get_utc_now_without_tzinfo(),
                "status__ne": InventoryStatus.EXPIRED,
            },
        )
        return items, total
    
    async def get_low_stock_inventory(
        self, 
        threshold: int = 10,
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Inventory], int]:
        """获取低库存记录
        
        Args:
            threshold: 低库存阈值
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        conditions = [
            self.model.status == InventoryStatus.AVAILABLE,
            self.model.available_quantity <= threshold,
            self.model.available_quantity > 0
        ]
        
        items, total = await self.get_paginated(
            page_num=(skip // limit) + 1,
            page_size=limit,
            order_by="available_quantity",
            order_direction="asc",
            filters={
                "status": InventoryStatus.AVAILABLE,
                "available_quantity__lte": threshold,
                "available_quantity__gt": 0,
            },
        )
        return items, total
    
    async def reserve_inventory(
        self, 
        inventory_id: int, 
        quantity: int
    ) -> Optional[Inventory]:
        """预留库存
        
        Args:
            inventory_id: 库存ID
            quantity: 预留数量
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在或预留失败则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        if inventory.available_quantity >= quantity:
            update_data = {
                "reserved_quantity": inventory.reserved_quantity + quantity,
                "available_quantity": inventory.available_quantity - quantity
            }
            return await self.update(inventory, update_data)
        
        return None
    
    async def release_reservation(
        self, 
        inventory_id: int, 
        quantity: int
    ) -> Optional[Inventory]:
        """释放预留库存
        
        Args:
            inventory_id: 库存ID
            quantity: 释放数量
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在或释放失败则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        if inventory.reserved_quantity >= quantity:
            update_data = {
                "reserved_quantity": inventory.reserved_quantity - quantity,
                "available_quantity": inventory.available_quantity + quantity
            }
            return await self.update(inventory, update_data)
        
        return None
    
    async def adjust_inventory(
        self, 
        inventory_id: int, 
        quantity_change: int,
        reason: str = ""
    ) -> Optional[Inventory]:
        """调整库存数量
        
        Args:
            inventory_id: 库存ID
            quantity_change: 数量变化（正数为增加，负数为减少）
            reason: 调整原因
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        new_quantity = max(0, inventory.quantity + quantity_change)
        new_available = max(0, inventory.available_quantity + quantity_change)
        
        update_data = {
            "quantity": new_quantity,
            "available_quantity": new_available,
            "notes": f"{inventory.notes or ''}\n调整: {quantity_change}, 原因: {reason}".strip()
        }
        return await self.update(inventory, update_data)
