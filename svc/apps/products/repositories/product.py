"""
商品数据访问层。
负责商品模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import and_, asc, desc, func, or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload

from svc.apps.albums.models.album import Album
from svc.apps.products.models.category import Category
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.schemas.product import ProductCreate, ProductUpdate
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class ProductRepository(BaseRepository[Product, ProductCreate, ProductUpdate]):
    """商品仓库类，提供商品数据访问方法
    
    该仓库类实现了Product模型的数据访问操作，
    包括商品的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化商品仓库"""
        super().__init__(db, Product)
    
    def _get_default_load_options(self) -> List[Any]:
        """获取商品模型的默认关系加载选项
        
        默认加载：
        - category: 商品分类（selectin关系）
        - album: 商品图册（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Product.category),
            selectinload(Product.album)
        ]
    
    async def create_with_album(self, product_data: ProductCreate, album_id: int) -> Product:
        """创建商品并关联图册ID（复用基类 create）"""
        data = {**product_data.model_dump(), "album_id": album_id}
        return await self.create(data)



    async def get_product_with_inventory_and_skus(self, product_id: int) -> Optional[Product]:
        """获取商品详情并加载库存和SKU信息"""
        product = await self.get_by_id(product_id)
        if product:
            # 加载dynamic关系
            product = await self.load_dynamic_relations(product, ['inventories', 'skus'])
        return product



    async def get_by_id(self, id: int, include_deleted: bool = False, load_options: Optional[List[Any]] = None) -> Optional[Product]:
        """获取商品详情并加载相册与封面（复用基类 get_by_id + load_options）"""
        if load_options is None:
            # 默认加载album和cover_image
            load_options = [selectinload(self.model.album).selectinload(Album.cover_image)]
        
        return await super().get_by_id(
            id=id,
            include_deleted=include_deleted,
            load_options=load_options,
        )

    async def get_products(
        self,
        page_num: int = 1,
        page_size: int = 10,
        status: Optional[str] = None,
        category_id: Optional[int] = None,
        is_featured: Optional[bool] = None,
        search_term: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        order_by: str = "created_at",
        order_direction: str = "desc",
        include_deleted: bool = False,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Product], int]:
        """获取商品列表及总数

        Args:
            page_num: 页码
            page_size: 每页记录数
            status: 商品状态筛选
            category_id: 分类ID筛选
            is_featured: 是否推荐商品筛选
            search_term: 搜索关键词
            min_price: 最低价格筛选
            max_price: 最高价格筛选
            order_by: 排序字段
            order_desc: 是否降序排序
            include_deleted: 是否包含已删除记录
            load_options: SQLAlchemy加载选项

        Returns:
            Tuple[List[Product], int]: 商品列表和总记录数
        """
        # 构建查询条件
        filters = {}
        if status:
            filters["status"] = status
        if category_id:
            filters["category_id"] = category_id
        if is_featured is not None:
            filters["is_featured"] = is_featured

        # 构建高级过滤条件
        advanced_filters = {}
        if search_term:
            advanced_filters["name__ilike"] = f"%{search_term}%"
        if min_price is not None:
            advanced_filters["price__gte"] = min_price
        if max_price is not None:
            advanced_filters["price__lte"] = max_price

        # 使用BaseRepository的get_paginated方法
        products, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            order_by=order_by,
            filters=advanced_filters,
            include_deleted=include_deleted,
            load_options=load_options,
            **filters,
        )
        
        return products, total

    async def get_products_with_category_and_album(
        self,
        page_num: int = 1,
        page_size: int = 10,
        **kwargs
    ) -> Tuple[List[Product], int]:
        """获取商品列表并加载分类和图册信息"""
        load_options = [
            selectinload(Product.category),
            selectinload(Product.album)
        ]
        return await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            load_options=load_options,
            **kwargs
        )

    async def search_products(
        self,
        search_term: str,
        limit: int = 100,
        load_options: Optional[List[Any]] = None
    ) -> List[Product]:
        """搜索商品"""
        search_fields = ["name", "description", "short_description", "sku"]
        return await self.search(
            term=search_term,
            fields=search_fields,
            limit=limit,
            load_options=load_options
        )


