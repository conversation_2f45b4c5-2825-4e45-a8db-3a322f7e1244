from typing import List, Optional, Any, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.products.models.sku import ProductSKU
from svc.apps.products.schemas.sku import ProductSKUCreate, ProductSKUUpdate
from svc.core.repositories.base import BaseRepository


class ProductSKURepository(BaseRepository[ProductSKU, ProductSKUCreate, ProductSKUUpdate]):
    """
    SKU仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, ProductSKU)

    def _get_default_load_options(self) -> List[Any]:
        """获取SKU模型的默认关系加载选项
        
        默认加载：
        - product: 所属商品（selectin关系）
        - spec_options: 规格选项（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(ProductSKU.product),
            selectinload(ProductSKU.spec_options)
        ]

