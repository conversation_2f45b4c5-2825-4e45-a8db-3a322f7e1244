from typing import List, Optional, Any, Tuple

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.apps.products.models.spec import Spec, SpecOption
from svc.apps.products.schemas.spec import SpecCreate, SpecUpdate, SpecOptionCreate, SpecOptionUpdate
from svc.core.repositories.base import BaseRepository


class SpecRepository(BaseRepository[Spec, SpecCreate, SpecUpdate]):
    """
    规格仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, Spec)

    def _get_default_load_options(self) -> List[Any]:
        """获取规格模型的默认关系加载选项
        
        默认加载：
        - categories: 关联的分类（selectin关系）
        - options: 规格选项（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Spec.categories),
            selectinload(Spec.options)
        ]




class SpecOptionRepository(BaseRepository[SpecOption, SpecOptionCreate, SpecOptionUpdate]):
    """
    规格选项仓库，复用BaseRepository，暴露适合服务层调用的接口
    """
    def __init__(self, db: AsyncSession):
        super().__init__(db, SpecOption)

    def _get_default_load_options(self) -> List[Any]:
        """获取规格选项模型的默认关系加载选项
        
        默认加载：
        - spec: 所属规格（selectin关系）
        - skus: 关联的SKU（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(SpecOption.spec),
            selectinload(SpecOption.skus)
        ]

