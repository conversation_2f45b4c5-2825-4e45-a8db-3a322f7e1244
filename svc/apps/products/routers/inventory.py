"""
库存API路由
包含库存的创建、查询、更新和调整功能
"""
from typing import Any, Dict

from fastapi import APIRouter, Depends, Path, Query, status

from svc.apps.auth.dependencies import (get_current_active_user,
                                        has_permission, resource_permission)
from svc.apps.auth.models.user import User
from svc.apps.products.dependencies import get_inventory_service
from svc.apps.products.schemas.inventory import (GetInventoriesParams,
                                                 InventoryAdjustmentRequest,
                                                 InventoryCreate,
                                                 InventoryListResponse,
                                                 InventoryReservationRequest,
                                                 InventoryResponse,
                                                 InventoryUpdate,
                                                 InventoryBatchUpdateRequest,
                                                 InventoryBatchUpdateResponse,
                                                 InventoryBatchDeleteRequest,
                                                 InventoryBatchDeleteResponse,
                                                 InventoryBatchAdjustmentRequest,
                                                 InventoryBatchAdjustmentResponse)
from svc.apps.products.services.inventory import InventoryService
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.exceptions.route_error_handler import (INVENTORY_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

# 创建路由器
router = APIRouter(
    tags=["库存管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_list_inventories(
    params: PageParams = Depends(),
    product_id: int = Query(None, description="商品ID"),
    variant_id: int = Query(None, description="变体ID"),
    status: str = Query(None, description="库存状态"),
    warehouse_location: str = Query(None, description="仓库位置"),
    supplier_id: int = Query(None, description="供应商ID"),
    low_stock_only: bool = Query(None, description="仅显示低库存"),
    expired_only: bool = Query(None, description="仅显示过期库存"),
    order_by: str = Query("created_at", description="排序字段"),
    order_direction: str = Query("desc", description="排序方向: asc/desc"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:read"))
) -> Result[InventoryListResponse]:
    """获取库存列表 (管理端)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        product_id=product_id,
        variant_id=variant_id,
        status=status,
        warehouse_location=warehouse_location,
        supplier_id=supplier_id,
        low_stock_only=low_stock_only,
        expired_only=expired_only,
        order_by=order_by,
        order_direction=order_direction
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result

@router.get("/admin/{inventory_id}", response_model=Result[InventoryResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_get_inventory_details(
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "read")),
) -> Result[InventoryResponse]:
    """获取库存详情 (管理端)"""
    result = await inventory_service.get_inventory(inventory_id=inventory_id)
    return result

@router.post("/admin/create", response_model=Result[Dict[str, Any]], status_code=status.HTTP_201_CREATED)
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_create_inventory(
    inventory_data: InventoryCreate,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:create")),
) -> Result:
    """创建库存记录 (管理端)"""
    result = await inventory_service.create_inventory(params=inventory_data)
    return result

@router.post("/admin/{inventory_id}/adjust", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_adjust_inventory(
    adjustment_data: InventoryAdjustmentRequest,
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "update")),
) -> Result:
    """调整库存 (管理端)"""
    result = await inventory_service.adjust_inventory(
        inventory_id=inventory_id, 
        params=adjustment_data
    )
    return result

@router.post("/admin/{inventory_id}/reserve", response_model=Result[Dict[str, Any]])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_reserve_inventory(
    reservation_data: InventoryReservationRequest,
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "update")),
) -> Result:
    """预留库存 (管理端)"""
    result = await inventory_service.reserve_inventory(
        inventory_id=inventory_id, 
        params=reservation_data
    )
    return result

# === 内部API路由 (Internal Routes) ===

@router.get("/internal/product/{product_id}", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def internal_get_product_inventory(
    product_id: int = Path(..., description="商品ID"),
    params: PageParams = Depends(),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> Result[InventoryListResponse]:
    """获取商品库存 (内部API)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        product_id=product_id,
        status="available"  # 只返回可用库存
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result

@router.get("/internal/variant/{variant_id}", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def internal_get_variant_inventory(
    variant_id: int = Path(..., description="变体ID"),
    params: PageParams = Depends(),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> Result[InventoryListResponse]:
    """获取变体库存 (内部API)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        variant_id=variant_id,
        status="available"  # 只返回可用库存
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result


# 批量操作接口
@router.put("/admin/batch", response_model=Result[InventoryBatchUpdateResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_batch_update_inventories(
    request: InventoryBatchUpdateRequest,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:update"))
) -> Result[InventoryBatchUpdateResponse]:
    """批量更新库存 (管理端)
    
    批量更新多个库存的状态、位置等信息
    """
    return await inventory_service.batch_update_inventories(request=request)


@router.delete("/admin/batch", response_model=Result[InventoryBatchDeleteResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_batch_delete_inventories(
    request: InventoryBatchDeleteRequest,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:delete"))
) -> Result[InventoryBatchDeleteResponse]:
    """批量删除库存 (管理端)
    
    批量删除多个库存记录，支持软删除和硬删除
    """
    return await inventory_service.batch_delete_inventories(request=request)


@router.post("/admin/batch/adjust", response_model=Result[InventoryBatchAdjustmentResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_batch_adjust_inventories(
    request: InventoryBatchAdjustmentRequest,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:update"))
) -> Result[InventoryBatchAdjustmentResponse]:
    """批量调整库存 (管理端)
    
    批量调整多个库存的数量，支持增加和减少
    """
    return await inventory_service.batch_adjust_inventories(request=request)
