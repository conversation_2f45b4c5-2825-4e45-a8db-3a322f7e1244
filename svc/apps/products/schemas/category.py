from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BatchUpdateRequest, BatchUpdateResponse, BatchDeleteRequest, BatchDeleteResponse


class CategoryBase(CamelCaseModel):
    """分类基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "摩托车头盔",
                "description": "包括全盔、半盔、揭面盔等各类摩托车安全头盔产品",
                "slug": "motorcycle-helmets",
                "parent_id": None,
                "level": 1,
                "status": "active",
                "sort_order": 100,
                "is_featured": True,
                "meta_data": {
                    "icon": "helmet",
                    "banner_image": "https://cdn.example.com/categories/motorcycle-helmets-banner.jpg",
                    "display_name": "摩托车头盔"
                }
            }
        }
    )
    name: str = Field(..., max_length=200, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    slug: Optional[str] = Field(default=None, description="URL别名")
    parent_id: Optional[int] = Field(default=None, description="父分类ID")
    level: int = Field(default=1, description="分类层级")
    status: Optional[str] = Field(default="active", description="分类状态")
    sort_order: int = Field(default=0, description="排序权重")
    is_featured: bool = Field(default=False, description="是否推荐")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class CategoryCreate(CategoryBase):
    """分类创建模型"""
    pass

class CategoryUpdate(CamelCaseModel):
    """分类更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "摩托车头盔（热门）",
                "description": "包括全盔、半盔、揭面盔等各类摩托车安全头盔产品，热门分类",
                "slug": "motorcycle-helmets-hot",
                "parent_id": None,
                "level": 1,
                "status": "active",
                "sort_order": 50,
                "is_featured": True,
                "meta_data": {
                    "icon": "helmet",
                    "banner_image": "https://cdn.example.com/categories/motorcycle-helmets-hot-banner.jpg",
                    "display_name": "摩托车头盔（热门）",
                    "promotion_tag": "热门"
                }
            }
        }
    )
    name: Optional[str] = Field(None, max_length=200, description="分类名称")
    description: Optional[str] = Field(None, description="分类描述")
    slug: Optional[str] = Field(default=None, description="URL别名")
    parent_id: Optional[int] = Field(default=None, description="父分类ID")
    level: Optional[int] = Field(default=None, description="分类层级")
    status: Optional[str] = Field(default=None, description="分类状态")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    is_featured: Optional[bool] = Field(default=None, description="是否推荐")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class CategoryResponse(CategoryBase):
    """分类响应模型"""
    model_config = ConfigDict(
        from_attributes=True, 
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "摩托车头盔",
                "description": "包括全盔、半盔、揭面盔等各类摩托车安全头盔产品",
                "slug": "motorcycle-helmets",
                "parent_id": None,
                "level": 1,
                "status": "active",
                "sort_order": 100,
                "is_featured": True,
                "meta_data": {
                    "icon": "helmet",
                    "banner_image": "https://cdn.example.com/categories/motorcycle-helmets-banner.jpg",
                    "display_name": "摩托车头盔"
                },
                "created_at": "2024-01-10T09:00:00",
                "updated_at": "2024-03-20T15:30:00",
                "deleted_at": None,
                "specs": [
                    {
                        "id": 1,
                        "name": "品牌",
                        "options": ["SHOEI", "ARAI", "AGV", "HJC"]
                    },
                    {
                        "id": 2,
                        "name": "头盔类型",
                        "options": ["全盔", "半盔", "揭面盔", "越野盔"]
                    }
                ]
            }
        }
    )
    id: int = Field(..., description="分类ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")
    specs: List[Dict[str, Any]] = Field(default=[], description="关联的规格列表")

class CategoryListResponse(PaginatedResponse[CategoryResponse]):
    """分类列表响应模型"""  
    pass

class CategoryTreeResponse(CategoryResponse):
    """分类树响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True
    )
    
    children: List['CategoryTreeResponse'] = Field(default=[], description="子分类列表")

class GetCategoriesParams(BaseModel):
    """获取分类列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore',
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 20,
                "status": "active",
                "parent_id": None,
                "level": 1,
                "is_featured": True,
                "search_term": "头盔",
                "order_by": "sort_order",
                "order_direction": "asc"
            }
        }
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="分类状态")
    parent_id: Optional[int] = Field(default=None, description="父分类ID")
    level: Optional[int] = Field(default=None, description="分类层级")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐分类")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc")

class UserCategoryResponse(BaseModel):
    """用户端分类响应模型，仅用于用户端接口，减少冗余字段传输"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "摩托车头盔",
                "description": "包括全盔、半盔、揭面盔等各类摩托车安全头盔产品",
                "slug": "motorcycle-helmets",
                "image_url": "https://cdn.example.com/categories/motorcycle-helmets-cover.jpg",
                "icon": "helmet",
                "is_featured": True,
                "attributes": {
                    "product_count": 89,
                    "popular_brands": ["SHOEI", "ARAI", "AGV", "HJC"],
                    "price_range": "¥299-¥8999"
                }
            }
        }
    )
    id: int = Field(..., description="分类ID")
    name: str = Field(..., description="分类名称")
    description: Optional[str] = Field(default=None, description="分类描述")
    slug: str = Field(..., description="分类别名")
    image_url: Optional[str] = Field(default=None, description="分类图片URL")
    icon: Optional[str] = Field(default=None, description="分类图标")
    is_featured: bool = Field(default=False, description="是否为推荐分类")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="分类属性")

class UserCategoryListResponse(PaginatedResponse[UserCategoryResponse]):
    """用户端分类分页响应模型"""
    pass


# 批量操作相关Schema
class CategoryBatchUpdate(CamelCaseModel):
    """分类批量更新数据模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "status": "active",
                "is_featured": True,
                "sort_order": 1
            }
        }
    )
    
    status: Optional[str] = Field(default=None, description="分类状态")
    is_featured: Optional[bool] = Field(default=None, description="是否推荐")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    parent_id: Optional[int] = Field(default=None, description="父分类ID")
    level: Optional[int] = Field(default=None, description="分类层级")


class CategoryBatchUpdateRequest(BatchUpdateRequest[CategoryBatchUpdate]):
    """分类批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "is_featured": True
                }
            }
        }
    )


class CategoryBatchUpdateResponse(BatchUpdateResponse):
    """分类批量更新响应模型"""
    pass


class DeleteCategoryParams(BaseModel):
    """删除分类参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "category_id": 1,
                "soft_delete": True
            }
        }
    )
    
    category_id: int = Field(..., description="分类ID")
    soft_delete: bool = Field(default=True, description="是否软删除")

class CategoryBatchDeleteRequest(BatchDeleteRequest):
    """分类批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )


class CategoryBatchDeleteResponse(BatchDeleteResponse):
    """分类批量删除响应模型"""
    pass
