from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.batch import BatchUpdateRequest, BatchUpdateResponse, BatchDeleteRequest, BatchDeleteResponse

class InventoryBase(CamelCaseModel):
    """库存基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "variant_id": 5,
                "quantity": 45,
                "reserved_quantity": 8,
                "available_quantity": 37,
                "status": "available",
                "warehouse_location": "上海头盔仓库",
                "shelf_location": "A区-02-03-01",
                "batch_number": "BATCH20240324001",
                "lot_number": "LOT001-2024",
                "expiry_date": "2026-12-31T23:59:59",
                "unit_cost": 280000,
                "total_cost": 12600000,
                "supplier_id": 1,
                "purchase_order_id": 1001,
                "notes": "SHOEI GT-AIR II 全盔摩托车头盔，新到货商品，质量检验合格"
            }
        }
    )
    
    product_id: int = Field(..., description="商品ID")
    variant_id: Optional[int] = Field(default=None, description="变体ID")
    quantity: int = Field(..., description="库存数量")
    reserved_quantity: int = Field(default=0, description="预留数量")
    available_quantity: int = Field(default=0, description="可用数量")
    status: str = Field(default="available", description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    shelf_location: Optional[str] = Field(default=None, description="货架位置")
    batch_number: Optional[str] = Field(default=None, description="批次号")
    lot_number: Optional[str] = Field(default=None, description="批号")
    expiry_date: Optional[datetime] = Field(default=None, description="过期日期")
    unit_cost: Optional[int] = Field(default=None, description="单位成本(分)")
    total_cost: Optional[int] = Field(default=None, description="总成本(分)")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    purchase_order_id: Optional[int] = Field(default=None, description="采购订单ID")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryCreate(InventoryBase):
    """库存创建模型"""
    pass

class InventoryUpdate(CamelCaseModel):
    """库存更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "quantity": 52,
                "available_quantity": 44,
                "status": "available",
                "warehouse_location": "上海头盔仓库",
                "shelf_location": "A区-02-03-02",
                "notes": "库存调整：新增7件SHOEI头盔，调整货架位置"
            }
        }
    )
    
    quantity: Optional[int] = Field(default=None, description="库存数量")
    reserved_quantity: Optional[int] = Field(default=None, description="预留数量")
    available_quantity: Optional[int] = Field(default=None, description="可用数量")
    status: Optional[str] = Field(default=None, description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    shelf_location: Optional[str] = Field(default=None, description="货架位置")
    batch_number: Optional[str] = Field(default=None, description="批次号")
    lot_number: Optional[str] = Field(default=None, description="批号")
    expiry_date: Optional[datetime] = Field(default=None, description="过期日期")
    unit_cost: Optional[int] = Field(default=None, description="单位成本(分)")
    total_cost: Optional[int] = Field(default=None, description="总成本(分)")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    purchase_order_id: Optional[int] = Field(default=None, description="采购订单ID")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryResponse(InventoryBase):
    """库存响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "product_id": 1,
                "variant_id": 5,
                "quantity": 45,
                "reserved_quantity": 8,
                "available_quantity": 37,
                "status": "available",
                "warehouse_location": "上海头盔仓库",
                "shelf_location": "A区-02-03-01",
                "batch_number": "BATCH20240324001",
                "lot_number": "LOT001-2024",
                "expiry_date": "2026-12-31T23:59:59",
                "unit_cost": 280000,
                "total_cost": 12600000,
                "supplier_id": 1,
                "purchase_order_id": 1001,
                "notes": "SHOEI GT-AIR II 全盔摩托车头盔，新到货商品，质量检验合格",
                "created_at": "2024-03-24T10:00:00",
                "updated_at": "2024-03-24T14:30:00"
            }
        }
    )
    
    id: int = Field(description="库存ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class InventoryListResponse(PaginatedResponse[InventoryResponse]):
    """库存列表响应模型"""
    pass

class GetInventoriesParams(BaseModel):
    """获取库存列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    product_id: Optional[int] = Field(default=None, description="商品ID")
    variant_id: Optional[int] = Field(default=None, description="变体ID")
    status: Optional[str] = Field(default=None, description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    low_stock_only: Optional[bool] = Field(default=None, description="仅显示低库存")
    expired_only: Optional[bool] = Field(default=None, description="仅显示过期库存")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_direction: Optional[str] = Field(default="desc", description="排序方向: asc/desc")

class InventoryAdjustmentRequest(BaseModel):
    """库存调整请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "adjustment_type": "increase",
                "quantity": 50,
                "reason": "新进货",
                "notes": "供应商A发货"
            }
        }
    )
    
    adjustment_type: str = Field(..., description="调整类型: increase/decrease")
    quantity: int = Field(..., description="调整数量")
    reason: str = Field(..., description="调整原因")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryReservationRequest(BaseModel):
    """库存预留请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "quantity": 5,
                "order_id": 1001,
                "notes": "订单预留"
            }
        }
    )
    
    quantity: int = Field(..., description="预留数量")
    order_id: Optional[int] = Field(default=None, description="订单ID")
    notes: Optional[str] = Field(default=None, description="备注")


# 批量操作相关Schema
class InventoryBatchUpdate(CamelCaseModel):
    """库存批量更新数据模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "status": "available",
                "warehouse_location": "仓库A",
                "quantity": 100
            }
        }
    )
    
    quantity: Optional[int] = Field(default=None, description="库存数量")
    reserved_quantity: Optional[int] = Field(default=None, description="预留数量")
    available_quantity: Optional[int] = Field(default=None, description="可用数量")
    status: Optional[str] = Field(default=None, description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    shelf_location: Optional[str] = Field(default=None, description="货架位置")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")


class InventoryBatchUpdateRequest(BatchUpdateRequest[InventoryBatchUpdate]):
    """库存批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "available",
                    "warehouse_location": "仓库A"
                }
            }
        }
    )


class InventoryBatchUpdateResponse(BatchUpdateResponse):
    """库存批量更新响应模型"""
    pass


class InventoryBatchDeleteRequest(BatchDeleteRequest):
    """库存批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": False
            }
        }
    )


class InventoryBatchDeleteResponse(BatchDeleteResponse):
    """库存批量删除响应模型"""
    pass


# 批量库存调整相关Schema
class InventoryBatchAdjustmentRequest(BaseModel):
    """批量库存调整请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "inventory_ids": [1, 2, 3, 4, 5],
                "adjustment_type": "increase",
                "quantity": 50,
                "reason": "批量补货",
                "notes": "供应商A批量发货"
            }
        }
    )
    
    inventory_ids: List[int] = Field(..., min_length=1, max_length=100, description="库存ID列表，最多100个")
    adjustment_type: str = Field(..., description="调整类型: increase/decrease")
    quantity: int = Field(..., description="调整数量")
    reason: str = Field(..., description="调整原因")
    notes: Optional[str] = Field(default=None, description="备注")


class InventoryBatchAdjustmentResponse(BaseModel):
    """批量库存调整响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "adjusted_count": 5,
                "failed_ids": [],
                "total_requested": 5,
                "success_rate": 1.0,
                "details": {"total_quantity_changed": 250}
            }
        }
    )
    
    adjusted_count: int = Field(..., description="成功调整的库存数量")
    failed_ids: List[int] = Field(default=[], description="调整失败的库存ID列表")
    total_requested: int = Field(..., description="请求调整的库存总数")
    success_rate: float = Field(..., description="成功率（0.0-1.0）")
    details: Dict[str, Any] = Field(default={}, description="调整详细信息")
