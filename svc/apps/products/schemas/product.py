from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.apps.albums.schemas.album import AlbumResponse, UserAlbumResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BatchUpdateRequest, BatchUpdateResponse, BatchDeleteRequest, BatchDeleteResponse


class ProductBase(CamelCaseModel):
    """商品基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "description": "日本SHOEI顶级摩托车头盔，采用多层复合纤维材质，配备双镜片系统，内置蓝牙耳机槽，符合ECE 22.05安全认证",
                "short_description": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "sku": "SHOEI-GTAIR2-FULL",
                "barcode": "4901234567890",
                "category_id": 1,
                "status": "active",
                "price": 399900,
                "cost_price": 280000,
                "market_price": 459900,
                "currency": "CNY",
                "is_featured": True,
                "is_digital": False,
                "track_inventory": True,
                "stock_quantity": 25,
                "min_stock_level": 5,
                "max_stock_level": 100,
                "weight": 1.65,
                "dimensions": {
                    "length": 28.5,
                    "width": 22.0,
                    "height": 32.0,
                    "unit": "cm"
                },
                "sales_count": 156,
                "view_count": 2340,
                "album_id": 1,
                "image_url": "https://cdn.example.com/products/shoei-gtair2-black.jpg",
                "slug": "shoei-gt-air-ii-full-helmet",
                "seo_title": "SHOEI GT-AIR II 全盔 摩托车头盔 - 日本顶级安全头盔",
                "seo_keywords": "SHOEI,摩托车头盔,全盔,GT-AIR II,安全头盔",
                "seo_description": "日本SHOEI GT-AIR II 全盔摩托车头盔，多层复合纤维材质，双镜片系统，符合ECE 22.05安全认证",
                "attributes": {
                    "brand": "SHOEI",
                    "model": "GT-AIR II",
                    "type": "全盔",
                    "size": "L",
                    "color": "哑光黑",
                    "safety_standard": "ECE 22.05",
                    "weight": "1650g",
                    "material": "多层复合纤维"
                },
                "rich_description": "<h2>产品特色</h2><p>• 多层复合纤维材质，轻量化设计</p><p>• 双镜片系统，防雾防紫外线</p><p>• 内置蓝牙耳机槽，支持通讯设备</p><p>• 符合ECE 22.05安全认证标准</p>",
                "sort_order": 100,
                "meta_data": {
                    "manufacturer": "SHOEI Co., Ltd.",
                    "country_of_origin": "日本",
                    "warranty_period": "24个月",
                    "safety_rating": "5星"
                }
            }
        }
    )
    name: str = Field(..., description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="简短描述")
    sku: str = Field(..., description="商品SKU")
    barcode: Optional[str] = Field(default=None, description="商品条码")
    category_id: Optional[int] = Field(default=None, description="主分类ID")
    status: Optional[str] = Field(default="draft", description="商品状态")
    price: int = Field(..., description="标准售价（分）")
    cost_price: Optional[int] = Field(default=None, description="成本价格（分）")
    market_price: Optional[int] = Field(default=None, description="市场价格（分）")
    currency: str = Field(default="CNY", description="币种")
    is_featured: bool = Field(default=False, description="是否推荐/置顶")
    is_digital: bool = Field(default=False, description="是否数字商品")
    track_inventory: bool = Field(default=True, description="是否启用库存管理")
    stock_quantity: int = Field(default=0, description="总库存")
    min_stock_level: int = Field(default=0, description="最低库存预警线")
    max_stock_level: Optional[int] = Field(default=None, description="最高库存限制")
    weight: Optional[float] = Field(default=None, description="重量(kg)")
    dimensions: Dict[str, Any] = Field(default_factory=dict, description="尺寸信息")
    sales_count: int = Field(default=0, description="累计销量")
    view_count: int = Field(default=0, description="累计浏览量")
    album_id: Optional[int] = Field(default=None, description="主图册ID")
    image_url: Optional[str] = Field(default=None, description="主图URL")
    slug: Optional[str] = Field(default=None, description="URL别名")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="自定义属性")
    rich_description: Optional[str] = Field(default=None, description="富文本详情")
    sort_order: int = Field(default=0, description="排序权重")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class ProductCreate(ProductBase):
    """商品创建模型"""
    pass

class ProductUpdate(CamelCaseModel):
    """商品更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "SHOEI GT-AIR II 全盔 摩托车头盔（限时优惠）",
                "description": "日本SHOEI顶级摩托车头盔，采用多层复合纤维材质，配备双镜片系统，内置蓝牙耳机槽，符合ECE 22.05安全认证，限时优惠中",
                "short_description": "SHOEI GT-AIR II 全盔 摩托车头盔 - 限时优惠",
                "category_id": 1,
                "status": "active",
                "price": 359900,
                "currency": "CNY",
                "stock_quantity": 18,
                "min_stock_level": 8,
                "track_inventory": True,
                "sales_count": 168,
                "view_count": 2560,
                "album_id": 1,
                "image_url": "https://cdn.example.com/products/shoei-gtair2-black-promo.jpg",
                "slug": "shoei-gt-air-ii-full-helmet-promo",
                "seo_title": "SHOEI GT-AIR II 全盔 摩托车头盔 - 限时优惠",
                "seo_keywords": "SHOEI,摩托车头盔,全盔,GT-AIR II,安全头盔,限时优惠",
                "seo_description": "日本SHOEI GT-AIR II 全盔摩托车头盔限时优惠，多层复合纤维材质，双镜片系统",
                "is_featured": True,
                "attributes": {
                    "brand": "SHOEI",
                    "model": "GT-AIR II",
                    "type": "全盔",
                    "size": "L",
                    "color": "哑光黑",
                    "safety_standard": "ECE 22.05",
                    "weight": "1650g",
                    "material": "多层复合纤维",
                    "promotion": "限时优惠"
                },
                "rich_description": "<h2>产品特色</h2><p>• 多层复合纤维材质，轻量化设计</p><p>• 双镜片系统，防雾防紫外线</p><p>• 内置蓝牙耳机槽，支持通讯设备</p><p>• 符合ECE 22.05安全认证标准</p><p>• <strong>限时优惠：立减400元</strong></p>",
                "sort_order": 50,
                "meta_data": {
                    "manufacturer": "SHOEI Co., Ltd.",
                    "country_of_origin": "日本",
                    "warranty_period": "24个月",
                    "safety_rating": "5星",
                    "promotion_end_date": "2024-12-31"
                },
                "sku": "SHOEI-GTAIR2-FULL-PROMO"
            }
        }
    )
    name: Optional[str] = Field(default=None, description="商品名称")
    description: Optional[str] = Field(default=None, description="商品描述")
    short_description: Optional[str] = Field(default=None, description="简短描述")
    category_id: Optional[int] = Field(default=None, description="主分类ID")
    status: Optional[str] = Field(default=None, description="商品状态")
    price: Optional[int] = Field(default=None, description="标准售价（分）")
    currency: Optional[str] = Field(default=None, description="币种")
    stock_quantity: Optional[int] = Field(default=None, description="总库存")
    min_stock_level: Optional[int] = Field(default=None, description="最低库存预警线")
    track_inventory: Optional[bool] = Field(default=None, description="是否启用库存管理")
    sales_count: Optional[int] = Field(default=None, description="累计销量")
    view_count: Optional[int] = Field(default=None, description="累计浏览量")
    album_id: Optional[int] = Field(default=None, description="主图册ID")
    image_url: Optional[str] = Field(default=None, description="主图URL")
    slug: Optional[str] = Field(default=None, description="URL别名")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    is_featured: Optional[bool] = Field(default=None, description="是否推荐/置顶")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="自定义属性")
    rich_description: Optional[str] = Field(default=None, description="富文本详情")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")
    sku: Optional[str] = Field(default=None, description="商品SKU")

class ProductResponse(ProductBase):
    """商品响应模型"""
    model_config = ConfigDict(
        from_attributes=True, 
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "description": "日本SHOEI顶级摩托车头盔，采用多层复合纤维材质，配备双镜片系统，内置蓝牙耳机槽，符合ECE 22.05安全认证",
                "short_description": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "sku": "SHOEI-GTAIR2-FULL",
                "barcode": "4901234567890",
                "category_id": 1,
                "status": "active",
                "price": 399900,
                "cost_price": 280000,
                "market_price": 459900,
                "currency": "CNY",
                "is_featured": True,
                "is_digital": False,
                "track_inventory": True,
                "stock_quantity": 25,
                "min_stock_level": 5,
                "max_stock_level": 100,
                "weight": 1.65,
                "dimensions": {
                    "length": 28.5,
                    "width": 22.0,
                    "height": 32.0,
                    "unit": "cm"
                },
                "sales_count": 156,
                "view_count": 2340,
                "album_id": 1,
                "image_url": "https://cdn.example.com/products/shoei-gtair2-black.jpg",
                "slug": "shoei-gt-air-ii-full-helmet",
                "seo_title": "SHOEI GT-AIR II 全盔 摩托车头盔 - 日本顶级安全头盔",
                "seo_keywords": "SHOEI,摩托车头盔,全盔,GT-AIR II,安全头盔",
                "seo_description": "日本SHOEI GT-AIR II 全盔摩托车头盔，多层复合纤维材质，双镜片系统，符合ECE 22.05安全认证",
                "attributes": {
                    "brand": "SHOEI",
                    "model": "GT-AIR II",
                    "type": "全盔",
                    "size": "L",
                    "color": "哑光黑",
                    "safety_standard": "ECE 22.05",
                    "weight": "1650g",
                    "material": "多层复合纤维"
                },
                "rich_description": "<h2>产品特色</h2><p>• 多层复合纤维材质，轻量化设计</p><p>• 双镜片系统，防雾防紫外线</p><p>• 内置蓝牙耳机槽，支持通讯设备</p><p>• 符合ECE 22.05安全认证标准</p>",
                "sort_order": 100,
                "meta_data": {
                    "manufacturer": "SHOEI Co., Ltd.",
                    "country_of_origin": "日本",
                    "warranty_period": "24个月",
                    "safety_rating": "5星"
                },
                "created_at": "2024-01-15T10:30:00",
                "updated_at": "2024-03-24T14:20:00",
                "deleted_at": None
            }
        }
    )
    id: int = Field(..., description="商品ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class ProductListResponse(PaginatedResponse[ProductResponse]):
    """商品列表响应模型"""
    pass

# 注意：SKU相关的Schema已移动到独立的sku.py文件中

class GetProductsParams(CamelCaseModel):
    """获取商品列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore',
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 20,
                "status": "active",
                "category_id": 1,
                "is_featured": True,
                "search_term": "SHOEI 头盔",
                "min_price": 200000,
                "max_price": 800000,
                "order_by": "created_at",
                "order_direction": "desc",
                "include_deleted": False
            }
        }
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="商品状态")
    category_id: Optional[int] = Field(default=None, description="分类ID")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    min_price: Optional[float] = Field(default=None, description="最低价格")
    max_price: Optional[float] = Field(default=None, description="最高价格")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_direction: Optional[str] = Field(default="desc", description="排序方向: asc/desc")
    include_deleted: Optional[bool] = Field(default=True, description="是否包含已删除记录")

class UserProductSKU(CamelCaseModel):
    """用户端产品规格组合，仅用于用户端接口，减少冗余字段传输"""
    id: int = Field(..., description="规格组合ID")
    sku: str = Field(..., description="SKU")
    price: float = Field(..., description="价格")
    stock_quantity: int = Field(..., description="库存数量")
    spec_option_ids: List[int] = Field(..., description="该组合包含的所有规格值ID")
    spec_option_values: List[str] = Field(..., description="该组合包含的所有规格值")
    album_url: Optional[str] = Field(default=None, description="规格图片URL")

class UserProductSpec(CamelCaseModel):
    """用户端产品规格属性结构"""
    id: int = Field(..., description="规格ID")
    name: str = Field(..., description="规格名")
    options: List[dict] = Field(..., description="可选值列表，如[{id, value}]")

class UserProductResponse(CamelCaseModel):
    """用户端产品响应模型，列表用image_url，详情用album"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "description": "日本SHOEI顶级摩托车头盔，采用多层复合纤维材质，配备双镜片系统，内置蓝牙耳机槽，符合ECE 22.05安全认证",
                "short_description": "SHOEI GT-AIR II 全盔 摩托车头盔",
                "price": 3999.00,
                "currency": "CNY",
                "image_url": "https://cdn.example.com/products/shoei-gtair2-black.jpg",
                "album": {
                    "id": 1,
                    "name": "SHOEI GT-AIR II 产品图册",
                    "description": "SHOEI GT-AIR II 全盔摩托车头盔官方产品图片",
                    "image_count": 6,
                    "cover_image": {
                        "id": 1,
                        "url": "https://cdn.example.com/products/shoei-gtair2-black-cover.jpg",
                        "alt_text": "SHOEI GT-AIR II 哑光黑封面图",
                        "sort_order": 1
                    },
                    "images": [
                        {
                            "id": 1,
                            "url": "https://cdn.example.com/products/shoei-gtair2-black-1.jpg",
                            "alt_text": "SHOEI GT-AIR II 正面图",
                            "sort_order": 1
                        },
                        {
                            "id": 2,
                            "url": "https://cdn.example.com/products/shoei-gtair2-black-2.jpg",
                            "alt_text": "SHOEI GT-AIR II 侧面图",
                            "sort_order": 2
                        }
                    ]
                },
                "is_featured": True,
                "attributes": {
                    "brand": "SHOEI",
                    "model": "GT-AIR II",
                    "type": "全盔",
                    "size": "L",
                    "color": "哑光黑",
                    "safety_standard": "ECE 22.05"
                },
                "rich_description": "<h2>产品特色</h2><p>• 多层复合纤维材质，轻量化设计</p><p>• 双镜片系统，防雾防紫外线</p><p>• 内置蓝牙耳机槽，支持通讯设备</p><p>• 符合ECE 22.05安全认证标准</p>",
                "category_id": 1
            }
        }
    )
    id: int
    name: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    price: float
    currency: str
    image_url: Optional[str] = None  # 产品封面图片URL（列表用）
    album: Optional[UserAlbumResponse] = None  # 产品主图册（详情用，精简版）
    is_featured: bool
    attributes: Dict[str, Any]
    rich_description: Optional[str] = None
    category_id: Optional[int] = Field(default=None, description="主分类ID")
    # specs: List[UserProductSpec]
    # spec_combinations: List[UserProductSpecCombination]

class UserProductListResponse(PaginatedResponse[UserProductResponse]):
    """用户端商品分页响应模型"""
    pass

class ProductBatchUpdate(CamelCaseModel):
    """商品批量更新模型 - 只包含允许批量更新的字段"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "price": 999.00,
                "status": "active",
                "is_featured": True,
                "category_id": 2
            }
        }
    )

    price: Optional[float] = Field(default=None, description="商品价格")
    status: Optional[str] = Field(default=None, description="商品状态")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐商品")
    category_id: Optional[int] = Field(default=None, description="分类ID")

class ProductBatchUpdateRequest(BatchUpdateRequest[ProductBatchUpdate]):
    """产品批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
                "update_data": {
                    "status": "active",
                }
            }
        }
    )

class ProductBatchUpdateResponse(BatchUpdateResponse):
    """商品批量更新响应模型"""
    pass

class DeleteProductParams(BaseModel):
    """删除商品参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "soft_delete": True
            }
        }
    )
    
    product_id: int = Field(..., description="商品ID")
    soft_delete: bool = Field(default=True, description="是否软删除")

class ProductBatchDeleteRequest(BatchDeleteRequest):
    """商品批量删除请求模型"""
    pass

class ProductBatchDeleteResponse(BatchDeleteResponse):
    """商品批量删除响应模型"""
    pass
