"""
SKU相关的Schema定义。
包括SKU的创建、更新、响应和批量操作Schema。
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import BatchUpdateRequest, BatchUpdateResponse, BatchDeleteRequest, BatchDeleteResponse


class ProductSKUBase(CamelCaseModel):
    """SKU基础模型"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    product_id: int = Field(..., description="所属产品ID")
    sku: str = Field(..., description="SKU编码")
    price: int = Field(..., description="SKU售价（分）")
    stock_quantity: int = Field(default=0, description="SKU库存")
    status: str = Field(default="active", description="SKU状态")
    sort_order: int = Field(default=0, description="排序权重")
    image_url: Optional[str] = Field(default=None, description="SKU主图URL")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class ProductSKUCreate(ProductSKUBase):
    """SKU创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "sku": "PROD001-RED-XL",
                "price": 9999,
                "stock_quantity": 100,
                "status": "active",
                "sort_order": 0,
                "image_url": "https://example.com/image.jpg",
                "meta_data": {"color": "red", "size": "xl"},
                "spec_option_ids": [1, 2, 3]
            }
        }
    )
    spec_option_ids: Optional[List[int]] = Field(default=None, description="规格选项ID列表")


class ProductSKUUpdate(CamelCaseModel):
    """SKU更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "price": 8999,
                "stock_quantity": 150,
                "status": "active",
                "sort_order": 1
            }
        }
    )
    
    sku: Optional[str] = Field(default=None, description="SKU编码")
    price: Optional[int] = Field(default=None, description="SKU售价（分）")
    stock_quantity: Optional[int] = Field(default=None, description="SKU库存")
    status: Optional[str] = Field(default=None, description="SKU状态")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    image_url: Optional[str] = Field(default=None, description="SKU主图URL")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")
    spec_option_ids: Optional[List[int]] = Field(default=None, description="规格选项ID列表")


class ProductSKUResponse(ProductSKUBase):
    """SKU响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "product_id": 1,
                "sku": "PROD001-RED-XL",
                "price": 9999,
                "stock_quantity": 100,
                "status": "active",
                "sort_order": 0,
                "image_url": "https://example.com/image.jpg",
                "meta_data": {"color": "red", "size": "xl"},
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(..., description="SKU ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")


class ProductSKUListResponse(PaginatedResponse[ProductSKUResponse]):
    """SKU列表响应模型"""
    pass


# 批量操作相关Schema
class ProductSKUBatchUpdate(CamelCaseModel):
    """SKU批量更新数据模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "price": 899900,
                "stock_quantity": 200,
                "status": "active",
                "sort_order": 10,
                "image_url": "https://cdn.example.com/skus/iphone15pm-dark-xl.jpg"
            }
        }
    )
    
    price: Optional[int] = Field(default=None, description="SKU售价（分）")
    stock_quantity: Optional[int] = Field(default=None, description="SKU库存")
    status: Optional[str] = Field(default=None, description="SKU状态")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    image_url: Optional[str] = Field(default=None, description="SKU主图URL")


class ProductSKUBatchUpdateRequest(BatchUpdateRequest[ProductSKUBatchUpdate]):
    """SKU批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                "update_data": {
                    "price": 899900,
                    "status": "active",
                    "sort_order": 10
                }
            }
        }
    )


class ProductSKUBatchUpdateResponse(BatchUpdateResponse):
    """SKU批量更新响应模型"""
    pass


class ProductSKUBatchDeleteRequest(BatchDeleteRequest):
    """SKU批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                "soft_delete": True
            }
        }
    )


class ProductSKUBatchDeleteResponse(BatchDeleteResponse):
    """SKU批量删除响应模型"""
    pass


# 特殊操作相关Schema
class SKUStockUpdateRequest(BaseModel):
    """SKU库存更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "quantity_change": 50,
                "operation_type": "increase",
                "reason": "iPhone 15 Pro Max 256GB 深空黑色补货入库，供应商发货"
            }
        }
    )
    
    quantity_change: int = Field(..., description="库存变化量（正数增加，负数减少）")
    operation_type: str = Field(..., description="操作类型：increase/decrease")
    reason: Optional[str] = Field(default=None, description="操作原因")


class SKUGenerateCodeRequest(BaseModel):
    """SKU编码生成请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "spec_option_ids": [1, 2, 3],
                "custom_prefix": "IPHONE15PM"
            }
        }
    )
    
    product_id: int = Field(..., description="产品ID")
    spec_option_ids: List[int] = Field(default=[], description="规格选项ID列表")
    custom_prefix: Optional[str] = Field(default=None, description="自定义前缀")


class SKUGenerateCodeResponse(BaseModel):
    """SKU编码生成响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "sku_code": "PROD001-RED-XL",
                "generated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    sku_code: str = Field(..., description="生成的SKU编码")
    generated_at: datetime = Field(..., description="生成时间")


class DeleteSKUParams(BaseModel):
    """删除SKU参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "sku_id": 1,
                "soft_delete": True
            }
        }
    )
    
    sku_id: int = Field(..., description="SKU ID")
    soft_delete: bool = Field(default=True, description="是否软删除")
