from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field

from svc.core.models.base import CamelCaseModel
from svc.core.schemas.base import PaginatedResponse
from svc.core.schemas.batch import (BatchDeleteRequest, BatchDeleteResponse,
                                    BatchUpdateRequest, BatchUpdateResponse)


class SpecBase(CamelCaseModel):
    """规格基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "头盔尺寸",
                "description": "摩托车头盔的尺寸规格",
                "sort_order": 100,
                "status": "active",
                "meta_data": {
                    "category": "size",
                    "unit": "cm",
                    "brand": "SHOEI"
                }
            }
        }
    )
    name: str = Field(..., max_length=200, description="规格名称")
    description: Optional[str] = Field(None, description="规格描述")
    sort_order: int = Field(default=0, description="排序权重")
    status: Optional[str] = Field(default="active", description="规格状态")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class SpecCreate(SpecBase):
    """规格创建模型"""
    pass

class SpecUpdate(CamelCaseModel):
    """规格更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "头盔尺寸（升级版）",
                "description": "摩托车头盔的尺寸规格，包含更多尺寸选项",
                "sort_order": 50,
                "status": "active",
                "meta_data": {
                    "category": "size",
                    "unit": "cm",
                    "brand": "SHOEI",
                    "version": "2.0"
                }
            }
        }
    )
    name: Optional[str] = Field(None, max_length=200, description="规格名称")
    description: Optional[str] = Field(None, description="规格描述")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格状态")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class SpecResponse(SpecBase):
    """规格响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="规格ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")
    # categories: List[Any] = Field(default=[], description="关联的分类列表")
    category_id: Optional[int] = Field(default=None, description="关联的分类ID")
    options: List['SpecOptionResponse'] = Field(default=[], description="规格选项列表")

class SpecListResponse(PaginatedResponse[SpecResponse]):
    """规格列表响应模型"""
    pass

class SpecOptionBase(CamelCaseModel):
    """规格选项基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "spec_id": 1,
                "value": "L",
                "sort_order": 100,
                "status": "active",
                "meta_data": {
                    "head_circumference": "58-59cm",
                    "weight": "1650g",
                    "color": "哑光黑"
                }
            }
        }
    )
    spec_id: int = Field(..., description="所属规格ID")
    value: str = Field(..., max_length=200, description="规格值")
    sort_order: int = Field(default=0, description="排序权重")
    status: Optional[str] = Field(default="active", description="规格选项状态")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class SpecOptionCreate(SpecOptionBase):
    """规格选项创建模型"""
    pass

class SpecOptionUpdate(CamelCaseModel):
    """规格选项更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "value": "L（大号）",
                "sort_order": 50,
                "status": "active",
                "meta_data": {
                    "head_circumference": "58-59cm",
                    "weight": "1650g",
                    "color": "哑光黑",
                    "stock": 25
                }
            }
        }
    )
    value: Optional[str] = Field(None, max_length=200, description="规格值")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格选项状态")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class SpecOptionResponse(SpecOptionBase):
    """规格选项响应模型"""
    model_config = ConfigDict(from_attributes=True, str_strip_whitespace=True)
    id: int = Field(..., description="规格选项ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class SpecOptionListResponse(PaginatedResponse[SpecOptionResponse]):
    """规格选项列表响应模型"""
    pass


# Spec批量操作相关Schema
class SpecBatchUpdate(CamelCaseModel):
    """规格批量更新数据模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "description": "摩托车头盔规格，包含尺寸、颜色、材质等",
                "sort_order": 50,
                "status": "active"
            }
        }
    )
    
    description: Optional[str] = Field(default=None, description="规格描述")
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格状态")


class SpecBatchUpdateRequest(BatchUpdateRequest[SpecBatchUpdate]):
    """规格批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "sort_order": 1
                }
            }
        }
    )


class SpecBatchUpdateResponse(BatchUpdateResponse):
    """规格批量更新响应模型"""
    pass


class SpecBatchDeleteRequest(BatchDeleteRequest):
    """规格批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )


class SpecBatchDeleteResponse(BatchDeleteResponse):
    """规格批量删除响应模型"""
    pass


# SpecOption批量操作相关Schema
class SpecOptionBatchUpdate(CamelCaseModel):
    """规格选项批量更新数据模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "status": "active",
                "sort_order": 1
            }
        }
    )
    
    sort_order: Optional[int] = Field(default=None, description="排序权重")
    status: Optional[str] = Field(default=None, description="规格选项状态")


class SpecOptionBatchUpdateRequest(BatchUpdateRequest[SpecOptionBatchUpdate]):
    """规格选项批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "sort_order": 1
                }
            }
        }
    )


class SpecOptionBatchUpdateResponse(BatchUpdateResponse):
    """规格选项批量更新响应模型"""
    pass


class SpecOptionBatchDeleteRequest(BatchDeleteRequest):
    """规格选项批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )


class SpecOptionBatchDeleteResponse(BatchDeleteResponse):
    """规格选项批量删除响应模型"""
    pass 

class DeleteSpecParams(BaseModel):
    """删除规格参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "spec_id": 1,
                "soft_delete": True
            }
        }
    )
    
    spec_id: int = Field(..., description="规格ID")
    soft_delete: bool = Field(default=True, description="是否软删除")

class DeleteSpecOptionParams(BaseModel):
    """删除规格选项参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "option_id": 1,
                "soft_delete": True
            }
        }
    )
    
    option_id: int = Field(..., description="规格选项ID")
    soft_delete: bool = Field(default=True, description="是否软删除")


# 创建和更新参数类
class CreateSpecParams(SpecCreate):
    """创建规格参数"""
    pass


class UpdateSpecParams(SpecUpdate):
    """更新规格参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "spec_id": 1,
                "name": "头盔尺寸（升级版）",
                "description": "摩托车头盔的尺寸规格，包含更多尺寸选项",
                "sort_order": 50,
                "status": "active",
                "meta_data": {
                    "category": "size",
                    "unit": "cm",
                    "brand": "SHOEI",
                    "version": "2.0"
                }
            }
        }
    )
    spec_id: int = Field(..., description="规格ID")


class CreateSpecOptionParams(SpecOptionCreate):
    """创建规格选项参数"""
    pass


class UpdateSpecOptionParams(SpecOptionUpdate):
    """更新规格选项参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "option_id": 1,
                "value": "XL",
                "sort_order": 50,
                "status": "active",
                "meta_data": {
                    "head_circumference": "60-61cm",
                    "weight": "1700g",
                    "color": "哑光黑"
                }
            }
        }
    )
    option_id: int = Field(..., description="规格选项ID")


# 查询参数类
class GetSpecParams(BaseModel):
    """获取规格详情参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "spec_id": 1
            }
        }
    )
    spec_id: int = Field(..., description="规格ID")


class GetSpecsParams(BaseModel):
    """获取规格列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 10,
                "status": "active",
                "search_term": "头盔",
                "category_id": 1
            }
        }
    )
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页记录数")
    status: Optional[str] = Field(default=None, description="规格状态筛选")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    category_id: Optional[int] = Field(default=None, description="分类ID筛选")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc")


class GetSpecOptionParams(BaseModel):
    """获取规格选项详情参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "option_id": 1
            }
        }
    )
    option_id: int = Field(..., description="规格选项ID")


class GetSpecOptionsParams(BaseModel):
    """获取规格选项列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "spec_id": 1,
                "page_num": 1,
                "page_size": 10
            }
        }
    )
    spec_id: int = Field(..., description="规格ID")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页记录数")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc")    order_direction: Optional[str] = Field(default="asc", description="排序方向: asc/desc")