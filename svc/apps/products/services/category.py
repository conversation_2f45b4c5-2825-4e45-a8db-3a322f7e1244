from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.products.models.category import Category, CategoryStatus
from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.schemas.category import (CategoryBatchDeleteRequest,
                                                CategoryBatchDeleteResponse,
                                                CategoryBatchUpdateRequest,
                                                CategoryBatchUpdateResponse,
                                                CategoryCreate,
                                                CategoryListResponse,
                                                CategoryResponse,
                                                CategoryTreeResponse,
                                                CategoryUpdate,
                                                GetCategoriesParams,
                                                UserCategoryListResponse,
                                                UserCategoryResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）

class CategoryService(BaseService, BatchOperationMixin):
    """分类服务类，提供分类的创建、查询和管理功能"""
    
    # 设置资源类型名称
    resource_type = "category"
    
    def __init__(
        self,
        category_repo: CategoryRepository,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化分类服务"""
        config = ServiceConfig(
            resource_type="category",
            cache_enabled=True,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(category_repo, config, redis, **kwargs)
        self.category_repo = category_repo
    
    async def get_resource_by_id(self, category_id: int) -> Optional[Category]:
        """获取指定ID的分类资源"""
        return await self.category_repo.get_by_id(category_id)
    
    async def get_category(self, category_id: int) -> Result[CategoryResponse]:
        """获取分类信息"""
        try:
            self.logger.info(f"获取分类: id={category_id}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(category_id)
                    cached_category = await self.get_cached_resource(
                        cache_key,
                        lambda data: CategoryResponse.model_validate(data)
                    )
                    if cached_category:
                        self.logger.debug(f"从缓存获取到分类: id={category_id}")
                        return self.create_success_result(cached_category)
                except Exception as e:
                    self.logger.warning(f"从缓存获取分类失败: id={category_id}, 错误={str(e)}")
            
            # 查询数据库
            category = await self.get_resource_by_id(category_id)
            if not category:
                self.logger.warning(f"分类不存在: id={category_id}")
                return self.resource_not_found_result(category_id)
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self.cache_resource(self._get_resource_cache_key(category_id), category_response, CACHE_TTL)
            
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"获取分类失败: id={category_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取分类失败: {str(e)}"
            )
    
    async def get_categories(self, params: GetCategoriesParams, user_mode: bool = False):
        try:
            page_num = params.page_num
            page_size = params.page_size
            self.logger.info(f"获取分类列表: page={page_num}, size={page_size}")
            categories, total = await self.category_repo.get_paginated(
                filters={"status": "active"},
                page_num=page_num,
                page_size=page_size
            )
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            if user_mode:
                user_categories = []
                for category in categories:
                    user_categories.append(UserCategoryResponse(
                        id=category.id,
                        name=category.name,
                        description=category.description,
                        slug=category.slug,
                        image_url=getattr(category, 'image_url', None),
                        icon=getattr(category, 'icon', None),
                        is_featured=category.is_featured,
                        attributes=getattr(category, 'attributes', {})
                    ))
                return self.create_success_result(
                    UserCategoryListResponse(
                        items=user_categories,
                        total=total,
                        page_num=page_num,
                        page_size=page_size,
                        page_count=total_pages
                    )
                )
            category_responses = []
            for category in categories:
                # 构建包含规格信息的分类响应
                category_dict = category.to_dict()
                # 添加关联的规格信息
                specs_data = []
                if hasattr(category, 'specs') and category.specs:
                    for spec in category.specs:
                        specs_data.append({
                            "id": spec.id,
                            "name": spec.name,
                            "description": spec.description,
                            "status": spec.status
                        })
                category_dict["specs"] = specs_data
                category_response = CategoryResponse.model_validate(category_dict)
                category_responses.append(category_response)
            paginated_response = CategoryListResponse(
                items=category_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取分类列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取分类列表失败: {str(e)}"
            )
    
    async def create_category(self, params: CategoryCreate) -> Result[CategoryResponse]:
        """创建分类"""
        try:
            self.logger.info(f"创建分类: name={params.name}, slug={params.slug}")
            
            # 检查slug是否已存在
            existing_category = await self.category_repo.get_one(slug=params.slug)
            if existing_category:
                self.logger.warning(f"分类别名已存在: {params.slug}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"分类别名 {params.slug} 已被使用"
                )
            
            # 创建分类
            category = await self.category_repo.create(**params.model_dump())
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self.cache_resource(self._get_resource_cache_key(category.id), category_response, CACHE_TTL)
            
            # 触发分类创建事件
            event_data = category_response.model_dump()
            dispatch("products:category:created", payload=event_data)
            
            self.logger.info(f"分类创建成功: id={category.id}, name={category.name}")
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"创建分类失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建分类失败: {str(e)}"
            )
    
    async def update_category(self, category_id: int, params: CategoryUpdate) -> Result[CategoryResponse]:
        """更新分类"""
        try:
            self.logger.info(f"更新分类: id={category_id}")
            
            # 获取分类
            category = await self.get_resource_by_id(category_id)
            if not category:
                self.logger.warning(f"分类不存在: {category_id}")
                return self.resource_not_found_result(category_id)
            
            # 如果更新slug，检查是否已存在
            if params.slug is not None and params.slug != category.slug:
                existing_category = await self.category_repo.get_one(slug=params.slug)
                if existing_category and existing_category.id != category.id:
                    self.logger.warning(f"分类别名已存在: {params.slug}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"分类别名 {params.slug} 已被使用"
                    )
            
            # 更新分类
            update_data = params.model_dump(exclude_unset=True)
            category = await self.category_repo.update(category, data=update_data)
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self.cache_resource(self._get_resource_cache_key(category.id), category_response, CACHE_TTL)
            
            # 触发分类更新事件
            event_data = category_response.model_dump()
            event_data["updated_fields"] = list(update_data.keys())
            dispatch("products:category:updated", payload=event_data)
            
            self.logger.info(f"分类更新成功: id={category.id}, name={category.name}")
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"更新分类失败: id={category_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新分类失败: {str(e)}"
            )
    
    async def get_category_tree(self, parent_id: Optional[int] = None) -> Result[List[CategoryTreeResponse]]:
        """获取分类树结构"""
        try:
            self.logger.info(f"获取分类树: parent_id={parent_id}")
            
            categories = await self.category_repo.get_category_tree(parent_id)
            
            # 构建树形响应
            tree_responses = []
            for category in categories:
                tree_response = CategoryTreeResponse.model_validate(category.to_dict())
                # 递归获取子分类
                children = await self.get_category_tree(category.id)
                if children.is_success:
                    tree_response.children = children.data
                tree_responses.append(tree_response)
            
            return self.create_success_result(tree_responses)
        except Exception as e:
            self.logger.error(f"获取分类树失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取分类树失败: {str(e)}"
            )
    
    async def delete_category(self, category_id: int, soft_delete: bool = True) -> Result[Dict[str, Any]]:
        """删除分类
        
        Args:
            category_id: 分类ID
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            self.logger.info(f"删除分类: id={category_id}, soft_delete={soft_delete}")
            
            # 检查分类是否存在
            category = await self.get_resource_by_id(category_id)
            if not category:
                self.logger.warning(f"分类不存在: id={category_id}")
                return self.resource_not_found_result(category_id)
            
            # 检查是否有子分类
            children = await self.category_repo.get_children(category_id)
            if children:
                self.logger.warning(f"分类有子分类，无法删除: id={category_id}")
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="分类有子分类，无法删除"
                )
            
            if soft_delete:
                # 软删除：更新状态为DELETED
                update_data = {"status": CategoryStatus.DELETED}
                await self.category_repo.update(category, update_data)
                self.logger.info(f"分类软删除成功: id={category_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.category_repo.delete(category)
                self.logger.info(f"分类硬删除成功: id={category_id}")
            
            # 清除缓存
            await self.delete_cache(self._get_resource_cache_key(category_id))
            
            # 触发分类删除事件
            event_data = {"category_id": category_id, "soft_delete": soft_delete}
            dispatch("products:category:deleted", payload=event_data)
            
            self.logger.info(f"分类删除成功: id={category_id}")
            return self.create_success_result({"id": category_id, "soft_delete": soft_delete})
            
        except Exception as e:
            self.logger.error(f"删除分类失败: id={category_id}, soft_delete={soft_delete}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"删除分类失败: {str(e)}"
            )
    

    
    async def batch_update_categories(self, request: CategoryBatchUpdateRequest) -> Result[CategoryBatchUpdateResponse]:
        """批量更新分类"""
        self.logger.info(f"批量更新分类: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.category_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_categories(self, request: CategoryBatchDeleteRequest) -> Result[CategoryBatchDeleteResponse]:
        """批量删除分类"""
        self.logger.info(f"批量删除分类: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.category_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
