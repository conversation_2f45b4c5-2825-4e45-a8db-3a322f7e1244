from typing import Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis

from svc.apps.products.models.inventory import Inventory
from svc.apps.products.repositories.inventory import InventoryRepository
from svc.apps.products.schemas.inventory import (
    GetInventoriesParams, InventoryAdjustmentRequest,
    InventoryBatchAdjustmentRequest, InventoryBatchAdjustmentResponse,
    InventoryBatchDeleteRequest, InventoryBatchDeleteResponse,
    InventoryBatchUpdateRequest, InventoryBatchUpdateResponse, InventoryCreate,
    InventoryListResponse, InventoryReservationRequest, InventoryResponse,
    InventoryUpdate)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

# 缓存配置
CACHE_TTL = 1800  # 库存缓存时间较短（30分钟）

class InventoryService(BaseService, BatchOperationMixin):
    """库存服务类，提供库存的创建、查询和管理功能"""

    def __init__(
        self,
        inventory_repo: InventoryRepository,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化库存服务"""
        config = ServiceConfig(
            resource_type="inventory",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(inventory_repo, config, redis, **kwargs)
        self.inventory_repo = inventory_repo
    
    async def get_resource_by_id(self, inventory_id: int) -> Optional[Inventory]:
        """获取指定ID的库存资源"""
        return await self.inventory_repo.get_by_id(inventory_id)
    
    async def get_inventory(self, inventory_id: int) -> Result[InventoryResponse]:
        """获取库存信息"""
        try:
            self.logger.info(f"获取库存: id={inventory_id}")
            
            # 查询数据库
            inventory = await self.get_resource_by_id(inventory_id)
            if not inventory:
                self.logger.warning(f"库存不存在: id={inventory_id}")
                return self.resource_not_found_result(inventory_id)
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"获取库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取库存失败: {str(e)}"
            )
    
    async def get_inventories(self, params: GetInventoriesParams) -> Result[InventoryListResponse]:
        """获取库存列表"""
        try:
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取库存列表: page={page_num}, size={page_size}")
            
            # 使用仓库类获取库存列表和总数
            inventories, total = await self.inventory_repo.get_inventories(
                page_num=page_num,
                page_size=page_size,
                product_id=params.product_id,
                variant_id=params.variant_id,
                status=params.status,
                warehouse_location=params.warehouse_location,
                supplier_id=params.supplier_id,
                low_stock_only=params.low_stock_only,
                expired_only=params.expired_only,
                order_by=params.order_by,
                order_direction=params.order_direction
            )
            
            # 构建库存响应列表
            inventory_responses = []
            for inventory in inventories:
                inventory_response = InventoryResponse.model_validate(inventory.to_dict())
                inventory_responses.append(inventory_response)
                
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = InventoryListResponse(
                items=inventory_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取库存列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取库存列表失败: {str(e)}"
            )
    
    async def create_inventory(self, params: InventoryCreate) -> Result[InventoryResponse]:
        """创建库存记录"""
        try:
            self.logger.info(f"创建库存: product_id={params.product_id}, quantity={params.quantity}")
            
            # 创建库存
            inventory = await self.inventory_repo.create(**params.model_dump())
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存创建事件
            event_data = inventory_response.model_dump()
            dispatch("products:inventory:created", payload=event_data)
            
            self.logger.info(f"库存创建成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"创建库存失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建库存失败: {str(e)}"
            )
    
    async def adjust_inventory(
        self, 
        inventory_id: int, 
        params: InventoryAdjustmentRequest
    ) -> Result[InventoryResponse]:
        """调整库存数量"""
        try:
            self.logger.info(f"调整库存: id={inventory_id}, type={params.adjustment_type}, quantity={params.quantity}")
            
            # 获取库存
            inventory = await self.get_resource_by_id(inventory_id)
            if not inventory:
                self.logger.warning(f"库存不存在: {inventory_id}")
                return self.resource_not_found_result(inventory_id)
            
            # 计算调整量
            quantity_change = params.quantity if params.adjustment_type == "increase" else -params.quantity
            
            # 调整库存
            inventory = await self.inventory_repo.adjust_inventory(
                inventory_id, 
                quantity_change, 
                params.reason
            )
            
            if not inventory:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="库存调整失败"
                )
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存调整事件
            event_data = inventory_response.model_dump()
            event_data.update({
                "adjustment_type": params.adjustment_type,
                "adjustment_quantity": params.quantity,
                "reason": params.reason
            })
            dispatch("products:inventory:adjusted", payload=event_data)
            
            self.logger.info(f"库存调整成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"调整库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"调整库存失败: {str(e)}"
            )
    
    async def reserve_inventory(
        self, 
        inventory_id: int, 
        params: InventoryReservationRequest
    ) -> Result[InventoryResponse]:
        """预留库存"""
        try:
            self.logger.info(f"预留库存: id={inventory_id}, quantity={params.quantity}")
            
            # 预留库存
            inventory = await self.inventory_repo.reserve_inventory(inventory_id, params.quantity)
            
            if not inventory:
                return self.create_error_result(
                    error_code=ErrorCode.OPERATION_FAILED,
                    error_message="库存预留失败，可能库存不足"
                )
            
            # 构建库存响应
            inventory_response = InventoryResponse.model_validate(inventory.to_dict())
            
            # 触发库存预留事件
            event_data = inventory_response.model_dump()
            event_data.update({
                "reserved_quantity": params.quantity,
                "order_id": params.order_id
            })
            dispatch("products:inventory:reserved", payload=event_data)
            
            self.logger.info(f"库存预留成功: id={inventory.id}")
            return self.create_success_result(inventory_response)
        except Exception as e:
            self.logger.error(f"预留库存失败: id={inventory_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"预留库存失败: {str(e)}"
            )
    
    async def batch_update_inventories(self, request: InventoryBatchUpdateRequest) -> Result[InventoryBatchUpdateResponse]:
        """批量更新库存"""
        self.logger.info(f"批量更新库存: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.inventory_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_inventories(self, request: InventoryBatchDeleteRequest) -> Result[InventoryBatchDeleteResponse]:
        """批量删除库存"""
        self.logger.info(f"批量删除库存: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.inventory_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_adjust_inventories(self, request: InventoryBatchAdjustmentRequest) -> Result[InventoryBatchAdjustmentResponse]:
        """批量调整库存
        
        Args:
            request: 批量调整请求
            
        Returns:
            Result[InventoryBatchAdjustmentResponse]: 批量调整结果
        """
        try:
            self.logger.info(f"批量调整库存: ids={request.inventory_ids}, type={request.adjustment_type}, quantity={request.quantity}")
            
            adjusted_count = 0
            failed_ids = []
            total_quantity_changed = 0
            
            for inventory_id in request.inventory_ids:
                try:
                    # 获取库存记录
                    inventory = await self.inventory_repo.get_by_id(inventory_id)
                    if not inventory:
                        failed_ids.append(inventory_id)
                        continue
                    
                    # 计算新的库存数量
                    if request.adjustment_type == "increase":
                        new_quantity = inventory.quantity + request.quantity
                        quantity_change = request.quantity
                    else:  # decrease
                        new_quantity = max(0, inventory.quantity - request.quantity)
                        quantity_change = -(inventory.quantity - new_quantity)
                    
                    # 更新库存
                    update_data = {
                        "quantity": new_quantity,
                        "available_quantity": new_quantity - inventory.reserved_quantity
                    }
                    
                    updated_inventory = await self.inventory_repo.update(inventory, update_data)
                    if updated_inventory:
                        adjusted_count += 1
                        total_quantity_changed += abs(quantity_change)
                        
                        # 清理缓存
                        cache_key = self._get_resource_cache_key(inventory_id)
                        await self.delete_cache(cache_key)
                        
                        # 触发事件
                        dispatch("products:inventory:adjusted", payload={
                            "id": inventory_id,
                            "old_quantity": inventory.quantity,
                            "new_quantity": new_quantity,
                            "quantity_change": quantity_change,
                            "reason": request.reason,
                            "adjustment_type": request.adjustment_type
                        })
                    else:
                        failed_ids.append(inventory_id)
                        
                except Exception as e:
                    self.logger.error(f"调整库存失败: id={inventory_id}, 错误={str(e)}")
                    failed_ids.append(inventory_id)
            
            # 构建响应
            response_data = InventoryBatchAdjustmentResponse(
                adjusted_count=adjusted_count,
                failed_ids=failed_ids,
                total_requested=len(request.inventory_ids),
                success_rate=adjusted_count / len(request.inventory_ids) if request.inventory_ids else 0.0,
                details={"total_quantity_changed": total_quantity_changed}
            )
            
            self.logger.info(f"批量调整库存完成: 成功={adjusted_count}, 失败={len(failed_ids)}")
            return self.create_success_result(response_data)
            
        except Exception as e:
            self.logger.error(f"批量调整库存失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量调整库存失败: {str(e)}"
            )
