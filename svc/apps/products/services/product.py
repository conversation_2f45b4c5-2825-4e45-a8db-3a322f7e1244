from typing import Any, Dict, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.schemas.album import AlbumCreate, UserAlbumResponse
from svc.apps.albums.services.album import AlbumService
from svc.apps.products.models.product import Product, ProductStatus
from svc.apps.products.models.sku import ProductSKU
from svc.apps.products.models.spec import Spec, SpecOption
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.products.repositories.sku import ProductSKURepository
from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                 SpecRepository)
from svc.apps.products.schemas.product import (GetProductsParams,
                                               ProductBatchDeleteRequest,
                                               ProductBatchDeleteResponse,
                                               ProductBatchUpdateRequest,
                                               ProductBatchUpdateResponse,
                                               ProductCreate,
                                               ProductListResponse,
                                               ProductResponse, ProductUpdate,
                                               UserProductListResponse,
                                               UserProductResponse,
                                               UserProductSKU, UserProductSpec)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services import BaseService, BatchOperationMixin, ServiceConfig

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）

class ProductService(BaseService,BatchOperationMixin):
    """商品服务类，提供商品的创建、查询和管理功能
    
    该服务类负责：
    1. 商品的创建和管理
    2. 商品状态更新
    3. 商品数据统计
    4. 商品库存管理
    
    服务类依赖ProductRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "product"
    
    def __init__(
        self,
        product_repo: ProductRepository,
        album_service: Optional[AlbumService] = None,
        spec_repo: Optional[SpecRepository] = None,
        spec_option_repo: Optional[SpecOptionRepository] = None,
        sku_repo: Optional[ProductSKURepository] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化商品服务

        Args:
            product_repo: 商品仓库实例
            album_service: 图册服务实例
            spec_repo: 规格仓库实例
            spec_option_repo: 规格选项仓库实例
            sku_repo: SKU仓库实例
            redis: Redis客户端，用于缓存和分布式锁
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="product",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(
            product_repo,
            config,
            redis,
            album_service=album_service,
            spec_repo=spec_repo,
            spec_option_repo=spec_option_repo,
            sku_repo=sku_repo,
            **kwargs
        )

        self.product_repo = product_repo
        self.album_service = album_service
        self.spec_repo = spec_repo
        self.spec_option_repo = spec_option_repo
        self.sku_repo = sku_repo
    
    async def get_resource_by_id(self, product_id: int, include_deleted: bool = False) -> Optional[Product]:
        """获取指定ID的商品资源
        
        Args:
            product_id: 商品ID
            include_deleted: 是否包含已删除记录
            
        Returns:
            Optional[Product]: 商品对象，不存在时返回None
        """
        return await self.product_repo.get_by_id(product_id, include_deleted=include_deleted)
    
    async def get_product(self, product_id: int, user_mode: bool = False, include_deleted: bool = False):
        """获取商品信息，user_mode=True时返回用户端精简schema"""
        try:
            self.logger.info(f"获取商品: id={product_id}")
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(product_id)
                    if user_mode:
                        cached_product = await self.get_cached_resource(
                            cache_key,
                            lambda data: UserProductResponse.model_validate(data)
                        )
                    else:
                        cached_product = await self.get_cached_resource(
                            cache_key,
                            lambda data: ProductResponse.model_validate(data)
                        )
                    if cached_product:
                        self.logger.debug(f"从缓存获取到商品: id={product_id}")
                        return self.create_success_result(cached_product)
                except Exception as e:
                    self.logger.warning(f"从缓存获取商品失败: id={product_id}, 错误={str(e)}")
            product = await self.get_resource_by_id(product_id, include_deleted=include_deleted)
            if not product:
                self.logger.warning(f"商品不存在: id={product_id}")
                return self.resource_not_found_result(product_id)
            if user_mode:
                album = None
                image_url = None
                if product.album:
                    image_url = product.album.cover_image.url if getattr(product.album, 'cover_image', None) else None
                    images = [img.url for img in getattr(product.album, 'images', [])] if hasattr(product.album, 'images') else []
                    album = UserAlbumResponse(
                        id=product.album.id,
                        name=product.album.name,
                        images=images
                    )
                user_product_response = UserProductResponse(
                    id=product.id,
                    name=product.name,
                    description=product.description,
                    short_description=getattr(product, 'short_description', None),
                    price=product.price,
                    currency=product.currency,
                    image_url=image_url,
                    album=album,
                    is_featured=product.is_featured,
                    attributes=getattr(product, 'attributes', {}),
                    rich_description=getattr(product, 'rich_description', None),
                    category_id=product.category_id,
                    specs=[],  # 规格等如有需要可补充
                    spec_combinations=[]
                )
                await self.cache_resource(self._get_resource_cache_key(product_id), user_product_response, CACHE_TTL)
                await self.product_repo.increment_field(product_id, "view_count")
                return self.create_success_result(user_product_response)
            product_response = ProductResponse.model_validate(product.to_dict())
            await self.cache_resource(self._get_resource_cache_key(product_id), product_response, CACHE_TTL)
            await self.product_repo.increment_field(product_id, "view_count")
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"获取商品失败: id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取商品失败: {str(e)}"
            )
    
    async def get_products(self, params: GetProductsParams, user_mode: bool = False) -> Result[ProductListResponse]:
        """获取商品列表，user_mode=True时返回用户端精简schema"""
        try:
            page_num = params.page_num
            page_size = params.page_size
            self.logger.info(f"获取商品列表: page={page_num}, size={page_size}")

            # 使用仓库的get_paginated方法
            filters = {}
            if params.status:
                filters["status"] = params.status
            if params.category_id:
                filters["category_id"] = params.category_id
            if params.is_featured is not None:
                filters["is_featured"] = params.is_featured
            if params.min_price is not None:
                filters["price__gte"] = params.min_price
            if params.max_price is not None:
                filters["price__lte"] = params.max_price
            if getattr(params, 'include_deleted', False):
                filters["deleted_at__isnull"] = True
            
            products, total = await self.product_repo.get_paginated(
                filters=filters,
                page_num=page_num,
                page_size=page_size,
                search_term=params.search_term,
                order_by=params.order_by,
                order_direction=params.order_direction
            )
            page_count = (total + page_size - 1) // page_size if page_size > 0 else 0
            if user_mode:
                user_products = []
                for product in products:
                    image_url = None
                    if product.album and getattr(product.album, 'cover_image', None):
                        image_url = product.album.cover_image.url
                    user_products.append(UserProductResponse(
                        id=product.id,
                        name=product.name,
                        description=product.description,
                        short_description=getattr(product, 'short_description', None),
                        price=product.price,
                        currency=product.currency,
                        image_url=image_url,
                        album=None,
                        is_featured=product.is_featured,
                        attributes=getattr(product, 'attributes', {}),
                        rich_description=getattr(product, 'rich_description', None),
                        category_id=product.category_id,
                        specs=[],  # 规格等如有需要可补充
                        spec_combinations=[]
                    ))
                return self.create_success_result(
                    UserProductListResponse(
                        items=user_products,
                        total=total,
                        page_num=page_num,
                        page_size=page_size,
                        page_count=page_count
                    )
                )
            # 管理端原有逻辑
            product_responses = []
            for product in products:
                product_response = ProductResponse.model_validate(product.to_dict())
                product_responses.append(product_response)
            paginated_response = ProductListResponse(
                items=product_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=page_count
            )
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取商品列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取商品列表失败: {str(e)}"
            )
    
    async def create_product(self, params: ProductCreate) -> Result[ProductResponse]:
        """创建商品, 并为其关联一个新图册"""
        try:
            self.logger.info(f"创建商品: name={params.name}, sku={params.sku}")
            
            # 检查SKU是否已存在
            existing_product = await self.product_repo.get_one(sku=params.sku)
            if existing_product:
                self.logger.warning(f"商品SKU已存在: {params.sku}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"商品SKU {params.sku} 已被使用"
                )
            
            # 1. 创建图册
            album_create_data = AlbumCreate(name=f"产品主图册 - {params.name}", tags=["product", "main"])
            album_result = await self.album_service.create_album(album_create_data)
            if not album_result.is_success:
                self.logger.error(f"为新产品创建主图册失败: {album_result.error_message}")
                return self.create_error_result(
                    error_code=ErrorCode.CREATE_FAILED,
                    error_message=f"创建关联图册失败: {album_result.error_message}"
                )
            created_album = album_result.data

            # 2. 创建商品并关联图册
            product_data = params.model_dump()
            product_data["album_id"] = created_album.id
            product = await self.product_repo.create(product_data)
            
            # 构建商品响应
            product_response = ProductResponse.model_validate(product.to_dict())
            
            # 缓存商品
            await self.cache_resource(self._get_resource_cache_key(product.id), product_response, CACHE_TTL)
            
            # 触发商品创建事件
            event_data = product_response.model_dump()
            dispatch("products:product:created", payload=event_data)
            
            self.logger.info(f"商品创建成功: id={product.id}, name={product.name}")
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"创建商品失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建商品失败: {str(e)}"
            )
    
    async def update_product(self, product_id: int, params: ProductUpdate) -> Result[ProductResponse]:
        """更新商品
        
        Args:
            product_id: 商品ID
            params: 商品更新参数
            
        Returns:
            Result[ProductResponse]: 结果对象
        """
        try:
            self.logger.info(f"更新商品: id={product_id}")
            
            # 获取商品
            product = await self.get_resource_by_id(product_id, include_deleted=False)
            if not product:
                self.logger.warning(f"商品不存在: {product_id}")
                return self.resource_not_found_result(product_id)
            
            # 如果更新SKU，检查是否已存在
            if params.sku is not None and params.sku != product.sku:
                existing_product = await self.product_repo.get_one(sku=params.sku)
                if existing_product and existing_product.id != product.id:
                    self.logger.warning(f"商品SKU已存在: {params.sku}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"商品SKU {params.sku} 已被使用"
                    )
            
            # 更新商品
            update_data = params.model_dump(exclude_unset=True)
            product = await self.product_repo.update(product, data=update_data)
            
            # 构建商品响应
            product_response = ProductResponse.model_validate(product.to_dict())
            
            # 缓存商品
            await self.cache_resource(self._get_resource_cache_key(product.id), product_response, CACHE_TTL)
            
            # 触发商品更新事件
            # await self._dispatch_product_updated_event(product, update_data)
            dispatch("products:product:updated", payload=product_response.model_dump())
            
            self.logger.info(f"商品更新成功: id={product.id}, name={product.name}")
            return self.create_success_result(product_response)
        except Exception as e:
            self.logger.error(f"更新商品失败: id={product_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"更新商品失败: {str(e)}"
            )
    
    async def delete_product(self, product_id: int, soft_delete: bool = True) -> Result[Dict[str, Any]]:
        """删除商品
        
        Args:
            product_id: 商品ID
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            self.logger.info(f"删除商品: id={product_id}, soft_delete={soft_delete}")
            # 软删除时只查找未删除的记录，硬删除时包含已删除的记录
            include_deleted = not soft_delete
            product_to_delete = await self.get_resource_by_id(product_id, include_deleted=include_deleted)
            if not product_to_delete:
                return self.resource_not_found_result(product_id)

            # 保存album_id用于日志记录
            album_id = product_to_delete.album_id

            if soft_delete:
                # 使用仓库的软删除方法
                await self.product_repo.soft_delete(product_to_delete)
                self.logger.info(f"商品软删除成功: id={product_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.product_repo.delete(product_to_delete, soft=False)
                self.logger.info(f"商品硬删除成功: id={product_id}")
            
            await self.delete_cache(self._get_resource_cache_key(product_id))
            dispatch(f"{self.resource_type}_deleted", payload={"id": product_id, "album_id": album_id, "soft_delete": soft_delete})
            
            return self.create_success_result({"id": product_id, "album_id": album_id, "soft_delete": soft_delete})
        except Exception as e:
            self.logger.error(f"删除商品失败: id={product_id}, soft_delete={soft_delete}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"删除商品失败: {str(e)}"
            )
    


    async def _get_user_product_specs(self, product_id: int, page_num: int = 1, page_size: int = 50) -> list:
        if not self.spec_repo or not self.spec_option_repo:
            return []
        specs, _ = await self.spec_repo.get_paginated(
            page_num=page_num,
            page_size=page_size,
            filters={"categories__has": product_id, "status__eq": "active"},
            order_by="sort_order",
            order_direction="asc"
        )
        result = []
        for spec in specs:
            options, _ = await self.spec_option_repo.get_paginated(
                page_num=1,
                page_size=100,
                spec_id=spec.id,
                status="active",
                order_by="sort_order",
                order_direction="asc"
            )
            option_list = [{"id": opt.id, "value": opt.value} for opt in options]
            result.append(UserProductSpec(id=spec.id, name=spec.name, options=option_list))
        return result

    async def _get_user_product_combinations(self, product_id: int, page_num: int = 1, page_size: int = 50) -> list:
        if not self.sku_repo or not self.spec_option_repo:
            return []
        combinations, _ = await self.sku_repo.get_paginated(
            page_num=page_num,
            page_size=page_size,
            product_id=product_id,
            status="active"
        )
        result = []
        for comb in combinations:
            option_values = []
            for oid in comb.spec_option_ids:
                opt = await self.spec_option_repo.get_one(id=oid)
                option_values.append(opt.value if opt else "")
            result.append(UserProductSKU(
                id=comb.id,
                sku=comb.sku,
                price=comb.price,
                stock_quantity=comb.stock_quantity,
                spec_option_ids=comb.spec_option_ids,
                spec_option_values=option_values,
                album_url=comb.album.url if comb.album and hasattr(comb.album, 'url') else None
            ))
        return result

    async def batch_update_products(self, request: ProductBatchUpdateRequest) -> Result[ProductBatchUpdateResponse]:    
        """批量更新商品"""
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.product_repo,
            resource_type="商品",
            event_prefix="products:product",
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_products(self, request: ProductBatchDeleteRequest) -> Result[ProductBatchDeleteResponse]:
        """批量删除商品"""
        self.logger.info(f"批量删除商品: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.product_repo,
            resource_type="商品",
            event_prefix="products:product",
            cache_key_generator=self._get_resource_cache_key
        )