"""
规格服务模块，提供面向对象形式的规格管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

from typing import Any, Dict, List, Optional

from redis.asyncio import Redis

from svc.apps.products.repositories.spec import (SpecOptionRepository,
                                                 SpecRepository)
from svc.apps.products.schemas.spec import (CreateSpecOptionParams,
                                            CreateSpecParams,
                                            DeleteSpecOptionParams,
                                            DeleteSpecParams,
                                            GetSpecOptionParams,
                                            GetSpecOptionsParams,
                                            GetSpecParams, GetSpecsParams,
                                            SpecBatchDeleteRequest,
                                            SpecBatchDeleteResponse,
                                            SpecBatchUpdateRequest,
                                            SpecBatchUpdateResponse,
                                            SpecListResponse,
                                            SpecOptionBatchDeleteRequest,
                                            SpecOptionBatchDeleteResponse,
                                            SpecOptionBatchUpdateRequest,
                                            SpecOptionBatchUpdateResponse,
                                            SpecOptionListResponse,
                                            SpecOptionResponse, SpecResponse,
                                            UpdateSpecOptionParams,
                                            UpdateSpecParams)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin

# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

# Add new event imports
from fastapi_events.dispatcher import dispatch

from svc.core.events.event_names import (PRODUCT_SPEC_CREATED,
                                         PRODUCT_SPEC_DELETED,
                                         PRODUCT_SPEC_OPTION_CREATED,
                                         PRODUCT_SPEC_OPTION_DELETED,
                                         PRODUCT_SPEC_OPTION_UPDATED,
                                         PRODUCT_SPEC_UPDATED)


class SpecService(BaseService, BatchOperationMixin):
    """规格服务类，提供规格的创建、查询和管理功能
    
    该服务类负责：
    1. 规格的创建和管理
    2. 规格状态更新
    3. 规格与分类的关联管理
    4. 规格的缓存管理
    
    服务类依赖SpecRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "spec"
    
    def __init__(
        self,
        spec_repo: SpecRepository,
        spec_option_repo: Optional[SpecOptionRepository] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化规格服务

        Args:
            spec_repo: 规格仓库实例
            spec_option_repo: 规格选项仓库实例
            redis: Redis客户端，用于缓存和分布式锁
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="spec",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(spec_repo, config, redis, **kwargs)
        self.spec_repo = spec_repo
        self.spec_option_repo = spec_option_repo
        
    # === 缓存相关方法 ===

    async def _get_cached_spec(self, spec_id: int) -> Optional[SpecResponse]:
        """从缓存获取规格详情"""
        if not self.redis:
            return None

        cache_key = self._get_resource_cache_key(spec_id)
        return await self.get_cached_resource(
            cache_key,
            lambda data: SpecResponse.model_validate(data)
        )

    async def _invalidate_spec_options_cache(self, spec_id: int) -> None:
        """清除规格选项集合缓存"""
        if not self.redis:
            return

        # 清除规格选项集合缓存
        collection_key = f"spec_options:v1:spec:{spec_id}"
        await self.delete_cache(collection_key)

    # === 核心业务方法 ===
    
    async def create_spec(self, params: CreateSpecParams) -> Result[SpecResponse]:
        """创建规格"""
        try:
            self.logger.info(f"创建规格: name={params.name}")
            
            # 检查规格名称是否已存在
            existing_spec = await self.spec_repo.get_one(name=params.name)
            if existing_spec:
                self.logger.warning(f"规格名称已存在: {params.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"规格名称 '{params.name}' 已存在"
                )
            
            # 创建规格
            spec_data = params.model_dump()
            spec = await self.spec_repo.create(spec_data)
            
            # 构建响应
            spec_response = SpecResponse.model_validate(spec.to_dict())
            
            # 缓存规格
            await self.cache_resource(self._get_resource_cache_key(spec.id), spec_response, 3600)
            
            # 触发规格创建事件
            event_data = spec_response.model_dump()
            dispatch(PRODUCT_SPEC_CREATED, payload=event_data)
            
            self.logger.info(f"规格创建成功: id={spec.id}, name={spec.name}")
            return self.create_success_result(spec_response, status_code=201)
            
        except Exception as e:
            self.logger.error(f"创建规格失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建规格失败: {str(e)}"
            )
    
    async def get_spec(self, params: GetSpecParams) -> Result[SpecResponse]:
        """获取规格详情"""
        try:
            self.logger.info(f"获取规格详情: id={params.spec_id}")
            
            # 先尝试从缓存获取
            cached_spec = await self._get_cached_spec(params.spec_id)
            if cached_spec:
                self.logger.debug(f"从缓存获取到规格: id={params.spec_id}")
                return self.create_success_result(cached_spec)
            
            # 从数据库获取
            spec = await self.spec_repo.get_by_id(params.spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={params.spec_id}")
                return self.resource_not_found_result(params.spec_id)
            
            # 构建响应并缓存
            spec_response = SpecResponse.model_validate(spec.to_dict())
            await self.cache_resource(self._get_resource_cache_key(spec.id), spec_response, 3600)
            
            self.logger.info(f"获取规格详情成功: id={params.spec_id}")
            return self.create_success_result(spec_response)
            
        except Exception as e:
            self.logger.error(f"获取规格详情失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格详情失败: {str(e)}"
            )
    
    async def update_spec(self, params: UpdateSpecParams) -> Result[SpecResponse]:
        """更新规格"""
        try:
            self.logger.info(f"更新规格: id={params.spec_id}")
            
            # 检查规格是否存在
            spec = await self.spec_repo.get_by_id(params.spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={params.spec_id}")
                return self.resource_not_found_result(params.spec_id)
            
            # 如果更新名称，检查是否重复
            if params.name and params.name != spec.name:
                existing_spec = await self.spec_repo.get_one(name=params.name)
                if existing_spec:
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"规格名称 '{params.name}' 已存在"
                    )
            
            # 更新规格
            update_data = {k: v for k, v in params.model_dump().items() if v is not None}
            updated_spec = await self.spec_repo.update(spec, update_data)
            
            # 构建响应
            spec_response = SpecResponse.model_validate(updated_spec.to_dict())
            
            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(params.spec_id), spec_response, 3600)
            
            # 触发规格更新事件
            event_data = spec_response.model_dump()
            dispatch(PRODUCT_SPEC_UPDATED, payload=event_data)
            
            self.logger.info(f"规格更新成功: id={params.spec_id}")
            return self.create_success_result(spec_response)
            
        except Exception as e:
            self.logger.error(f"更新规格失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新规格失败: {str(e)}"
            )
    
    async def delete_spec(self, params: DeleteSpecParams, soft_delete: bool = True) -> Result[Dict[str, Any]]:
        """删除规格
        
        Args:
            params: 删除请求参数
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            self.logger.info(f"删除规格: id={params.spec_id}, soft_delete={soft_delete}")
            
            # 检查规格是否存在
            spec = await self.spec_repo.get_by_id(params.spec_id)
            if not spec:
                self.logger.warning(f"规格不存在: id={params.spec_id}")
                return self.resource_not_found_result(params.spec_id)
            
            if soft_delete:
                # 软删除：更新状态为DELETED
                update_data = {"status": "deleted"}
                await self.spec_repo.update(spec, update_data)
                self.logger.info(f"规格软删除成功: id={params.spec_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.spec_repo.delete(spec)
                self.logger.info(f"规格硬删除成功: id={params.spec_id}")
            
            # 清除缓存
            await self.delete_cache(self._get_resource_cache_key(params.spec_id))
            
            # 触发规格删除事件
            event_data = {"spec_id": params.spec_id, "soft_delete": soft_delete}
            dispatch(PRODUCT_SPEC_DELETED, payload=event_data)
            
            self.logger.info(f"规格删除成功: id={params.spec_id}")
            return self.create_success_result({"message": "规格删除成功", "soft_delete": soft_delete})
            
        except Exception as e:
            self.logger.error(f"删除规格失败: id={params.spec_id}, soft_delete={soft_delete}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除规格失败: {str(e)}"
            )
    
    async def get_specs_list(
        self,
        params: GetSpecsParams
    ) -> Result[SpecListResponse]:
        """获取规格列表"""
        try:
            self.logger.info(f"获取规格列表: page={params.page_num}, size={params.page_size}, status={params.status}, search_term={params.search_term}, category_id={params.category_id}")

            # 构建查询条件
            filters = {}
            if params.status:
                filters["status"] = params.status
            if params.search_term:
                filters["name__ilike"] = f"%{params.search_term}%"
            if params.category_id:
                filters["categories__has"] = params.category_id

            # 使用统一的分页方法（仓库层会自动加载关系）
            specs, total = await self.spec_repo.get_paginated(
                page_num=params.page_num,
                page_size=params.page_size,
                order_by="sort_order",
                order_direction="asc",
                filters=filters
            )

            # 构建响应，包含规格选项
            spec_responses = []
            for spec in specs:
                # 规格选项已经通过默认关系加载，无需额外查询
                options = []
                if hasattr(spec, 'options') and spec.options:
                    options = [SpecOptionResponse.model_validate(option.to_dict()) for option in spec.options]

                # 构建包含分类信息和选项的规格响应
                spec_dict = spec.to_dict()
                spec_dict["category_id"] = params.category_id
                spec_dict["options"] = options
                spec_responses.append(SpecResponse.model_validate(spec_dict))

            # 计算总页数
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0

            # 构建分页响应
            response = SpecListResponse(
                items=spec_responses,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )

            self.logger.info(f"获取规格列表成功: count={len(specs)}, total={total}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取规格列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格列表失败: {str(e)}"
            )
    
    async def batch_update_specs(self, request: SpecBatchUpdateRequest) -> Result[SpecBatchUpdateResponse]:
        """批量更新规格"""
        self.logger.info(f"批量更新规格: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.spec_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )
    
    async def batch_delete_specs(self, request: SpecBatchDeleteRequest) -> Result[SpecBatchDeleteResponse]:
        """批量删除规格"""
        self.logger.info(f"批量删除规格: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.spec_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )


class SpecOptionService(BaseService, BatchOperationMixin):
    """规格选项服务类，提供规格选项的创建、查询和管理功能

    该服务类负责：
    1. 规格选项的创建和管理
    2. 规格选项状态更新
    3. 规格选项与SKU的关联管理
    4. 规格选项的缓存管理

    服务类依赖SpecOptionRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """

    # 设置资源类型名称
    resource_type = "spec_option"

    def __init__(
        self,
        spec_option_repo: SpecOptionRepository,
        spec_repo: Optional[SpecRepository] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化规格选项服务

        Args:
            spec_option_repo: 规格选项仓库实例
            spec_repo: 规格仓库实例，用于验证规格是否存在
            redis: Redis客户端，用于缓存和分布式锁
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="spec_option",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(spec_option_repo, config, redis, **kwargs)

        self.spec_option_repo = spec_option_repo
        self.spec_repo = spec_repo

    # === 缓存相关方法 ===

    async def _get_cached_spec_option(self, option_id: int) -> Optional[SpecOptionResponse]:
        """从缓存获取规格选项详情"""
        if not self.redis:
            return None

        cache_key = self._get_resource_cache_key(option_id)
        return await self.get_cached_resource(
            cache_key,
            lambda data: SpecOptionResponse.model_validate(data)
        )

    async def _invalidate_spec_option_cache(self, option_id: int) -> None:
        """清除单个规格选项缓存"""
        if not self.redis:
            return

        cache_key = self._get_resource_cache_key(option_id)
        await self.delete_cache(cache_key)

    async def _invalidate_spec_options_cache(self, spec_id: int) -> None:
        """清除规格选项集合缓存"""
        if not self.redis:
            return

        # 清除规格选项集合缓存
        collection_key = f"spec_options:v1:spec:{spec_id}"
        await self.delete_cache(collection_key)

    # === 核心业务方法 ===

    async def create_spec_option(self, params: CreateSpecOptionParams) -> Result[SpecOptionResponse]:
        """创建规格选项"""
        try:
            self.logger.info(f"创建规格选项: spec_id={params.spec_id}, value={params.value}")

            # 验证规格是否存在
            if self.spec_repo:
                spec = await self.spec_repo.get_by_id(params.spec_id)
                if not spec:
                    return self.create_error_result(
                        error_code=ErrorCode.NOT_FOUND,
                        error_message=f"规格 {params.spec_id} 不存在"
                    )

            # 检查规格选项值是否已存在
            existing_options, _ = await self.spec_option_repo.get_paginated(
                filters={"spec_id": params.spec_id, "value": params.value}
            )
            if existing_options:
                self.logger.warning(f"规格选项值已存在: spec_id={params.spec_id}, value={params.value}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"规格选项值 '{params.value}' 已存在"
                )

            # 创建规格选项
            option_data = params.model_dump()
            option = await self.spec_option_repo.create(option_data)

            # 构建响应
            option_response = SpecOptionResponse.model_validate(option.to_dict())

            # 缓存规格选项
            await self.cache_resource(self._get_resource_cache_key(option.id), option_response, 3600)

            # 清除相关缓存
            await self._invalidate_spec_options_cache(params.spec_id)

            # 触发规格选项创建事件
            event_data = option_response.model_dump()
            dispatch(PRODUCT_SPEC_OPTION_CREATED, payload=event_data)

            self.logger.info(f"规格选项创建成功: id={option.id}, value={option.value}")
            return self.create_success_result(option_response, status_code=201)

        except Exception as e:
            self.logger.error(f"创建规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建规格选项失败: {str(e)}"
            )

    async def get_spec_option(self, params: GetSpecOptionParams) -> Result[SpecOptionResponse]:
        """获取规格选项详情"""
        try:
            self.logger.info(f"获取规格选项详情: id={params.option_id}")

            # 先尝试从缓存获取
            cached_option = await self._get_cached_spec_option(params.option_id)
            if cached_option:
                self.logger.debug(f"从缓存获取到规格选项: id={params.option_id}")
                return self.create_success_result(cached_option)

            # 从数据库获取
            option = await self.spec_option_repo.get_by_id(params.option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={params.option_id}")
                return self.resource_not_found_result(params.option_id)

            # 构建响应并缓存
            option_response = SpecOptionResponse.model_validate(option.to_dict())
            await self.cache_resource(self._get_resource_cache_key(option.id), option_response, 3600)

            self.logger.info(f"获取规格选项详情成功: id={params.option_id}")
            return self.create_success_result(option_response)

        except Exception as e:
            self.logger.error(f"获取规格选项详情失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格选项详情失败: {str(e)}"
            )

    async def update_spec_option(self, params: UpdateSpecOptionParams) -> Result[SpecOptionResponse]:
        """更新规格选项"""
        try:
            self.logger.info(f"更新规格选项: id={params.option_id}")

            # 检查规格选项是否存在
            option = await self.spec_option_repo.get_by_id(params.option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={params.option_id}")
                return self.resource_not_found_result(params.option_id)

            # 如果更新值，检查是否重复
            if params.value and params.value != option.value:
                existing_options, _ = await self.spec_option_repo.get_paginated(
                    filters={"spec_id": option.spec_id, "value": params.value}
                )
                if existing_options and any(opt.id != params.option_id for opt in existing_options):
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"规格选项值 '{params.value}' 已存在"
                    )

            # 更新规格选项
            update_data = {k: v for k, v in params.model_dump().items() if v is not None}
            updated_option = await self.spec_option_repo.update(option, update_data)

            # 构建响应
            option_response = SpecOptionResponse.model_validate(updated_option.to_dict())

            # 更新缓存
            await self.cache_resource(self._get_resource_cache_key(params.option_id), option_response, 3600)

            # 清除相关缓存
            await self._invalidate_spec_options_cache(updated_option.spec_id)

            # 触发规格选项更新事件
            event_data = option_response.model_dump()
            dispatch(PRODUCT_SPEC_OPTION_UPDATED, payload=event_data)

            self.logger.info(f"规格选项更新成功: id={params.option_id}")
            return self.create_success_result(option_response)

        except Exception as e:
            self.logger.error(f"更新规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新规格选项失败: {str(e)}"
            )

    async def delete_spec_option(self, params: DeleteSpecOptionParams, soft_delete: bool = True) -> Result[Dict[str, Any]]:
        """删除规格选项
        
        Args:
            params: 删除请求参数
            soft_delete: 是否软删除，默认为True
            
        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            self.logger.info(f"删除规格选项: id={params.option_id}, soft_delete={soft_delete}")
            
            # 检查规格选项是否存在
            option = await self.spec_option_repo.get_by_id(params.option_id)
            if not option:
                self.logger.warning(f"规格选项不存在: id={params.option_id}")
                return self.resource_not_found_result(params.option_id)
            
            if soft_delete:
                # 软删除：更新状态为DELETED
                update_data = {"status": "deleted"}
                await self.spec_option_repo.update(option, update_data)
                self.logger.info(f"规格选项软删除成功: id={params.option_id}")
            else:
                # 硬删除：直接从数据库删除
                await self.spec_option_repo.delete(option)
                self.logger.info(f"规格选项硬删除成功: id={params.option_id}")
            
            # 清除缓存
            await self._invalidate_spec_option_cache(params.option_id)
            await self._invalidate_spec_options_cache(option.spec_id)
            
            # 触发规格选项删除事件
            event_data = {"option_id": params.option_id, "spec_id": option.spec_id, "soft_delete": soft_delete}
            dispatch(PRODUCT_SPEC_OPTION_DELETED, payload=event_data)
            
            self.logger.info(f"规格选项删除成功: id={params.option_id}")
            return self.create_success_result({"message": "规格选项删除成功", "soft_delete": soft_delete})
            
        except Exception as e:
            self.logger.error(f"删除规格选项失败: id={params.option_id}, soft_delete={soft_delete}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除规格选项失败: {str(e)}"
            )

    async def get_options_by_spec(
        self,
        params: GetSpecOptionsParams
    ) -> Result[SpecOptionListResponse]:
        """获取规格的所有选项"""
        try:
            self.logger.info(f"获取规格选项列表: spec_id={params.spec_id}, page={params.page_num}, size={params.page_size}")

            # 使用分页方法获取规格选项
            options, total = await self.spec_option_repo.get_paginated(
                filters={"spec_id": params.spec_id},
                page_num=params.page_num,
                page_size=params.page_size,
                order_by="sort_order",
                order_direction="asc"
            )

            # 构建响应
            option_responses = [SpecOptionResponse.model_validate(option.to_dict()) for option in options]

            # 计算总页数
            page_count = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0

            # 构建分页响应
            response = SpecOptionListResponse(
                items=option_responses,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=page_count
            )

            self.logger.info(f"获取规格选项列表成功: spec_id={params.spec_id}, count={len(options)}, total={total}")
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取规格选项列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取规格选项列表失败: {str(e)}"
            )

    async def batch_create_spec_options(self, options_data: List[CreateSpecOptionParams]) -> Result[List[SpecOptionResponse]]:
        """批量创建规格选项"""
        try:
            self.logger.info(f"批量创建规格选项: count={len(options_data)}")

            created_options = []
            errors = []

            for i, option_data in enumerate(options_data):
                try:
                    result = await self.create_spec_option(option_data)
                    if result.success:
                        created_options.append(result.data)
                    else:
                        errors.append(f"第{i+1}个规格选项创建失败: {result.message}")
                except Exception as e:
                    errors.append(f"第{i+1}个规格选项创建异常: {str(e)}")

            if errors:
                self.logger.warning(f"批量创建规格选项部分失败: {errors}")
                return self.create_error_result(
                    error_code=ErrorCode.PARTIAL_SUCCESS,
                    error_message=f"部分规格选项创建失败: {'; '.join(errors)}",
                    data={"created": created_options, "errors": errors}
                )

            self.logger.info(f"批量创建规格选项成功: count={len(created_options)}")
            return self.create_success_result(created_options, status_code=201)

        except Exception as e:
            self.logger.error(f"批量创建规格选项失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量创建规格选项失败: {str(e)}"
            )
    
    async def batch_update_spec_options(self, request: SpecOptionBatchUpdateRequest) -> Result[SpecOptionBatchUpdateResponse]:
        """批量更新规格选项
        
        Args:
            request: 批量更新请求
            
        Returns:
            Result[SpecOptionBatchUpdateResponse]: 批量更新结果
        """
        try:
            self.logger.info(f"批量更新规格选项: ids={request.resource_ids}")
            
            # 提取更新数据
            resource_ids = request.resource_ids
            update_data = request.update_data.model_dump(exclude_unset=True)
            
            # 调用批量更新方法，直接返回结果，避免双重包装
            return await self.batch_update_resources(
                resource_ids=resource_ids,
                update_data=update_data,
                repository=self.spec_option_repo,
                resource_type=self.resource_type,
                cache_key_generator=self._get_resource_cache_key
            )
        except Exception as e:
            self.logger.error(f"批量更新规格选项失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量更新规格选项失败: {str(e)}"
            )
    
    async def batch_delete_spec_options(self, request: SpecOptionBatchDeleteRequest) -> Result[SpecOptionBatchDeleteResponse]:
        """批量删除规格选项
        
        Args:
            request: 批量删除请求
            
        Returns:
            Result[SpecOptionBatchDeleteResponse]: 批量删除结果
        """
        try:
            self.logger.info(f"批量删除规格选项: ids={request.resource_ids}, soft_delete={request.soft_delete}")
            
            # 调用批量删除方法，直接返回结果，避免双重包装
            return await self.batch_delete_resources(
                resource_ids=request.resource_ids,
                soft_delete=request.soft_delete,
                repository=self.spec_option_repo,
                resource_type=self.resource_type,
                cache_key_generator=self._get_resource_cache_key
            )
        except Exception as e:
            self.logger.error(f"批量删除规格选项失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"批量删除规格选项失败: {str(e)}"
            )
                
