"""
门店模型，定义门店的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.orm import relationship
from typing import Optional, Dict

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.apps.albums.models.album import Album

# 门店状态枚举
class ShopStatus(str, enum.Enum):
    OPEN = "open"          # 营业中
    CLOSED = "closed"      # 暂停营业
    PERMANENTLY_CLOSED = "permanently_closed"  # 永久关闭
    COMING_SOON = "coming_soon" # 即将开业
    DELETED = "deleted"    # 已删除

class Shop(Base, ResourceMixin):
    """
    门店模型，定义门店的基本属性和规则
    """
    __tablename__ = "shops"
    __resource_type__ = "shop"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="门店名称")
    description = Column(Text, nullable=True, comment="门店描述")
    address_line1 = Column(String(255), nullable=False, comment="地址行1")
    address_line2 = Column(String(255), nullable=True, comment="地址行2")
    city = Column(String(100), nullable=False, comment="城市")
    state_province = Column(String(100), nullable=True, comment="州/省")
    postal_code = Column(String(20), nullable=False, comment="邮政编码")
    country = Column(String(50), nullable=False, default="CN", comment="国家")
    phone_number = Column(String(50), nullable=True, comment="联系电话")
    email = Column(String(255), nullable=True, comment="电子邮箱")
    website = Column(String(255), nullable=True, comment="网站")

    # 营业时间，可以使用 JSON 存储，例如：{"Mon": "9am-5pm", "Tue": "9am-5pm"}
    opening_hours = Column(DatabaseCompatibleJSON, nullable=True, comment="营业时间")

    # 门店状态
    status = Column(String(50), nullable=False, default=ShopStatus.COMING_SOON, comment="门店状态")
    is_franchise = Column(Boolean, default=False, comment="是否为加盟店")

    # 地理位置信息
    latitude = Column(String(50), nullable=True, comment="纬度")
    longitude = Column(String(50), nullable=True, comment="经度")

    # 关联图册
    album_id = Column(BigInteger, ForeignKey("albums.id", ondelete="SET NULL"), nullable=True, unique=True, comment="关联图册ID")

    created_at = Column(DateTime, default=get_utc_now_without_tzinfo, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo, nullable=False, comment="更新时间")

    # 扩展字段，用于存储其他自定义信息
    extra_info = Column(DatabaseCompatibleJSON, nullable=True, comment="扩展信息")

    # 关系
    album = relationship("Album", lazy="selectin", uselist=False, foreign_keys=[album_id])

    def __repr__(self):
        return f"<Shop(id={self.id}, name='{self.name}')>"

    def to_dict(self) -> Dict:
        """将门店对象转换为字典，包含关联图册信息"""
        shop_dict = {c.name: getattr(self, c.name) for c in self.__table__.columns if c.name not in ['created_at', 'updated_at']}
        shop_dict['created_at'] = self.created_at.isoformat() if self.created_at else None
        shop_dict['updated_at'] = self.updated_at.isoformat() if self.updated_at else None
        
        if self.album:
            # 复用 album 模型的 to_dict 方法
            shop_dict['album'] = self.album.to_dict()
        else:
            shop_dict['album'] = None
            
        return shop_dict