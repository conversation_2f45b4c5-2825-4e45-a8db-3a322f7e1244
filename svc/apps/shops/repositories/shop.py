"""
店铺数据访问层。
负责店铺模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import Optional, List, Tuple, Any

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from svc.core.repositories import BaseRepository
from svc.apps.shops.models.shop import Shop, ShopStatus
from svc.apps.shops.schemas.shop import ShopCreate, ShopUpdate, GetShopsParams


class ShopRepository(BaseRepository[Shop, ShopCreate, ShopUpdate]):
    """店铺仓库类，提供店铺数据访问方法"""
    
    def __init__(self, db: AsyncSession):
        """初始化店铺仓库"""
        super().__init__(db, Shop)

    def _get_default_load_options(self) -> List[Any]:
        """获取店铺模型的默认关系加载选项
        
        默认加载：
        - album: 店铺图册（selectin关系）
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        return [
            selectinload(Shop.album)
        ]



    async def get_shops(self, params: GetShopsParams, load_options: Optional[List[Any]] = None) -> Tuple[List[Shop], int]:
        """获取店铺列表及总数"""
        # 构建过滤条件
        filters = {}
        if params.status:
            filters["status"] = params.status
        if params.city:
            filters["city__ilike"] = f"%{params.city}%"
        if params.country:
            filters["country"] = params.country
        if params.is_franchise is not None:
            filters["is_franchise"] = params.is_franchise
        
        # 构建搜索条件（使用 __or__ 分组以复用基类高级过滤）
        if params.search_term:
            or_group = {
                "name__ilike": f"%{params.search_term}%",
                "address_line1__ilike": f"%{params.search_term}%",
                "description__ilike": f"%{params.search_term}%",
            }
            filters["__or__"] = or_group

        # 使用基类的 get_paginated 方法，统一分页参数命名
        shops, total = await self.get_paginated(
            page_num=params.page_num,
            page_size=params.page_size,
            order_by="created_at",
            order_direction="desc",
            filters=filters,
            load_options=load_options,
        )
        
        return shops, total




    async def get_active_shops(
        self, 
        page_num: int = 1,
        page_size: int = 100,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Shop], int]:
        """获取活跃的店铺（分页）"""
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            status=ShopStatus.ACTIVE,
            order_by="created_at",
            order_direction="desc",
            load_options=load_options
        )
        return items, total

    async def get_shops_by_city(
        self, 
        city: str, 
        page_num: int = 1,
        page_size: int = 50,
        load_options: Optional[List[Any]] = None
    ) -> Tuple[List[Shop], int]:
        """根据城市获取店铺（分页）"""
        items, total = await self.get_paginated(
            page_num=page_num,
            page_size=page_size,
            city=city,
            status=ShopStatus.ACTIVE,
            order_by="created_at",
            order_direction="desc",
            load_options=load_options
        )
        return items, total

    async def search_shops(
        self,
        search_term: str,
        limit: int = 100,
        load_options: Optional[List[Any]] = None
    ) -> List[Shop]:
        """搜索店铺"""
        search_fields = ["name", "address_line1", "description"]
        return await self.search(
            term=search_term,
            fields=search_fields,
            limit=limit,
            load_options=load_options
        )