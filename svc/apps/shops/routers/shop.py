"""
店铺API路由
分为管理端（/shops/admin/）和用户端（/shops/）接口
"""
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.dependencies import (  # resource_permission # 可扩展为 shop 权限
    get_current_active_user, has_permission)
from svc.apps.auth.models.user import User
from svc.apps.shops.dependencies import get_shop_service
from svc.apps.shops.schemas.shop import (GetShopsParams, PaginatedShopResponse,
                                         ShopCreate, ShopResponse, ShopUpdate,
                                         UserShopListResponse,
                                         UserShopResponse,
                                         ShopBatchUpdateRequest,
                                         ShopBatchDeleteRequest,
                                         ShopBatchDeleteResponse,
                                         ShopBatchUpdateResponse)

from svc.apps.shops.services.shop import ShopService
from svc.core.database import get_db
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.exceptions.route_error_handler import (SHOP_ERROR_MAPPING,
                                                     handle_route_errors)
from svc.core.models.result import Result
from svc.core.schemas.base import PageParams

router = APIRouter(
    tags=["店铺管理"]
)

STORE_ERROR_MAPPING = {}

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[PaginatedShopResponse])
@handle_route_errors(STORE_ERROR_MAPPING)
async def admin_list_shops(
    params: PageParams = Depends(),
    status: Optional[str] = Query(None, description="店铺状态", alias="status"),
    city: Optional[str] = Query(None, description="城市", alias="city"),
    country: Optional[str] = Query(None, description="国家", alias="country"),
    is_franchise: Optional[bool] = Query(None, description="是否为加盟店", alias="isFranchise"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:read"))
) -> Result[PaginatedShopResponse]:
    """获取店铺列表 (管理端)"""
    params_obj = GetShopsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        city=city,
        country=country,
        is_franchise=is_franchise,
        search_term=search_term,
        order_by="created_at",
        order_direction="desc"
    )
    result = await shop_service.get_shops(params=params_obj)
    return result

@router.put("/admin/batch", response_model=Result[ShopBatchUpdateResponse])
@handle_route_errors(error_mapping=SHOP_ERROR_MAPPING)
async def admin_batch_update_shops(
    request: ShopBatchUpdateRequest,
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:update"))
) -> Result[ShopBatchUpdateResponse]:
    """批量更新店铺 (管理端)
    
    批量更新多个店铺的状态、特色标记等信息
    """
    result = await shop_service.batch_update_shops(request=request)
    return result

@router.delete("/admin/batch", response_model=Result[ShopBatchDeleteResponse])
@handle_route_errors(error_mapping=SHOP_ERROR_MAPPING)
async def admin_batch_delete_shops(
    request: ShopBatchDeleteRequest,
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:delete"))
) -> Result[ShopBatchDeleteResponse]:
    """批量删除店铺 (管理端)
    
    批量删除多个店铺
    """
    result = await shop_service.batch_delete_shops(request=request)
    return result

@router.get("/admin/{shop_id}", response_model=Result[ShopResponse])
@handle_route_errors(STORE_ERROR_MAPPING)
async def admin_get_shop_details(
    shop_id: int = Path(..., description="店铺ID"),
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:read"))
) -> Result[ShopResponse]:
    """获取店铺详情 (管理端)"""
    result = await shop_service.get_shop(shop_id=shop_id)
    return result

@router.post("/admin/create", response_model=Result[ShopResponse], status_code=status.HTTP_201_CREATED)
@handle_route_errors(STORE_ERROR_MAPPING)
async def admin_create_shop(
    shop_data: ShopCreate,
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:create"))
) -> Result[ShopResponse]:
    """创建店铺 (管理端)"""
    result = await shop_service.create_shop(shop_data=shop_data)
    return result

@router.put("/admin/{shop_id}", response_model=Result[ShopResponse])
@handle_route_errors(STORE_ERROR_MAPPING)
async def admin_update_shop(
    shop_id: int = Path(..., description="店铺ID"),
    shop_data: ShopUpdate = Body(...),
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:update"))
) -> Result[ShopResponse]:
    """更新店铺 (管理端)"""
    result = await shop_service.update_shop(shop_id=shop_id, shop_data=shop_data)
    return result

@router.delete("/admin/{shop_id}", response_model=Result[Dict[str, Any]])
@handle_route_errors(STORE_ERROR_MAPPING)
async def admin_delete_shop(
    shop_id: int = Path(..., description="店铺ID"),
    soft_delete: bool = Query(True, description="是否软删除", alias="softDelete"),
    shop_service: ShopService = Depends(get_shop_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("shop:delete"))
) -> Result[Dict[str, Any]]:
    """删除店铺 (管理端)"""
    result = await shop_service.delete_shop(shop_id=shop_id, soft_delete=soft_delete)
    return result



# === 用户端路由 (Client Routes) ===

@router.get("/list", response_model=Result[UserShopListResponse])
@handle_route_errors(STORE_ERROR_MAPPING)
async def list_shops(
    params: PageParams = Depends(),
    status: Optional[str] = Query("open", description="店铺状态", alias="status"),
    city: Optional[str] = Query(None, description="城市", alias="city"),
    country: Optional[str] = Query(None, description="国家", alias="country"),
    is_franchise: Optional[bool] = Query(None, description="是否为加盟店", alias="isFranchise"),
    search_term: Optional[str] = Query(None, description="搜索关键词", alias="searchTerm"),
    shop_service: ShopService = Depends(get_shop_service)
) -> Result[UserShopListResponse]:
    """获取公开店铺列表 (用户端)"""
    params_obj = GetShopsParams(
        page_num=params.page_num,
        page_size=params.page_size,
        status=status,
        city=city,
        country=country,
        is_franchise=is_franchise,
        search_term=search_term,
        order_by="created_at",
        order_direction="desc"
    )
    result = await shop_service.get_shops(params=params_obj, user_mode=True)
    return result

@router.get("/{shop_id}", response_model=Result[UserShopResponse])
@handle_route_errors(STORE_ERROR_MAPPING)
async def get_shop_details(
    shop_id: int = Path(..., description="店铺ID"),
    shop_service: ShopService = Depends(get_shop_service)
) -> Result[UserShopResponse]:
    """获取店铺详情 (用户端)"""
    result = await shop_service.get_shop(shop_id=shop_id, user_mode=True)
    return result