from pydantic import BaseModel, Field, ConfigDict, EmailStr
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.models.base import CamelCaseModel

from svc.core.schemas.base import PaginatedResponse
from svc.apps.shops.models.shop import ShopStatus # 导入ShopStatus枚举
from svc.apps.albums.schemas.album import AlbumResponse
from svc.apps.albums.schemas.album import UserAlbumResponse
from svc.core.schemas.batch import BatchUpdateRequest, BatchDeleteRequest, BatchDeleteResponse, BatchUpdateResponse

class ShopBase(CamelCaseModel):
    """门店基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "上海南京路旗舰店",
                "description": "位于上海市中心南京路步行街的旗舰体验店，提供最新产品展示和体验服务",
                "address_line1": "南京东路123号",
                "address_line2": "恒基名人购物中心1楼",
                "city": "上海",
                "state_province": "上海市",
                "postal_code": "200001",
                "country": "CN",
                "phone_number": "021-6328-8888",
                "email": "<EMAIL>",
                "website": "https://shop.example.com/shanghai-nanjing",
                "opening_hours": {
                    "周一至周五": "10:00-22:00",
                    "周六至周日": "09:30-22:30",
                    "法定节假日": "09:00-23:00"
                },
                "is_franchise": False,
                "latitude": "31.2304",
                "longitude": "121.4737",
                "extra_info": {
                    "floor_area_sqm": 800,
                    "has_parking": True,
                    "parking_spaces": 50,
                    "staff_count": 25,
                    "services": ["产品展示", "体验服务", "维修中心", "培训教室"]
                }
            }
        }
    )

    name: str = Field(..., max_length=200, description="门店名称")
    description: Optional[str] = Field(default=None, description="门店描述")
    address_line1: str = Field(..., max_length=255, description="地址行1")
    address_line2: Optional[str] = Field(default=None, max_length=255, description="地址行2")
    city: str = Field(..., max_length=100, description="城市")
    state_province: Optional[str] = Field(default=None, max_length=100, description="州/省")
    postal_code: str = Field(..., max_length=20, description="邮政编码")
    country: str = Field(default="CN", max_length=50, description="国家")
    phone_number: Optional[str] = Field(default=None, max_length=50, description="联系电话")
    email: Optional[EmailStr] = Field(default=None, description="电子邮箱")
    website: Optional[str] = Field(default=None, max_length=255, description="网站")
    opening_hours: Optional[Dict[str, str]] = Field(default=None, description="营业时间")
    is_franchise: bool = Field(default=False, description="是否为加盟店")
    latitude: Optional[str] = Field(default=None, max_length=50, description="纬度")
    longitude: Optional[str] = Field(default=None, max_length=50, description="经度")
    extra_info: Optional[Dict[str, Any]] = Field(default=None, description="扩展信息")

class ShopCreate(ShopBase):
    """门店创建模型"""
    status: ShopStatus = Field(default=ShopStatus.COMING_SOON, description="门店状态")

class ShopUpdate(CamelCaseModel):
    """门店更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "上海南京路旗舰店（装修升级）",
                "description": "位于上海市中心南京路步行街的旗舰体验店，装修升级后提供更优质的产品展示和体验服务",
                "phone_number": "021-6328-9999",
                "opening_hours": {
                    "周一至周五": "10:00-22:00",
                    "周六至周日": "09:30-22:30",
                    "法定节假日": "09:00-23:00",
                    "特殊说明": "装修期间营业时间可能调整"
                },
                "status": "active",
                "extra_info": {
                    "floor_area_sqm": 1000,
                    "has_parking": True,
                    "parking_spaces": 60,
                    "staff_count": 30,
                    "services": ["产品展示", "体验服务", "维修中心", "培训教室", "VIP休息室"],
                    "renovation_status": "已完成"
                }
            }
        }
    )

    name: Optional[str] = Field(default=None, max_length=200, description="门店名称")
    description: Optional[str] = Field(default=None, description="门店描述")
    address_line1: Optional[str] = Field(default=None, max_length=255, description="地址行1")
    address_line2: Optional[str] = Field(default=None, max_length=255, description="地址行2")
    city: Optional[str] = Field(default=None, max_length=100, description="城市")
    state_province: Optional[str] = Field(default=None, max_length=100, description="州/省")
    postal_code: Optional[str] = Field(default=None, max_length=20, description="邮政编码")
    country: Optional[str] = Field(default=None, max_length=50, description="国家")
    phone_number: Optional[str] = Field(default=None, max_length=50, description="联系电话")
    email: Optional[EmailStr] = Field(default=None, description="电子邮箱")
    website: Optional[str] = Field(default=None, max_length=255, description="网站")
    opening_hours: Optional[Dict[str, str]] = Field(default=None, description="营业时间")
    status: Optional[ShopStatus] = Field(default=None, description="门店状态")
    is_franchise: Optional[bool] = Field(default=None, description="是否为加盟店")
    latitude: Optional[str] = Field(default=None, max_length=50, description="纬度")
    longitude: Optional[str] = Field(default=None, max_length=50, description="经度")
    extra_info: Optional[Dict[str, Any]] = Field(default=None, description="扩展信息")

class ShopResponse(ShopBase):
    """门店响应模型"""
    id: int = Field(..., description="门店ID")
    status: ShopStatus = Field(..., description="门店状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    album_id: Optional[int] = Field(default=None, description="关联图册ID")
    album: Optional[AlbumResponse] = Field(default=None, description="关联图册详情")

    model_config = ConfigDict(from_attributes=True) # 允许从 ORM 对象转换

class PaginatedShopResponse(PaginatedResponse[ShopResponse]):
    """分页门店响应模型"""
    pass

class GetShopsParams(BaseModel):
    """查询门店列表参数"""
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=100, description="每页数量")
    status: Optional[ShopStatus] = Field(default=None, description="门店状态")
    city: Optional[str] = Field(default=None, max_length=100, description="城市")
    country: Optional[str] = Field(default=None, max_length=50, description="国家")
    is_franchise: Optional[bool] = Field(default=None, description="是否为加盟店")
    search_term: Optional[str] = Field(default=None, description="搜索关键词 (匹配名称、地址)")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_direction: Optional[str] = Field(default="desc", description="排序方向: asc/desc")

class UserShopResponse(CamelCaseModel):
    """用户端门店响应模型，列表用image_url，详情用album"""
    id: int = Field(..., description="门店ID")
    name: str = Field(..., description="门店名称")
    description: Optional[str] = Field(default=None, description="门店描述")
    address_line1: str = Field(..., max_length=255, description="地址行1")
    city: str = Field(..., max_length=100, description="城市")
    country: str = Field(default="CN", max_length=50, description="国家")
    phone_number: Optional[str] = Field(default=None, max_length=50, description="联系电话")
    website: Optional[str] = Field(default=None, max_length=255, description="网站")
    opening_hours: Optional[Dict[str, str]] = Field(default=None, description="营业时间")
    is_franchise: bool = Field(default=False, description="是否为加盟店")
    latitude: Optional[str] = Field(default=None, max_length=50, description="纬度")
    longitude: Optional[str] = Field(default=None, max_length=50, description="经度")
    extra_info: Optional[Dict[str, Any]] = Field(default=None, description="扩展信息")
    image_url: Optional[str] = Field(default=None, description="门店封面图片URL（列表用）")
    album: Optional[UserAlbumResponse] = Field(default=None, description="门店主图册（详情用，精简版）")

class UserShopListResponse(PaginatedResponse[UserShopResponse]):
    """用户端门店分页响应模型"""
    pass

class ShopBatchDeleteResponse(BatchDeleteResponse):
    """门店批量删除响应模型"""
    pass

class ShopBatchUpdateResponse(BatchUpdateResponse):
    """门店批量更新响应模型"""
    pass

class ShopBatchUpdate(ShopUpdate):
    """门店批量更新模型
    
    定义允许批量更新的字段，继承自ShopUpdate
    """
    pass

class ShopBatchUpdateRequest(BatchUpdateRequest[ShopBatchUpdate]):
    """店铺批量更新请求模型
    
    包含要更新的店铺ID列表和更新数据
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "is_featured": True
                }
            }
        }
    )

class DeleteShopParams(BaseModel):
    """删除店铺参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "shop_id": 1,
                "soft_delete": True
            }
        }
    )
    
    shop_id: int = Field(..., description="店铺ID")
    soft_delete: bool = Field(default=True, description="是否软删除")

class ShopBatchDeleteRequest(BatchDeleteRequest):
    """店铺批量删除请求模型
    
    包含要删除的店铺ID列表
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )