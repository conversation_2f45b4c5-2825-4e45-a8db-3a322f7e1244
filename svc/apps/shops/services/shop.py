from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.schemas.album import AlbumCreate, UserAlbumResponse
from svc.apps.albums.services.album import AlbumService
from svc.apps.shops.models.shop import Shop, ShopStatus
from svc.apps.shops.repositories.shop import ShopRepository
from svc.apps.shops.schemas.shop import (GetShopsParams, PaginatedShopResponse,
                                         ShopBatchDeleteRequest,
                                         ShopBatchDeleteResponse,
                                         ShopBatchUpdateRequest,
                                         ShopBatchUpdateResponse, ShopCreate,
                                         ShopResponse, ShopUpdate,
                                         UserShopListResponse,
                                         UserShopResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.services.base import BaseService
from svc.core.services.config import ServiceConfig
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）

class ShopService(BaseService, BatchOperationMixin):
    """门店服务类，提供门店的创建、查询和管理功能

    该服务类负责：
    1. 门店的创建和管理
    2. 门店状态更新
    3. 门店数据查询

    服务类依赖ShopRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """

    resource_type = "shop"

    def __init__(
        self,
        shop_repo: ShopRepository,
        album_service: Optional[AlbumService] = None,
        redis: Optional[Redis] = None,
        **kwargs
    ):
        """初始化门店服务

        Args:
            shop_repo: 门店仓库实例
            album_service: 图册服务实例
            redis: Redis客户端，用于缓存
            **kwargs: 其他参数
        """
        config = ServiceConfig(
            resource_type="shop",
            cache_enabled=True,
            cache_ttl=CACHE_TTL,
            enable_events=True,
            enable_batch_operations=True
        )

        super().__init__(shop_repo, config, redis, **kwargs)

        self.shop_repo = shop_repo
        self.album_service = album_service

    async def get_resource_by_id(self, shop_id: int) -> Optional[Shop]:
        """获取指定ID的门店资源

        Args:
            shop_id: 门店ID

        Returns:
            Optional[Shop]: 门店对象，不存在时返回None
        """
        return await self.shop_repo.get_by_id(shop_id)

    def _convert_to_user_response(self, shop: Shop, include_album: bool = False) -> UserShopResponse:
        """转换门店对象为用户端响应格式"""
        image_url = None
        album = None
        
        if shop.album:
            # 获取封面图片URL
            if shop.album.cover_image and shop.album.cover_image.url:
                image_url = shop.album.cover_image.url
            # 如果没有封面图片，使用第一张图片
            elif shop.album.images and len(shop.album.images) > 0:
                sorted_images = sorted(shop.album.images, key=lambda x: x.sort_order or 0)
                if sorted_images and sorted_images[0].url:
                    image_url = sorted_images[0].url
            
            # 如果需要包含图册信息
            if include_album:
                images = [img.url for img in getattr(shop.album, 'images', [])] if hasattr(shop.album, 'images') else []
                album = UserAlbumResponse(
                    id=shop.album.id,
                    name=shop.album.name,
                    images=images
                )
        
        return UserShopResponse(
            id=shop.id,
            name=shop.name,
            description=shop.description,
            address_line1=shop.address_line1,
            city=shop.city,
            country=shop.country,
            phone_number=getattr(shop, 'phone_number', None),
            website=getattr(shop, 'website', None),
            opening_hours=getattr(shop, 'opening_hours', None),
            is_franchise=shop.is_franchise,
            latitude=getattr(shop, 'latitude', None),
            longitude=getattr(shop, 'longitude', None),
            extra_info=getattr(shop, 'extra_info', None),
            image_url=image_url,
            album=album
        )



    async def get_shop(self, shop_id: int, user_mode: bool = False) -> Result[ShopResponse]:
        """获取门店信息

        Args:
            shop_id: 门店ID
            user_mode: 是否为用户模式

        Returns:
            Result[ShopResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取门店: id={shop_id}")

            # 尝试从缓存获取
            cache_key = self._get_resource_cache_key(shop_id)
            if user_mode:
                cached_shop = await self.get_cached_resource(
                    cache_key,
                    lambda data: UserShopResponse.model_validate(data)
                )
            else:
                cached_shop = await self.get_cached_resource(
                    cache_key,
                    lambda data: ShopResponse.model_validate(data)
                )
            
            if cached_shop:
                self.logger.debug(f"从缓存获取到门店: id={shop_id}")
                return self.create_success_result(cached_shop)

            # 从数据库获取
            shop = await self.get_resource_by_id(shop_id)
            if not shop:
                self.logger.warning(f"门店不存在: id={shop_id}")
                return self.resource_not_found_result(shop_id)

            # 转换响应格式
            if user_mode:
                response = self._convert_to_user_response(shop, include_album=True)
            else:
                response = ShopResponse.model_validate(shop)

            # 缓存结果
            await self.cache_resource(self._get_resource_cache_key(shop_id), response, CACHE_TTL)
            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"获取门店失败: {str(e)}"
            )

    async def get_shops(self, params: GetShopsParams, user_mode: bool = False) -> Result[PaginatedShopResponse]:
        """获取门店列表

        Args:
            params: 查询参数
            user_mode: 是否为用户模式

        Returns:
            Result[PaginatedShopResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取门店列表: page={params.page_num}, size={params.page_size}")
            
            # 获取门店数据
            filters = {}
            if params.status:
                filters["status"] = params.status
            if params.city:
                filters["city"] = params.city
            if params.country:
                filters["country"] = params.country
            if params.is_franchise is not None:
                filters["is_franchise"] = params.is_franchise
            
            shops, total_count = await self.shop_repo.get_paginated(
                filters=filters,
                page_num=params.page_num,
                page_size=params.page_size,
                search_term=params.search_term,
                order_by=params.order_by,
                order_direction=params.order_direction
            )
            pages = (total_count + params.page_size - 1) // params.page_size if params.page_size else 1

            # 转换响应格式
            if user_mode:
                user_shops = [self._convert_to_user_response(shop, include_album=False) for shop in shops]
                response = UserShopListResponse(
                    items=user_shops,
                    total=total_count,
                    page_num=params.page_num,
                    page_size=params.page_size,
                    page_count=pages
                )
            else:
                shop_responses = [ShopResponse.model_validate(shop) for shop in shops]
                response = PaginatedShopResponse(
                    items=shop_responses,
                    total=total_count,
                    page_num=params.page_num,
                    page_size=params.page_size,
                    page_count=pages
                )

            return self.create_success_result(response)

        except Exception as e:
            self.logger.error(f"获取门店列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"获取门店列表失败: {str(e)}"
            )

    async def create_shop(self, shop_data: ShopCreate) -> Result[ShopResponse]:
        """创建新门店

        Args:
            shop_data: 门店创建数据

        Returns:
            Result[ShopResponse]: 创建结果
        """
        try:
            self.logger.info(f"创建新门店: name={shop_data.name}")

            # 检查门店是否已存在
            existing_shop = await self.shop_repo.get_one(name=shop_data.name, city=shop_data.city)
            if existing_shop:
                self.logger.warning(f"门店已存在: name={shop_data.name}, city={shop_data.city}")
                return self.create_error_result(
                    error_code=ErrorCode.RESOURCE_EXISTS,
                    error_message=f"名为 '{shop_data.name}' 的门店在城市 '{shop_data.city}' 已存在"
                )
            
            # 创建图册
            album_create_data = AlbumCreate(name=f"店铺图册 - {shop_data.name}", tags=["shop"])
            album_result = await self.album_service.create_album(album_create_data)
            if not album_result.is_success:
                self.logger.error(f"为新店铺创建图册失败: {album_result.error_message}")
                return self.create_error_result(
                    error_code=ErrorCode.CREATE_FAILED,
                    error_message=f"创建关联图册失败: {album_result.error_message}"
                )
            
            # 创建门店
            shop_data_dict = shop_data.model_dump()
            shop_data_dict["album_id"] = album_result.data.id
            new_shop = await self.shop_repo.create(shop_data_dict)
            shop_response = ShopResponse.model_validate(new_shop)

            # 分发事件
            dispatch(f"{self.resource_type}_created", payload=shop_response.model_dump())
            self.logger.info(f"门店创建成功: id={new_shop.id}, name={new_shop.name}")
            
            return self.create_success_result(shop_response)

        except Exception as e:
            self.logger.error(f"创建门店失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.CREATE_FAILED,
                error_message=f"创建门店失败: {str(e)}"
            )

    async def update_shop(self, shop_id: int, shop_data: ShopUpdate) -> Result[ShopResponse]:
        """更新门店信息

        Args:
            shop_id: 门店ID
            shop_data: 更新数据

        Returns:
            Result[ShopResponse]: 更新结果
        """
        try:
            self.logger.info(f"更新门店: id={shop_id}")
            
            # 检查门店是否存在
            shop = await self.get_resource_by_id(shop_id)
            if not shop:
                self.logger.warning(f"门店不存在: id={shop_id}")
                return self.resource_not_found_result(shop_id)

            # 更新门店
            update_data = shop_data.model_dump(exclude_unset=True)
            if not update_data:
                self.logger.warning(f"无有效更新字段: id={shop_id}")
                return self.create_error_result(
                    error_code=ErrorCode.UPDATE_FAILED,
                    error_message="未提供任何需要更新的字段"
                )

            updated_shop = await self.shop_repo.update(shop, update_data)
            if not updated_shop:
                self.logger.error(f"更新门店失败: id={shop_id}")
                return self.create_error_result(
                    error_code=ErrorCode.UPDATE_FAILED,
                    error_message="更新门店失败"
                )

            shop_response = ShopResponse.model_validate(updated_shop)

            # 清除缓存并分发事件
            await self.delete_cache(self._get_resource_cache_key(shop_id))
            dispatch(f"{self.resource_type}_updated", payload=shop_response.model_dump())
            
            self.logger.info(f"门店更新成功: id={shop_id}")
            return self.create_success_result(shop_response)

        except Exception as e:
            self.logger.error(f"更新门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.UPDATE_FAILED,
                error_message=f"更新门店失败: {str(e)}"
            )

    async def delete_shop(self, shop_id: int, soft_delete: bool = True) -> Result[Dict[str, Any]]:
        """删除门店

        Args:
            shop_id: 门店ID
            soft_delete: 是否软删除

        Returns:
            Result[Dict[str, Any]]: 删除结果
        """
        try:
            self.logger.info(f"删除门店: id={shop_id}, soft_delete={soft_delete}")
            
            # 检查门店是否存在
            shop = await self.get_resource_by_id(shop_id)
            if not shop:
                self.logger.warning(f"门店不存在: id={shop_id}")
                return self.resource_not_found_result(shop_id)

            album_id = shop.album_id

            # 执行删除
            if soft_delete:
                update_data = ShopUpdate(status=ShopStatus.DELETED).model_dump(exclude_unset=True)
                deleted_shop = await self.shop_repo.update(shop, update_data)
                if not deleted_shop:
                    self.logger.error(f"软删除门店失败: id={shop_id}")
                    return self.create_error_result(
                        error_code=ErrorCode.DELETE_FAILED,
                        error_message="软删除门店失败"
                    )
                self.logger.info(f"门店软删除成功: id={shop_id}")
            else:
                await self.shop_repo.delete(shop)
                self.logger.info(f"门店硬删除成功: id={shop_id}")

            # 清除缓存并分发事件
            await self.delete_cache(self._get_resource_cache_key(shop_id))
            dispatch(f"{self.resource_type}_deleted", payload={
                "id": shop_id, 
                "album_id": album_id, 
                "soft_delete": soft_delete
            })
            
            return self.create_success_result({
                "id": shop_id, 
                "album_id": album_id, 
                "soft_delete": soft_delete
            })

        except Exception as e:
            self.logger.error(f"删除门店失败: id={shop_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.DELETE_FAILED,
                error_message=f"删除门店失败: {str(e)}"
            )

    async def batch_update_shops(self, request: ShopBatchUpdateRequest) -> Result[ShopBatchUpdateResponse]:
        """批量更新门店"""
        self.logger.info(f"批量更新门店: ids={request.resource_ids}")
        return await self.batch_update_resources(
            resource_ids=request.resource_ids,
            update_data=request.update_data.model_dump(exclude_unset=True),
            repository=self.shop_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )

    async def batch_delete_shops(self, request: ShopBatchDeleteRequest) -> Result[ShopBatchDeleteResponse]:
        """批量删除门店"""
        self.logger.info(f"批量删除门店: ids={request.resource_ids}, soft_delete={request.soft_delete}")
        return await self.batch_delete_resources(
            resource_ids=request.resource_ids,
            soft_delete=request.soft_delete,
            repository=self.shop_repo,
            resource_type=self.resource_type,
            cache_key_generator=self._get_resource_cache_key
        )