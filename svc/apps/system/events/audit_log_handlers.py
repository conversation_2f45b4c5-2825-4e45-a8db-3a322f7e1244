"""
系统审计日志事件处理器
"""

import logging
from datetime import datetime
from typing import Any

from svc.core.events.local_handlers import local_handler
from fastapi_events.typing import Event

from svc.core.events.event_names import \
    SYSTEM_AUDIT_LOG_RECORDED  # SYSTEM_AUDIT_LOG_REQUESTED # 服务层直接调用服务记录，事件处理器仅监听已记录事件; 监听此事件以确认记录

logger = logging.getLogger(__name__)


def _make_serializable(obj: Any) -> Any:
    """将对象转换为可JSON序列化的格式"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {k: _make_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_make_serializable(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        # 对于自定义对象，尝试转换为字典
        return _make_serializable(obj.__dict__)
    else:
        # 对于基本类型（str, int, float, bool, None），直接返回
        return obj


# 处理审计日志记录请求事件 (实际创建审计日志)
@local_handler.register(event_name=SYSTEM_AUDIT_LOG_RECORDED)
async def handle_audit_log_record_request(
    event: Event,
):
    """处理审计日志记录请求，实际创建审计日志记录。"""
    _, payload = event
    user_id = payload.get('user_id')
    action = payload.get('action')
    resource_type = payload.get('resource_type')
    resource_id = payload.get('resource_id')
    username = payload.get('username')
    status = payload.get('status', 'success')
    message = payload.get('message')
    metadata = payload.get('metadata', {})

    # 记录接收到的事件信息
    logger.info(
        f"[Event Handler] 处理审计日志记录请求: action={action}, "
        f"user_id={user_id}, resource={resource_type}:{resource_id}, status={status}"
    )

    if not action or not resource_type:
        logger.error(f"审计日志记录请求缺少必要字段: action={action}, resource_type={resource_type}")
        return

    db = None
    try:
        # 导入必要的模块（延迟导入避免循环依赖）
        from svc.apps.system.repositories.audit_log import AuditLogRepository
        from svc.apps.system.services.audit_log import AuditLogService
        from svc.core.database.utils import get_session_for_event

        # 获取数据库会话
        async with get_session_for_event() as db:

            # 创建审计日志服务
            audit_log_repo = AuditLogRepository(db=db)
            audit_service = AuditLogService(audit_log_repo=audit_log_repo)
            # 确保resource_id是字符串类型   
            if resource_id is not None and not isinstance(resource_id, str):
                resource_id = str(resource_id)

            # 确保metadata中的所有值都是可序列化的
            if metadata:
                metadata = _make_serializable(metadata)

            # 记录审计日志
            result = await audit_service.create_audit_log(
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                request=None,  # 事件处理器中没有request对象
                details=metadata,
                status=status,
                message=message or f"{action} {resource_type}"
            )

            # 检查结果
            if result.is_success:
                # 提交事务
                await db.commit()
                logger.info(f"审计日志记录成功: {action} {resource_type} - user_id={user_id}")
            else:
                # 记录失败，回滚事务
                await db.rollback()
                logger.error(f"审计日志服务返回失败: {result.result_msg}")

    except Exception as e:
        logger.error(f"记录审计日志失败: {str(e)}", exc_info=True)
        if db:
            try:
                await db.rollback()
            except Exception:
                pass
    finally:
        if db:
            try:
                await db.close()
            except Exception:
                pass

    # 在这里可以添加额外的逻辑，例如：
    # 1. 触发基于特定审计事件的通知 (例如，关键操作失败)
    # if status == 'failed' and action in ['user_delete', 'config_update']:
    #     try:
    #         dispatch(
    #             "system:security:alert_triggered",
    #             payload={
    #                 "alert_type": "critical_action_failed",
    #                 "log_id": log_id,
    #                 "action": action,
    #                 "user_id": user_id,
    #                 "resource_type": resource_type,
    #                 "resource_id": resource_id,
    #                 "message": payload.get('message', 'Unknown error')
    #             }
    #         )
    #     except Exception as e:
    #         logger.error(f"触发安全警报失败 (log_id={log_id}): {e}", exc_info=True)

    # 2. 更新相关实体的状态或统计 (如果需要，但通常统计更新由 SYSTEM_STATS_UPDATE_REQUESTED 处理)

    # 3. 将日志推送到外部监控系统 (例如 ELK, Splunk)
    # try:
    #     # external_monitoring_client.push_log(payload)
    #     pass
    # except Exception as e:
    #     logger.error(f"推送审计日志到外部系统失败 (log_id={log_id}): {e}", exc_info=True)

    # 注意：确保这里的逻辑不会再次触发 SYSTEM_AUDIT_LOG_RECORDED，避免无限循环

    pass # Placeholder for actual logic