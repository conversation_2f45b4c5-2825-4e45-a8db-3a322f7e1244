"""
审计日志仓库实现。
提供对审计日志数据的访问和操作。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog
from svc.core.repositories import BaseRepository


class AuditLogRepository(BaseRepository[AuditLog, Dict[str, Any], Dict[str, Any]]):
    """
    审计日志仓库类
    
    提供对审计日志数据的访问和操作，支持按条件查询和过滤。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化审计日志仓库"""
        super().__init__(db, AuditLog)
    

    
    

    

    




    async def delete_old_logs(
        self,
        
        cutoff_date: datetime
    ) -> int:
        """
        删除旧的审计日志

        Args:
            db: 数据库会话
            cutoff_date: 截止日期，早于此日期的日志将被删除

        Returns:
            int: 删除的记录数
        """
        return await self.delete_where(filters={"created_at__lt": cutoff_date})