"""
审计日志模式定义。
定义审计日志的API请求和响应数据结构。
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, ConfigDict, Field
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel
from svc.core.schemas.batch import BatchDeleteRequest, BatchDeleteResponse

class AuditLogBase(CamelCaseModel):
    """审计日志基础模式"""
    action: str = Field(..., description="操作类型，如login、create、update、delete等")
    resource_type: str = Field(..., description="资源类型，如user、role、config等")
    resource_id: Optional[str] = Field(None, description="资源ID")
    user_id: Optional[int] = Field(None, description="执行操作的用户ID")
    username: Optional[str] = Field(None, description="执行操作的用户名")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理（浏览器或客户端信息）")
    status: str = Field("success", description="操作结果，success或failure")
    details: Optional[Dict[str, Any]] = Field(None, description="操作详情")
    message: Optional[str] = Field(None, description="额外信息")

class AuditLogCreate(AuditLogBase):
    """创建审计日志的请求模式"""
    pass

class AuditLogInDB(AuditLogBase):
    """数据库中的审计日志模式"""
    id: int
    created_at: datetime
    
    model_config = ConfigDict(
        from_attributes=True
    )

class AuditLogResponse(AuditLogInDB):
    """审计日志响应模式"""
    pass

class AuditLogListResponse(PaginatedResponse[AuditLogResponse]):
    """审计日志列表响应模型"""
    pass

class AuditLogFilter(BaseModel):
    """审计日志查询过滤器"""
    action: Optional[str] = Field(None, description="操作类型")
    resource_type: Optional[str] = Field(None, description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    user_id: Optional[int] = Field(None, description="执行操作的用户ID")
    username: Optional[str] = Field(None, description="执行操作的用户名")
    status: Optional[str] = Field(None, description="操作结果")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页条数")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_direction: Optional[str] = Field(default="desc", description="排序方向: asc/desc")


# === 批量操作Schema ===

class AuditLogBatchDeleteRequest(BatchDeleteRequest):
    """审计日志批量删除请求模型
    
    用于批量清理旧的审计日志
    """
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                "soft_delete": False,  # 审计日志通常使用硬删除
                "reason": "清理SHOEI头盔产品相关的旧审计日志"
            }
        }
    )

class AuditLogBatchDeleteResponse(BatchDeleteResponse):
    """审计日志批量删除响应模型"""
    pass 