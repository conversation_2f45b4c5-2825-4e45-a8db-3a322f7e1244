"""数据库引擎管理模块"""
import logging
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import AsyncAdaptedQueuePool

from svc.core.config.settings import get_settings
from svc.core.models.base import Base
logger = logging.getLogger(__name__)

# 全局引擎实例
_engine: Optional[AsyncEngine] = None
_initialized = False

async def get_engine(testing: bool = False) -> AsyncEngine:
    """获取数据库引擎"""
    global _engine, _initialized

    if _initialized and _engine:
        return _engine

    settings = get_settings()
    db_uri = settings.db_test_uri if testing else settings.db_uri

    _engine = create_async_engine(
        db_uri,
        echo=settings.db_echo,
        future=True,
        pool_pre_ping=True,
        poolclass=AsyncAdaptedQueuePool,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
        pool_timeout=settings.db_pool_timeout,
        pool_recycle=settings.db_pool_recycle
    )

    _initialized = True
    logger.info(f"数据库引擎已初始化，连接池大小: {settings.db_pool_size}")
    # if settings.env != "production":
    await _init_database(_engine)
  

    return _engine

async def _init_database(engine: AsyncEngine) -> None:
    """初始化数据库，根据当前模型定义创建表"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all, checkfirst=True)
        logger.info("数据库表已创建")

async def close_engine() -> None:
    """关闭数据库引擎"""
    global _engine, _initialized

    if _engine:
        await _engine.dispose()
        _engine = None
        _initialized = False
        logger.info("数据库引擎已关闭")

async def check_database_health() -> bool:
    """检查数据库健康状态"""
    if not _engine:
        return False

    try:
        from sqlalchemy.future import select
        async with _engine.connect() as conn:
            await conn.execute(select(1))
        return True
    except Exception as e:
        logger.error(f"数据库健康检查失败: {e}")
        return False


async def clear_database(
    engine: Optional[AsyncEngine] = None,
    confirm: bool = False,
    recreate_tables: bool = True
) -> bool:
    """
    清空数据库并可选重新创建表
    
    Args:
        engine: 数据库引擎，如果不提供则使用全局引擎
        confirm: 确认操作，防止意外清空
        recreate_tables: 是否重新创建表结构
        
    Returns:
        bool: 操作是否成功
        
    Raises:
        ValueError: 生产环境或未确认操作时抛出
    """
    settings = get_settings()
    
    # 生产环境安全检查
    if settings.env == "production" and not confirm:
        logger.error("生产环境禁止清空数据库")
        raise ValueError("生产环境禁止清空数据库")
    
    # 确认检查
    if not confirm:
        logger.error("清空数据库需要显式确认")
        raise ValueError("清空数据库需要显式确认，请设置 confirm=True")
    
    # 使用指定引擎或全局引擎
    target_engine = engine or _engine
    if not target_engine:
        logger.error("数据库引擎未初始化")
        return False
    
    try:
        
        logger.warning("开始清空数据库...")
        
        async with target_engine.begin() as conn:
            # 删除所有表（使用 CASCADE 处理外键依赖）

            await conn.run_sync(Base.metadata.drop_all, checkfirst=True)
            logger.info("数据库表已删除")
            
            # 可选重新创建表
            if recreate_tables:
                await conn.run_sync(Base.metadata.create_all, checkfirst=True)
                logger.info("数据库表已重新创建")
        
        logger.info("数据库清空操作完成")
        return True
        
    except Exception as e:
        logger.error(f"清空数据库失败: {e}")
        return False
    

