"""数据库会话管理模块"""
import contextlib
import logging
from typing import AsyncGenerator

from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import sessionmaker

from .engine import get_engine
from .exceptions import DatabaseError

logger = logging.getLogger(__name__)

# 全局会话工厂
_session_factory = None

async def _ensure_factory():
    """确保会话工厂已创建"""
    global _session_factory
    if _session_factory is None:
        engine = await get_engine()
        _session_factory = sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autocommit=False,
            autoflush=False
        )

@contextlib.asynccontextmanager
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话上下文管理器"""
    await _ensure_factory()

    session = _session_factory()
    try:
        yield session
    except HTTPException:
        # 不包装HTTPException，让它直接传播
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"数据库会话操作失败: {e}")
        # 只包装真正的数据库相关异常
        if "database" in str(e).lower() or "sql" in str(e).lower() or "connection" in str(e).lower():
            raise DatabaseError(f"数据库操作失败: {e}") from e
        else:
            # 其他异常直接传播，不包装
            raise
    finally:
        await session.close()

async def create_session() -> AsyncSession:
    """创建新的会话实例（需要手动管理）"""
    await _ensure_factory()
    return _session_factory()