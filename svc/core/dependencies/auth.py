"""
认证和授权依赖项。
提供用于在FastAPI路由中使用的依赖注入函数工厂。

使用示例:

1. 基本认证检查:
```python
from fastapi import APIRouter, Depends
from svc.core.dependencies.auth import auth_dependencies

router = APIRouter()

@router.get("/me")
async def read_users_me(current_user = Depends(auth_dependencies.current_active_user())):
    return current_user
```

# ... 其他示例保持不变 ...
"""
import inspect
import logging
from typing import Any, Callable, Dict, List, Optional, Type, Union

from fastapi import Depends, HTTPException, Path, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from svc.core.cache.apps import user_cache
from svc.core.database import get_db
from svc.core.security.oauth2 import oauth2_scheme
from svc.core.security.permissions import PermissionService

logger = logging.getLogger(__name__)

class AuthDependencies:
    """提供路由依赖项函数工厂，简化认证授权使用"""
    
    def __init__(
        self, 
        permission_service: Optional[PermissionService] = None,
        get_db_func: Optional[Callable] = None
    ):
        """初始化依赖项工厂
        
        Args:
            permission_service: 权限服务实例
            get_db_func: 获取数据库会话的函数
        """
        self.permission_service = permission_service
        self.get_db_func = get_db_func or get_db
    
    def _create_error_response(self, status_code: int, detail: str) -> HTTPException:
        """创建统一的错误响应"""
        return HTTPException(
            status_code=status_code,
            detail={
                "result_code": status_code,
                "result_msg": detail,
            },
            headers={"WWW-Authenticate": "Bearer"} if status_code == 401 else None
        )
    
    def _check_superuser(self, user: Any) -> bool:
        """检查用户是否为超级用户
        
        Args:
            user: 用户对象
        
        Returns:
            是否为超级用户
        """
        return getattr(user, "is_superuser", False)
    
    async def _check_permission_or_role(
        self, 
        user: Any, 
        db: AsyncSession,
        required_items: List[str],
        check_type: str = "permission"
    ) -> List[str]:
        """检查用户权限或角色
        
        Args:
            user: 用户对象
            db: 数据库会话
            required_items: 需要的权限或角色列表
            check_type: 检查类型，"permission"或"role"
        
        Returns:
            缺失的权限或角色列表
        """
        # 超级用户直接通过
        if self._check_superuser(user):
            return []
            
        # 检查每个需要的权限/角色
        missing_items = []
        for item in required_items:
            has_item = False
            
            # 使用内置方法检查
            attr_name = f"has_{check_type}"
            if hasattr(user, attr_name):
                has_item = getattr(user, attr_name)(item)
            
            # 使用权限服务检查
            if not has_item and self.permission_service:
                service_method = getattr(self.permission_service, f"has_{check_type}", None)
                if service_method:
                    has_item = await service_method(user.id, item, db)
                    
            if not has_item:
                missing_items.append(item)
        
        return missing_items
    
    async def _load_user(self, user_id: int, db: AsyncSession) -> Any:
        """加载用户信息
        
        Args:
            user_id: 用户ID
            db: 数据库会话
            
        Returns:
            用户对象，如果不存在返回None
        """
        from svc.apps.auth.repositories.user import UserRepository
        return await UserRepository(db=db).get_by_id(user_id)
    
    def current_user(self):
        """获取当前用户的依赖项
        
        验证请求已经通过认证，并返回当前用户对象。
        从request.state中获取user_id，该ID由AuthMiddleware注入。
        
        Returns:
            依赖项函数，返回当前用户
            
        Raises:
            HTTPException: 401 如果用户未登录
        """
        async def _get_current_user(
            request: Request,
            db: AsyncSession = Depends(self.get_db_func),
            token:str=Depends(oauth2_scheme)
        ):
            # 验证user_id是否存在
            if not hasattr(request.state, "user_id") or not request.state.user_id:
                raise self._create_error_response(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证的请求"
                )
                
            user_id = request.state.user_id

            # 尝试从缓存获取用户 ID 对应的数据（可能是序列化的字典或基础 User 对象）
            cached_user_data = await user_cache.get(user_id)

            user: Optional[Any] = None # 显式声明 user 类型

            if cached_user_data:
                # 即使缓存命中，也总是使用当前会话从数据库加载用户，以确保对象附加到当前会话
                # 这可以防止 DetachedInstanceError，因为缓存的对象可能来自已关闭的旧会话
                user = await self._load_user(user_id, db)
                if not user:
                    # 如果缓存说用户存在但数据库找不到，可能数据不一致，按未找到处理并清除缓存
                    logger.warning(f"用户 {user_id} 在缓存中找到但在数据库中未找到，清除缓存。")
                    await user_cache.delete(user_id)
                    raise self._create_error_response(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="用户信息无效或已过期"
                    )
                # 可选：如果需要，可以在这里比较缓存数据和数据库数据，记录差异

            else:
                # 缓存未命中，从数据库加载
                user = await self._load_user(user_id, db)

                if user:
                    # 仅在从数据库成功加载后才更新缓存
                    # 注意：这里缓存的是从当前会话加载的对象，但下次取出时仍需重新加载
                    await user_cache.set(user_id, user)
                else:
                    # 数据库也找不到用户
                    raise self._create_error_response(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="找不到用户"
                    )

            # 确保最终返回的 user 对象非 None (虽然逻辑上前面会抛异常)
            if user is None:
                 logger.error(f"代码逻辑错误：在 _get_current_user 中 user 对象不应为 None")
                 raise self._create_error_response(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="内部服务器错误"
                    )

            # 为向后兼容，注入完整用户到请求状态
            request.state.user = user

            return user

        return _get_current_user
    
    def current_active_user(self):
        """获取当前活跃用户的依赖项
        
        验证用户已登录且处于活跃状态。
        
        Returns:
            依赖项函数，返回当前活跃用户
            
        Raises:
            HTTPException: 401 如果用户未登录
            HTTPException: 403 如果用户未激活
        """
        current_user = self.current_user()
        async def _get_current_active_user(user = Depends(current_user)):
            if not getattr(user, "is_active", False):
                raise self._create_error_response(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户未激活"
                )
            return user
        return _get_current_active_user
    
    def current_superuser(self):
        """获取当前超级用户的依赖项
        
        验证用户已登录、处于活跃状态且是超级用户。
        
        Returns:
            依赖项函数，返回当前超级用户
            
        Raises:
            HTTPException: 401 如果用户未登录
            HTTPException: 403 如果用户未激活或不是超级用户
        """
        current_active_user = self.current_active_user()
        async def _get_current_superuser(user = Depends(current_active_user)):
            if not self._check_superuser(user):
                raise self._create_error_response(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足，需要管理员权限"
                )
            return user
        return _get_current_superuser
    
    def has_permission(self, permission: Union[str, List[str]]):
        """检查用户是否拥有指定权限的依赖项
        
        验证用户拥有指定的一个或多个权限。如果指定多个权限，用户必须同时拥有所有权限。
        超级用户总是通过权限检查。
        
        Args:
            permission: 权限标识符或权限列表
            
        Returns:
            依赖项函数，返回用户对象
            
        Raises:
            HTTPException: 401 如果用户未登录
            HTTPException: 403 如果用户未激活或缺少所需权限
            
        """
        # 统一转换为列表
        required_permissions = [permission] if isinstance(permission, str) else permission
        
        current_active_user = self.current_active_user()
        async def _check_permission(
            user = Depends(current_active_user),
            db: AsyncSession = Depends(self.get_db_func)
        ):
            # 检查权限
            missing_permissions = await self._check_permission_or_role(
                user, db, required_permissions, "permission"
            )
            # 如果有缺失的权限，记录日志并抛出异常
            if missing_permissions:
                permission_str = "、".join(missing_permissions)
                # 添加日志记录
                logger.warning(
                    f"用户 {user.id if hasattr(user, 'id') else '未知'} 权限不足，需要：{permission_str}"
                )
                # 仍然抛出异常以返回 403
                raise self._create_error_response(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要：{permission_str}"
                )
                    
            return user
        return _check_permission
    
    def has_role(self, role: Union[str, List[str]]):
        """检查用户是否拥有指定角色的依赖项
        
        验证用户拥有指定的一个或多个角色。如果指定多个角色，用户必须同时拥有所有角色。
        超级用户总是通过角色检查。
        
        Args:
            role: 角色名称或角色列表
            
        Returns:
            依赖项函数，返回用户对象
            
        Raises:
            HTTPException: 401 如果用户未登录
            HTTPException: 403 如果用户未激活或缺少所需角色
        """
        # 统一转换为列表
        required_roles = [role] if isinstance(role, str) else role
        
        current_active_user = self.current_active_user()
        async def _check_role(
            user = Depends(current_active_user),
            db: AsyncSession = Depends(self.get_db_func)
        ):
            # 检查角色
            missing_roles = await self._check_permission_or_role(
                user, db, required_roles, "role"
            )
            
            # 如果有缺失的角色，抛出异常
            if missing_roles:
                roles_str = "、".join(missing_roles)
                raise self._create_error_response(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要角色：{roles_str}"
                )
                
            return user
        return _check_role
    
    def resource_permission(self, resource_type: str, action: str):
        """检查用户是否有权限操作某类资源的依赖项工厂
        
        验证用户对特定资源拥有指定的操作权限。
        超级用户总是通过资源权限检查。
        
        Args:
            resource_type: 资源类型
            action: 操作类型
            
        Returns:
            依赖项函数，从路径参数获取资源ID并返回资源对象
            
        Raises:
            HTTPException: 401 如果用户未登录
            HTTPException: 403 如果用户未激活或缺少所需资源权限
            HTTPException: 404 如果资源不存在
            HTTPException: 500 如果权限服务配置错误
            
            
   
        """
        from fastapi import Path, Request
        
        current_active_user = self.current_active_user()
        
        # 定义从路径参数获取资源ID的依赖函数
        async def _check_resource_permission(
            request: Request,
            user = Depends(current_active_user),
            db: AsyncSession = Depends(self.get_db_func)
        ):
            # 从路径参数中获取资源ID
            path_params = request.path_params
            id_param = f"{resource_type}_id"
            
            if id_param not in path_params:
                logger.error(f"缺少路径参数: {id_param}")
                raise self._create_error_response(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"URL路径中缺少必需的{id_param}参数"
                )
                
            try:
                resource_id = int(path_params[id_param])
            except (ValueError, TypeError):
                logger.error(f"路径参数类型错误: {id_param}={path_params[id_param]}")
                raise self._create_error_response(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail=f"资源ID参数必须是整数"
                )
                
            if not self.permission_service:
                logger.error("未配置权限服务，无法检查资源权限")
                raise self._create_error_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="服务配置错误：未配置权限服务"
                )
                
            # 获取资源模型

            resource_model = self.permission_service._get_model_for_type(resource_type)
            if not resource_model:
                logger.error(f"未注册的资源类型: {resource_type}")
                raise self._create_error_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"服务配置错误：未注册的资源类型 {resource_type}"
                )
                
            try:
                # 使用permission_service的resource_finder获取资源
                resource = await self.permission_service.resource_finder.find_resource(
                    resource_model, resource_id, db
                )
                
                if not resource:
                    raise self._create_error_response(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"资源不存在: {resource_type}(id={resource_id})"
                    )
                
                # 检查用户是否有资源权限，使用优化后的方法
                has_perm = await self.permission_service.check_resource_access(
                    user, resource_type, resource_id, action, db, resource
                )
                
                if not has_perm:
                    raise self._create_error_response(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"没有权限操作此资源: {resource_type}:{action}"
                    )
                    
                return resource
                
            except HTTPException:
                # 重新抛出HTTP异常
                raise
            except Exception as e:
                # 记录并转换其他异常
                logger.error(f"检查资源权限时发生错误: {str(e)}")
                raise self._create_error_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"权限检查失败: {str(e)}"
                )
                
        return _check_resource_permission

    def configure(self, permission_service: Optional[PermissionService] = None, get_db_func: Optional[Callable] = None):
        """配置依赖项工厂实例
        
        更新当前实例的权限服务和数据库会话获取函数。
        
        Args:
            permission_service: 权限服务实例
            get_db_func: 获取数据库会话的函数
        """
        if permission_service:
            self.permission_service = permission_service
        if get_db_func:
            self.get_db_func = get_db_func
        
        logger.info(f"认证依赖项已配置，权限服务: {'已配置' if self.permission_service else '未配置'}")
        return self


auth_dependencies = AuthDependencies()


def configure_auth_dependencies(
    permission_service: Optional[PermissionService] = None,
    get_db_func: Optional[Callable] = None,
    resources: Optional[Dict[str, Type]] = None
) -> AuthDependencies:
    """配置全局认证依赖项（已移除注册行为，改由启动期自动发现）。"""
    global auth_dependencies
    if permission_service is None:
        from svc.core.security.permissions import permission_service as global_permission_service
        permission_service = global_permission_service
    auth_dependencies.configure(
        permission_service=permission_service,
        get_db_func=get_db_func
    )
    return auth_dependencies


