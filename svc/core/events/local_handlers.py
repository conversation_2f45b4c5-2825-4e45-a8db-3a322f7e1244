import asyncio
import fnmatch
import functools
import inspect
import sys
from contextlib import AsyncExitStack
from typing import (Any, AsyncGenerator, Callable, Dict, ForwardRef, List,
                    Optional, Tuple, cast)

from fastapi_events.handlers.base import BaseEventHandler
from fastapi_events.otel.utils import create_span_for_handle_fn
from fastapi_events.typing import Event
from typing_extensions import Protocol, runtime_checkable


def evaluate_forwardref(type_: ForwardRef, globalns: Any, localns: Any) -> Any:
    """
    Adopted from pydantic source code
    """
    if sys.version_info < (3, 9):
        return type_._evaluate(globalns, localns)
    else:
        # Even though it is the right signature for python 3.9, mypy complains with
        # `error: Too many arguments for "_evaluate" of "ForwardRef"` hence the cast...
        # Python 3.13/3.12.4+ made `recursive_guard` a kwarg, so name it explicitly to avoid:
        # TypeError: ForwardRef._evaluate() missing 1 required keyword-only argument: 'recursive_guard'
        return cast(Any, type_)._evaluate(globalns, localns, recursive_guard=set())


def get_typed_annotation(
    annotation: Any,
    globalns: Dict[str, Any]
) -> Any:
    """
    Adopted from fastapi source code
    """
    if isinstance(annotation, str):
        annotation = ForwardRef(annotation)
        annotation = evaluate_forwardref(annotation, globalns, globalns)
    return annotation


def get_typed_signature(
    call: Callable[..., Any]
) -> inspect.Signature:
    """
    Adopted from fastapi source code
    """
    signature = inspect.signature(call)
    globalns = getattr(call, "__globals__", {})
    typed_params = [
        inspect.Parameter(
            name=param.name,
            kind=param.kind,
            default=param.default,
            annotation=get_typed_annotation(param.annotation, globalns),
        )
        for param in signature.parameters.values()
    ]
    typed_signature = inspect.Signature(typed_params)
    return typed_signature


@runtime_checkable
class Depends(Protocol):
    dependency: Optional[Callable[..., Any]]
    use_cache: bool


class Dependant:
    def __init__(
        self,
        call: Callable[..., Any],
        name: Optional[str],
        dependencies: Optional[List["Dependant"]] = None,
    ):
        self.call = call
        self.name = name
        self.dependencies = dependencies or []


def get_param_sub_dependant(
    *,
    param: inspect.Parameter,
    name: str,
) -> Dependant:
    depends: Depends = param.default
    if depends.dependency:
        dependency = depends.dependency
    else:
        dependency = param.annotation

    return get_dependant(
        name=name,
        call=dependency,
    )


def get_dependant(
    *,
    call: Callable[..., Any],
    name: Optional[str] = None,
) -> Dependant:
    handler_signature = get_typed_signature(call)
    signature_params = handler_signature.parameters

    dependant = Dependant(
        call=call,
        name=name,
    )

    for param_name, param in signature_params.items():
        if isinstance(param.default, Depends):  # FIXME create a Protocol for params.Depends?
            sub_dependant = get_param_sub_dependant(
                param=param,
                name=param_name,
            )
            dependant.dependencies.append(sub_dependant)
            continue

    return dependant


def is_async_generator_function(func: Callable[..., Any]) -> bool:
    """Check if a function is an async generator function."""
    return inspect.isasyncgenfunction(func)


def is_generator_function(func: Callable[..., Any]) -> bool:
    """Check if a function is a (sync) generator function."""
    return inspect.isgeneratorfunction(func)


async def solve_dependencies(
    *,
    event: Event,
    dependant: Dependant,
    exit_stack: AsyncExitStack,
) -> Tuple[
    Dict[str, Any],
    List[Any]
]:
    values: Dict[str, Any] = {}
    errors: List[Any] = []

    for sub_dependant in dependant.dependencies:
        use_sub_dependant = sub_dependant
        call = sub_dependant.call

        sub_values, sub_errors = await solve_dependencies(
            event=event,
            dependant=use_sub_dependant,
            exit_stack=exit_stack,
        )
        if sub_errors:
            errors.extend(sub_errors)
            continue

        # Handle async generator dependencies (with yield)
        if is_async_generator_function(call):
            try:
                async_gen = call(**sub_values)
                solved = await async_gen.__anext__()
                # Register cleanup function to finalize the generator
                exit_stack.push_async_callback(_finalize_async_generator, async_gen)
            except StopAsyncIteration:
                # Generator didn't yield anything
                solved = None
            except Exception as e:
                errors.append(e)
                continue
        
        # Handle sync generator dependencies (with yield)
        elif is_generator_function(call):
            try:
                loop = asyncio.get_event_loop()
                gen = await loop.run_in_executor(None, functools.partial(call, **sub_values))
                try:
                    solved = next(gen)
                    # Register cleanup function to finalize the generator
                    exit_stack.push_async_callback(_finalize_sync_generator, gen)
                except StopIteration:
                    # Generator didn't yield anything
                    solved = None
            except Exception as e:
                errors.append(e)
                continue
        
        # Handle async function dependencies
        elif asyncio.iscoroutinefunction(call):
            try:
                solved = await call(**sub_values)
            except Exception as e:
                errors.append(e)
                continue
        
        # Handle sync function dependencies
        else:
            try:
                loop = asyncio.get_event_loop()
                solved = await loop.run_in_executor(None, functools.partial(call, **sub_values))
            except Exception as e:
                errors.append(e)
                continue

        if sub_dependant.name is not None:
            values[sub_dependant.name] = solved

    return values, errors


async def _finalize_async_generator(async_gen: AsyncGenerator[Any, None]) -> None:
    """Finalize an async generator by calling its cleanup code."""
    try:
        await async_gen.__anext__()
    except StopAsyncIteration:
        # Expected - generator finished cleanup
        pass
    except Exception as e:
        # Log the error but don't re-raise to avoid breaking the cleanup process
        print(f"Error during async generator cleanup: {e}")
    finally:
        await async_gen.aclose()


async def _finalize_sync_generator(gen) -> None:
    """Finalize a sync generator by calling its cleanup code."""
    try:
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, lambda: next(gen, None))
    except StopIteration:
        # Expected - generator finished cleanup
        pass
    except Exception as e:
        # Log the error but don't re-raise to avoid breaking the cleanup process
        print(f"Error during sync generator cleanup: {e}")


class LocalHandler(BaseEventHandler):
    def __init__(self):
        self._registry = {}

    def register(self, _func=None, event_name="*"):
        """
        Register a handler for an event. The handler will receive a tuple of event name and payload as its only argument.

        ### Args

        :param _func: The function to be registered as a handler.  Typically, you would use `register` as a decorator and omit this argument.
        :param event_name: The name of the event to be associated with the handler. Use "*", the default value, to match all events.

        ### Examples

        Register a handler as a decorator:
        ```python
        from fastapi_events.handlers.local import local_handler
        from fastapi_events.typing import Event

        @local_handler.register(event_name="my_event")
        async def my_event_handler(event: Event):
            event_name, payload = event
            print(f"Received event {event_name} with payload {payload}")
        ```
        
        Register a handler with async generator dependency:
        ```python
        from fastapi import Depends
        
        async def get_db_session():
            session = create_session()
            try:
                yield session
            finally:
                await session.close()
        
        @local_handler.register(event_name="user_created")
        async def handle_user_created(event: Event, db: Session = Depends(get_db_session)):
            event_name, payload = event
            # Use db session here
            await db.execute(...)
        ```
        """
        def _wrap(func):
            self._register_handler(event_name, func)
            return func

        if _func is None:
            return _wrap

        return _wrap(func=_func)

    async def handle(self, event: Event) -> None:
        event_name, payload = event

        with create_span_for_handle_fn(
            handler_instance=self,
            event_name=event_name,
            payload=payload,
        ):
            for handler in self._get_handlers_for_event(event_name=event_name):
                # Use AsyncExitStack to manage cleanup of generator dependencies
                async with AsyncExitStack() as exit_stack:
                    try:
                        # Resolve dependencies with proper cleanup support
                        dependant = get_dependant(call=handler)
                        values, errors = await solve_dependencies(
                            event=event, 
                            dependant=dependant,
                            exit_stack=exit_stack
                        )
                        
                        if errors:
                            # Log errors but continue with other handlers
                            for error in errors:
                                print(f"Error resolving dependencies: {error}")
                            continue

                        # Execute the handler
                        if inspect.iscoroutinefunction(handler):
                            await handler(event, **values)
                        else:
                            # Making sure sync function will never block the event loop
                            loop = asyncio.get_event_loop()
                            await loop.run_in_executor(None, functools.partial(handler, event, **values))
                    
                    except Exception as e:
                        print(f"Error handling event {event_name}: {e}")
                        continue
                # AsyncExitStack automatically handles cleanup when exiting the context

    def _register_handler(self, event_name, func):
        if not isinstance(event_name, str):
            event_name = str(event_name)

        if event_name not in self._registry:
            self._registry[event_name] = []

        self._registry[event_name].append(func)

    def _get_handlers_for_event(self, event_name):
        if not isinstance(event_name, str):
            event_name = str(event_name)

        # TODO consider adding a cache
        handlers = []
        for event_name_pattern, registered_handlers in self._registry.items():
            if fnmatch.fnmatch(event_name, event_name_pattern):
                handlers.extend(registered_handlers)

        return handlers


local_handler = LocalHandler()