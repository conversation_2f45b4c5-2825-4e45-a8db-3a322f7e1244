# core/middleware/__init__.py
from .audit import AuditMiddleware
from .auth import AuthMiddleware
from .base import BaseMiddleware
from .cors import CORSMiddleware
from .exception import ExceptionHandlerMiddleware
from .logging import RequestLoggingMiddleware
from .rate_limit import RateLimitMiddleware
from .registry import MiddlewareRegistry, setup_middlewares
from .security import SecurityHeadersMiddleware
from .shared_config import (AUDIT_EXCLUDE_METHODS, AUDIT_EXCLUDE_PATHS,
                            COMMON_EXCLUDE_PATHS, DEFAULT_REQUEST_ID_HEADER,
                            REQUEST_LOGGING_EXCLUDE_PATHS)

__all__ = [
    # 中间件基础类
    'BaseMiddleware',
    'MiddlewareRegistry',
    'setup_middlewares',
    'middleware_config',

    # 中间件类
    'AuditMiddleware',
    'AuthMiddleware',
    'RateLimitMiddleware',
    'CORSMiddleware',
    'RequestLoggingMiddleware',
    'SecurityHeadersMiddleware',
    'ExceptionHandlerMiddleware',
]

# 中间件配置
middleware_config = {
    
    # 认证中间件配置
    "auth": {
        "enabled": True,
        "options": {
            "exclude_paths": [
                # 认证相关接口
                "/api/v1/auth/login",
                "/api/v1/auth/register",
                "/api/v1/wechat/login",
           
                # 文档和OpenAPI根路径
                "/docs",
                "/redoc",
                "/openapi.json",
                # 规格相关公共接口（参数化路径）- 更新为新的用户端接口
                "/api/v1/specs/product/{product_id}/specifications",
                "/api/v1/specs/product/{product_id}/combinations",
                # 可选：如需更灵活可用正则
                # "^/api/v1/specs/product/\\d+/specs$",
                # Health check endpoints (public access)
                '/api/v1/health/liveness',
                '/api/v1/health/readiness',
                # Billing public paths (Updated based on refactoring)
                '/api/v1/subscription_plans/list',
                '/api/v1/subscription_plans/active',
                '/api/v1/subscription_plans/details/{plan_id}',
                '/api/v1/payments/callback', # Payment gateway callback
                # Marketing public paths
                '/api/v1/campaign/active',
                '/api/v1/campaign/details/{campaign_id}',
                '/api/v1/invitation/validate/{code}',
                # Products module client paths (public access)
                '/api/v1/product/list',  # 商品列表
                '/api/v1/product/{product_id}',  # 商品详情
                '/api/v1/product/recommended',  # 推荐商品
                '/api/v1/product/category/{category_id}',  # 按分类查询商品
                '/api/v1/category/list',  # 分类列表
                '/api/v1/category/{category_id}',  # 分类详情
                '/api/v1/category/tree',  # 分类树
                '/api/v1/category/recommended',  # 推荐分类
                '/api/v1/spec/specs',  # 规格列表
                '/api/v1/spec/specs/{spec_id}/options',  # 规格选项
                '/api/v1/sku/products/{product_id}/skus',  # 商品SKU列表
                '/api/v1/sku/skus/{sku_id}',  # SKU详情
                '/api/v1/inventory/internal/product/{product_id}',  # 内部库存API
                # Shops module client paths (public access)
                '/api/v1/shop/list',  # 店铺列表和详情 (包含 /{shop_id})
                '/api/v1/shop/{shop_id}',
                # Albums module client paths (public access)
                '/api/v1/album/banner',  # 首页轮播图
            ],
            "use_oauth2": False
        }
    },
    
    # 速率限制中间件配置
    "ratelimit": {
        "enabled": True,
        "options": {
            "limit": 100,
            "window": 60
        }
    },
    
    # CORS中间件配置
    "cors": {
        "enabled": True,
        "options": {
            "allow_origins": ["*"],
            "allow_methods": ["*"],
            "allow_headers": ["*"]
        }
    },
    
    # 请求日志和请求ID中间件配置（合并了原requestid中间件功能）
    "requestlogging": {
        "enabled": True,
        "options": {
            "exclude_paths": REQUEST_LOGGING_EXCLUDE_PATHS,
            "header_name": DEFAULT_REQUEST_ID_HEADER
        }
    },
    
    # 安全头中间件配置
    "securityheaders": {
        "enabled": False,
        "options": {}
    },
    
    # 审计日志中间件配置
    "audit": {
        "enabled": True,
        "options": {
            "exclude_paths": AUDIT_EXCLUDE_PATHS,
            "exclude_methods": AUDIT_EXCLUDE_METHODS,
            "include_request_body": True,  # 启用请求体记录以支持未认证操作
            "include_response_body": False,
            "max_body_size": 2048,  # 增加大小以记录注册等操作的详细信息
            "unauthenticated_paths": {
                "/api/v1/auth/register",
                "/api/v1/auth/login",
                "/api/v1/auth/password-reset/request",
                "/api/v1/auth/password-reset",
                "/api/v1/wechat/login"
            }
        }
    },

    # 异常处理中间件配置
    "exceptionhandler": {
        "enabled": True,
        "options": {}
    },

}

# 中间件优先级顺序
MIDDLEWARE_PRIORITY = [
    "requestlogging", # 1. 请求日志和请求ID (最高优先级，合并了原requestid功能)
    "cors",          # 2. CORS
    "securityheaders", # 3. 安全头
    "ratelimit",    # 4. 速率限制
    "auth",          # 5. 认证
    "audit",         # 6. 审计日志 (在认证之后)
    "exceptionhandler", # 7. 异常处理
]
