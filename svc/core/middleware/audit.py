"""
API审计日志中间件

自动记录API操作的审计日志，包括用户操作、请求信息、响应状态等。
支持配置排除路径和方法，避免记录不必要的操作。
"""

import json
import logging
import time
from typing import Any, Awaitable, Callable, Dict, Optional, Set

from fastapi import Request, Response
from starlette.types import ASGIApp

from .base import BaseMiddleware
from .shared_config import (AUDIT_EXCLUDE_METHODS, AUDIT_EXCLUDE_PATHS,
                            RequestStateFields)

logger = logging.getLogger(__name__)


class AuditMiddleware(BaseMiddleware):
    """
    API审计日志中间件
    
    自动记录已认证用户的API操作，包括：
    - 操作类型和资源类型
    - 用户信息和请求信息
    - 响应状态和执行时间
    - 错误信息（如果有）
    """
    
    priority = 30  # 在认证中间件之后，异常处理中间件之前
    __name__ = "audit"
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        exclude_paths: Optional[Set[str]] = None,
        exclude_methods: Optional[Set[str]] = None,
        include_request_body: bool = False,
        include_response_body: bool = False,
        max_body_size: int = 1024,  # 最大记录的请求/响应体大小

        **options
    ):
        """
        初始化审计中间件

        Args:
            app: ASGI应用
            enabled: 是否启用中间件
            exclude_paths: 排除的路径集合
            exclude_methods: 排除的HTTP方法集合
            include_request_body: 是否包含请求体
            include_response_body: 是否包含响应体
            max_body_size: 最大记录的请求/响应体大小（字节）

            **options: 其他选项
        """
        super().__init__(app, enabled=enabled, **options)

        # 默认排除的路径（使用共享配置）
        self.exclude_paths = exclude_paths or AUDIT_EXCLUDE_PATHS

        # 默认排除的方法（只排除查询操作，使用共享配置）
        self.exclude_methods = exclude_methods or AUDIT_EXCLUDE_METHODS



        self.include_request_body = include_request_body
        self.include_response_body = include_response_body
        self.max_body_size = max_body_size
    
    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """处理请求并记录审计日志"""
        
        if not self.is_enabled():
            return await call_next(request)
        
        # 跳过不需要审计的路径和方法
        if self._should_skip_audit(request):
            return await call_next(request)
        
        # 获取用户信息（优先从token payload中获取）
        user_id = None
        token_payload = getattr(request.state, RequestStateFields.TOKEN_PAYLOAD, None)
        if token_payload:
            user_id = token_payload.get('user_id')

        # 如果token payload中没有，再从request.state中获取
        if user_id is None:
            user_id = getattr(request.state, RequestStateFields.USER_ID, None)

        # 记录所有请求（不再区分认证和未认证）

        # 复用请求日志中间件的开始时间，如果没有则创建新的
        start_time = getattr(request.state, RequestStateFields.START_TIME, None)
        if start_time is None:
            start_time = time.time()
            setattr(request.state, RequestStateFields.START_TIME, start_time)
        
        # 读取请求体（如果需要）
        request_body = None
        if self.include_request_body:
            request_body = await self._read_request_body(request)
        
        try:
            # 执行请求
            response = await call_next(request)
            
            # 读取响应体（如果需要）
            response_body = None
            if self.include_response_body:
                response_body = await self._read_response_body(response)
            
            # 复用或计算处理时间
            duration = getattr(request.state, RequestStateFields.PROCESS_TIME, None)
            if duration is None:
                duration = time.time() - start_time

            # 记录成功的操作
            await self._record_audit_log(
                request=request,
                response=response,
                user_id=user_id,
                duration=duration,
                status="success",
                request_body=request_body,
                response_body=response_body
            )
            
            return response
            
        except Exception as e:
            # 复用或计算处理时间
            duration = getattr(request.state, RequestStateFields.PROCESS_TIME, None)
            if duration is None:
                duration = time.time() - start_time

            # 记录失败的操作
            await self._record_audit_log(
                request=request,
                user_id=user_id,
                duration=duration,
                status="failure",
                error=str(e),
                request_body=request_body
            )
            raise
    
    def _should_skip_audit(self, request: Request) -> bool:
        """判断是否应该跳过审计"""
        # 检查排除路径（只排除系统路径）
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return True

        # 检查排除方法（只排除查询操作）
        if request.method in self.exclude_methods:
            return True

        return False



    async def _read_request_body(self, request: Request) -> Optional[Any]:
        """读取并解析请求体"""
        try:
            body = await request.body()
            if len(body) > self.max_body_size:
                return f"<body too large: {len(body)} bytes>"

            if not body:
                return None

            content_type = request.headers.get("content-type", "").lower()
            body_str = body.decode('utf-8', errors='ignore')

            # 根据Content-Type解析请求体
            if 'application/json' in content_type:
                # JSON格式
                try:
                    return json.loads(body_str)
                except json.JSONDecodeError:
                    return body_str[:self.max_body_size]
            elif 'application/x-www-form-urlencoded' in content_type:
                # 表单格式（OAuth2PasswordRequestForm）
                return self._parse_form_data(body_str)
            elif 'multipart/form-data' in content_type:
                # 多部分表单（文件上传等）
                # 暂时返回原始字符串，后续可以增强
                return body_str[:self.max_body_size]
            else:
                # 其他格式，返回原始字符串
                return body_str[:self.max_body_size]

        except Exception as e:
            logger.warning(f"读取请求体失败: {str(e)}")
            return None

    def _parse_form_data(self, form_data: str) -> dict:
        """解析表单数据"""
        try:
            from urllib.parse import unquote

            parsed_data = {}
            for pair in form_data.split('&'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    parsed_data[key] = unquote(value)

            return parsed_data
        except Exception as e:
            logger.warning(f"解析表单数据失败: {str(e)}")
            return {}
    
    async def _read_response_body(self, response: Response) -> Optional[str]:
        """读取响应体"""
        try:
            # 注意：这里需要小心处理，避免影响响应
            # 在实际实现中可能需要更复杂的处理
            return None  # 暂时不实现响应体读取
        except Exception as e:
            logger.warning(f"读取响应体失败: {str(e)}")
            return None
    
    async def _record_audit_log(
        self,
        request: Request,
        user_id: int,
        duration: float,
        status: str,
        response: Optional[Response] = None,
        error: Optional[str] = None,
        request_body: Optional[Any] = None,
        response_body: Optional[Any] = None
    ):
        """记录API审计日志"""
        try:
            # 解析操作类型、资源类型和资源ID
            action, resource_type, resource_id = self._parse_action_and_resource(request)

            # 构建详情（包含请求ID用于关联）
            details = {
                "request_id": getattr(request.state, RequestStateFields.REQUEST_ID, None),
                "method": request.method,
                "path": request.url.path,
                "duration": round(duration, 3),
                "user_agent": request.headers.get("user-agent"),
                "content_type": request.headers.get("content-type")
            }
            
            # 添加查询参数
            if request.query_params:
                details["query_params"] = dict(request.query_params)
            
            # 添加请求体
            if request_body is not None:
                details["request_body"] = request_body
            
            # 添加响应信息
            if response:
                details["status_code"] = response.status_code
                details["response_headers"] = dict(response.headers)
            
            # 添加响应体
            if response_body is not None:
                details["response_body"] = response_body
            
            # 添加错误信息
            if error:
                details["error"] = error
            
            # 统一提取用户信息（认证和未认证）
            username = self._extract_user_info(request, request_body)

            # 如果从URL中没有提取到资源ID，尝试从请求体中提取
            if not resource_id and request_body:
                resource_id = self._extract_resource_id_from_request(request, request_body, resource_type)
            
            # 异步记录审计日志
            await self._async_record_audit_log(
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                user_id=user_id,
                username=username,
                request=request,
                details=details,
                status=status,
                message=f"API {request.method} {request.url.path} - {status}"
            )
            
        except Exception as e:
            logger.error(f"记录API审计日志失败: {str(e)}", exc_info=True)
    
    def _parse_action_and_resource(self, request: Request) -> tuple[str, str, Optional[str]]:
        """从请求中解析操作类型、资源类型和资源ID"""
        method = request.method.lower()
        path = request.url.path.strip('/')

        # 映射HTTP方法到操作类型
        action_mapping = {
            'post': 'create',
            'put': 'update',
            'patch': 'update',
            'delete': 'delete',
            'get': 'read'
        }

        action = action_mapping.get(method, method)
        path_parts = [part for part in path.split('/') if part]

        # 提取资源类型和ID
        resource_type, resource_id = self._extract_resource_from_path(path_parts)

        return action, resource_type, resource_id

    def _extract_resource_from_path(self, path_parts: list[str]) -> tuple[str, Optional[str]]:
        """从路径部分提取资源类型和ID"""
        if not path_parts:
            return "unknown", None

        # 跳过API版本前缀
        start_idx = 0
        if path_parts[0] == 'api':
            start_idx = 1
            if len(path_parts) > 1 and path_parts[1] in ['v1', 'v2']:
                start_idx = 2
        elif path_parts[0] in ['v1', 'v2']:
            start_idx = 1

        if start_idx >= len(path_parts):
            return "unknown", None

        resource_type = path_parts[start_idx]
        resource_id = None

        # 查找资源ID（数字或UUID格式）
        for i in range(start_idx + 1, len(path_parts)):
            part = path_parts[i]
            if self._is_resource_id(part):
                resource_id = part
                break

        return resource_type, resource_id

    def _is_resource_id(self, part: str) -> bool:
        """判断路径部分是否为资源ID"""
        # 排除常见的操作名称
        action_keywords = {
            'register', 'login', 'logout', 'signin', 'signup', 'reset', 'forgot',
            'verify', 'confirm', 'activate', 'deactivate', 'enable', 'disable',
            'create', 'update', 'delete', 'list', 'search', 'filter', 'export',
            'import', 'upload', 'download', 'send', 'receive', 'check', 'validate'
        }

        if part.lower() in action_keywords:
            return False

        # 数字ID
        if part.isdigit():
            return True
        # UUID格式
        if len(part) == 36 and part.count('-') == 4:
            return True
        # 其他可能的ID格式（字母数字组合，长度合理，且不是常见单词）
        if (len(part) >= 8 and
            part.replace('-', '').replace('_', '').isalnum() and
            not part.lower() in action_keywords):
            return True
        return False

    def _extract_user_info(self, request: Request, request_body: Any) -> Optional[str]:
        """统一提取用户信息（认证和未认证）"""
        try:
            # 优先从token payload中获取（已认证用户）
            token_payload = getattr(request.state, RequestStateFields.TOKEN_PAYLOAD, None)
            if token_payload:
                # 优先返回用户名
                username = token_payload.get('username')
                if username:
                    return username
                # 如果没有用户名，返回用户ID的字符串形式
                user_id = token_payload.get('user_id')
                if user_id:
                    return str(user_id)

            # 从请求体中提取用户标识
            if isinstance(request_body, dict):
                username_fields = [
                    'email', 'username', 'phone', 'mobile',
                    'wechat_user_id', 'openid', 'unionid'
                ]
                for field in username_fields:
                    value = request_body.get(field)
                    if value:
                        return str(value)

            # 从查询参数中提取
            if hasattr(request, 'query_params'):
                query_fields = ['email', 'username', 'phone', 'mobile']
                for field in query_fields:
                    value = request.query_params.get(field)
                    if value:
                        return str(value)

            return None
        except Exception:
            return None

    def _extract_resource_id_from_request(self, request: Request, request_body: Any, resource_type: str) -> Optional[str]:
        """从请求体中提取资源ID"""
        try:
            if not isinstance(request_body, dict):
                return None

            # 定义资源类型对应的ID字段映射
            resource_id_mappings = {
                'auth': self._extract_auth_resource_id,
                'wechat': self._extract_wechat_resource_id,
                'user': self._extract_user_resource_id,
                'users': self._extract_user_resource_id,
                'product': self._extract_product_resource_id,
                'products': self._extract_product_resource_id,
            }

            # 使用特定的提取器
            extractor = resource_id_mappings.get(resource_type)
            if extractor:
                return extractor(request, request_body)

            # 通用ID提取
            return self._extract_generic_resource_id(request_body, resource_type)

        except Exception:
            return None

    def _extract_auth_resource_id(self, request: Request, request_body: dict) -> Optional[str]:
        """提取认证相关的资源ID"""
        path = request.url.path.lower()

        # 根据具体的认证操作提取不同的标识
        if any(keyword in path for keyword in ['register', 'signup']):
            return self._get_first_value(request_body, ['email', 'username', 'phone'])
        elif any(keyword in path for keyword in ['login', 'signin']):
            # OAuth2PasswordRequestForm使用username字段，但实际可能是email
            return self._get_first_value(request_body, ['username', 'email', 'phone'])
        elif any(keyword in path for keyword in ['reset', 'forgot']):
            return request_body.get('email')
        elif 'verify' in path:
            return request_body.get('email') or request_body.get('token')

        return self._get_first_value(request_body, ['email', 'username', 'phone'])

    def _extract_wechat_resource_id(self, request: Request, request_body: dict) -> Optional[str]:
        """提取微信相关的资源ID"""
        path = request.url.path.lower()

        # 微信登录通常使用code或openid作为标识
        if 'login' in path:
            return self._get_first_value(request_body, ['code', 'openid', 'unionid'])
        elif 'bind' in path:
            return self._get_first_value(request_body, ['wechat_user_id', 'openid', 'unionid'])
        elif 'userinfo' in path:
            # 对于用户信息相关的请求，优先从token payload中获取用户标识
            token_payload = getattr(request.state, RequestStateFields.TOKEN_PAYLOAD, None)
            if token_payload:
                # 优先返回用户名或用户ID
                username = token_payload.get('username')
                if username:
                    return username
                user_id = token_payload.get('user_id')
                if user_id:
                    return str(user_id)

            # 如果没有token信息，从请求体中提取
            return self._get_first_value(request_body, ['wechat_user_id', 'openid', 'unionid'])

        # 从嵌套的user_info中提取
        if request_body:
            user_info = request_body.get('user_info', {})
            if isinstance(user_info, dict):
                return self._get_first_value(user_info, ['openid', 'unionid'])

        return self._get_first_value(request_body, ['code', 'openid', 'unionid', 'wechat_user_id'])

    def _extract_user_resource_id(self, request: Request, request_body: dict) -> Optional[str]:
        """提取用户相关的资源ID"""
        return self._get_first_value(request_body, ['id', 'user_id', 'email', 'username'])

    def _extract_product_resource_id(self, request: Request, request_body: dict) -> Optional[str]:
        """提取产品相关的资源ID"""
        return self._get_first_value(request_body, ['id', 'product_id', 'sku', 'code', 'name'])

    def _extract_generic_resource_id(self, request_body: dict, resource_type: str) -> Optional[str]:
        """通用资源ID提取"""
        return self._get_first_value(request_body, [
            'id', f'{resource_type}_id', 'code', 'name', 'key'
        ])

    def _get_first_value(self, data: dict, keys: list[str]) -> Optional[str]:
        """从字典中获取第一个非空值"""
        for key in keys:
            value = data.get(key)
            if value:
                return str(value)
        return None

    async def _async_record_audit_log(
        self,
        action: str,
        resource_type: str,
        resource_id: Optional[str],
        user_id: Optional[int],
        username: Optional[str],
        request: Request,
        details: Dict[str, Any],
        status: str,
        message: str
    ):
        """异步记录审计日志"""
        db = None
        try:
            # 导入必要的模块（延迟导入避免循环依赖）
            from svc.apps.system.repositories.audit_log import \
                AuditLogRepository
            from svc.apps.system.services.audit_log import AuditLogService
            from svc.core.database.utils import get_session_for_script

            logger.debug(f"开始记录审计日志: {action} {resource_type} - {status}")

            # 获取数据库会话
            async with get_session_for_script() as db:
                logger.debug(f"数据库会话已创建: {id(db)}")

                # 创建审计日志服务
                audit_log_repo = AuditLogRepository(db=db)
                audit_service = AuditLogService(audit_log_repo=audit_log_repo)

                # 记录审计日志
                result = await audit_service.create_audit_log(
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    user_id=user_id,
                    username=username,
                    request=request,
                    details=details,
                    status=status,
                    message=message
                )

                # 检查结果
                if result.is_success:
                    # 提交事务
                    await db.commit()
                    logger.debug(f"审计日志记录成功: {action} {resource_type}")
                else:
                    # 记录失败，回滚事务
                    await db.rollback()
                    logger.error(f"审计日志服务返回失败: {result.result_msg}")

        except Exception as e:
            # 审计日志记录失败不应该影响业务流程
            logger.error(f"异步记录审计日志失败: {str(e)}", exc_info=True)
