from typing import Dict

from fastapi import Request, Response
from starlette.types import ASGIApp

from .base import BaseMiddleware


class SecurityHeadersMiddleware(BaseMiddleware):
    """安全头中间件，添加安全相关的HTTP头"""
    
    priority = 70

    __name__ = "security_headers"

    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        headers: Dict[str, str] = None,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        
        # 默认安全头
        self.headers = {
            "X-Frame-Options": "DENY",  # 防止点击劫持
            "X-Content-Type-Options": "nosniff",  # 防止MIME类型嗅探
            "X-XSS-Protection": "1; mode=block",  # XSS保护
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",  # 强制HTTPS
            "Content-Security-Policy": self._get_default_csp(),  # 内容安全策略
            "Referrer-Policy": "strict-origin-when-cross-origin",  # 引用策略
            "Permissions-Policy": self._get_default_permissions_policy(),  # 权限策略
        }
        
        # 更新自定义头
        if headers:
            self.headers.update(headers)
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if not self.is_enabled():
            return await call_next(request)
            
        # 处理请求
        response = await call_next(request)
        
        # 添加安全头
        for header_name, header_value in self.headers.items():
            response.headers[header_name] = header_value
        
        return response
    
    def _get_default_csp(self) -> str:
        """获取默认的内容安全策略"""
        return (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self' data: https:; "
            "object-src 'none'; "
            "base-uri 'self'; "
            "form-action 'self'; "
            "frame-ancestors 'none'; "
            "block-all-mixed-content; "
            "upgrade-insecure-requests;"
        )
    
    def _get_default_permissions_policy(self) -> str:
        """获取默认的权限策略"""
        return (
            "accelerometer=(), "
            "camera=(), "
            "geolocation=(), "
            "gyroscope=(), "
            "magnetometer=(), "
            "microphone=(), "
            "payment=(), "
            "usb=()"
        ) 