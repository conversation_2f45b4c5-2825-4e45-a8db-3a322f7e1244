"""
资源模型 Mixin。

约定：
- 继承本 Mixin 的 ORM 模型若声明了 `__resource_type__`，将在应用启动期被自动发现并注册到权限系统。
- 可选声明 `__resource_aliases__`（集合或列表），为同一模型提供多个资源别名。

最低期望字段（不强制声明类型，仅用于权限检查时的存在性判断）：
- id: 主键
- owner_id: 资源所有者用户ID（可缺省）
- tenant_id: 租户ID（可缺省）
- is_public: 是否公开（可缺省）

自定义访问控制：
- 如模型实现 `async def can_access(self, user_id: int, db) -> bool`，权限服务会在通用规则之后调用该方法以进行自定义判断。
"""

from __future__ import annotations

from typing import Iterable, Optional, Set


class ResourceMixin:
    """资源模型标记与默认行为。

    模型应在类级别设置：
    - __resource_type__: str  资源类型（必填）
    - __resource_aliases__: set[str] | list[str]  资源别名（可选）
    """

    # 派生类应覆盖此属性
    __resource_type__: Optional[str] = None
    __resource_aliases__: Optional[Iterable[str]] = None

    @classmethod
    def get_resource_type(cls) -> Optional[str]:
        rtype = getattr(cls, "__resource_type__", None)
        if rtype is None:
            return None
        if not isinstance(rtype, str) or not rtype:
            return None
        return rtype

    @classmethod
    def get_resource_aliases(cls) -> Set[str]:
        aliases = getattr(cls, "__resource_aliases__", None)
        if not aliases:
            return set()
        try:
            return {str(a) for a in aliases if str(a)}
        except Exception:
            return set()


