"""仓库层模块。

提供数据访问层的核心组件，包括基础仓库类、混入类、异常类和工具函数。
"""

from .base import BaseRepository, RepositoryConfig, FilterBuilder, FilterOperator
from .exceptions import (
    RepositoryError,
    RecordNotFoundError,
    ValidationError,
    BatchOperationError,
    DatabaseConnectionError,
    QueryExecutionError,
    CacheError
)
from .utils import (
    validate_field_exists,
    prepare_data,
    add_timestamps,
    build_cache_key,
    safe_batch_size,
    build_filter_conditions,
    safe_limit_offset,
    get_datetime_utils
)
from .mixins import QueryMixin, BatchMixin, SoftDeleteMixin, CacheMixin

__all__ = [
    # 主要类
    "BaseRepository",
    "RepositoryConfig",
    "FilterBuilder",
    "FilterOperator",
    
    # 异常类
    "RepositoryError",
    "RecordNotFoundError", 
    "ValidationError",
    "BatchOperationError",
    "DatabaseConnectionError",
    "QueryExecutionError",
    "CacheError",
    
    # 混入类
    "QueryMixin",
    "BatchMixin",
    "SoftDeleteMixin", 
    "CacheMixin",
    
    # 工具函数
    "validate_field_exists",
    "prepare_data",
    "add_timestamps",
    "build_cache_key",
    "safe_batch_size",
    "build_filter_conditions",
    "safe_limit_offset",
    "get_datetime_utils"
]