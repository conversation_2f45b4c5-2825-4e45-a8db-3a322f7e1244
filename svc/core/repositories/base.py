"""优化后的基础仓储模式实现。

提供通用的数据访问方法，专门针对PostgreSQL数据库优化，支持JSON字段操作。
重新设计后保持核心功能完整性，优化性能，简化API，减少复杂度。
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Generic, List, Optional, Tuple, Type, TypeVar, Union

from sqlalchemy import Column, and_, asc, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import Select
from sqlalchemy.dialects.postgresql import JSONB

from .exceptions import RecordNotFoundError, ValidationError, RepositoryError
from .utils import (
    validate_field_exists, prepare_data, add_timestamps,
    build_filter_conditions, safe_limit_offset, get_datetime_utils
)
from .mixins import QueryMixin, BatchMixin, SoftDeleteMixin, CacheMixin, RelationMixin

# 定义模型类型变量
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class FilterOperator(str, Enum):
    """过滤操作符枚举"""
    # 基础比较操作符
    EQ = "eq"
    NE = "ne" 
    GT = "gt"
    GTE = "gte"
    LT = "lt"
    LTE = "lte"
    
    # 文本操作符
    LIKE = "like"
    ILIKE = "ilike"
    
    # 集合操作符
    IN = "in"
    NOT_IN = "not_in"
    
    # 空值检查
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"
    
    # 范围查询
    BETWEEN = "between"
    
    # PostgreSQL JSON 操作符
    JSON_CONTAINS = "json_contains"
    JSON_CONTAINED_BY = "json_contained_by"
    JSON_HAS_KEY = "json_has_key"
    JSON_HAS_ANY_KEY = "json_has_any_key"
    JSON_HAS_ALL_KEYS = "json_has_all_keys"


@dataclass
class FilterCondition:
    """过滤条件"""
    field: str
    operator: FilterOperator
    value: Any


class FilterBuilder:
    """过滤条件构建器"""
    
    def __init__(self):
        self.conditions: List[FilterCondition] = []
        self.logic_operator: str = "and"
    
    def add_condition(self, field: str, operator: FilterOperator, value: Any) -> 'FilterBuilder':
        """添加过滤条件"""
        self.conditions.append(FilterCondition(field, operator, value))
        return self
    
    def set_logic_operator(self, operator: str) -> 'FilterBuilder':
        """设置逻辑操作符"""
        self.logic_operator = operator
        return self
    
    def build_sql_condition(self, column, condition: FilterCondition):
        """构建SQL条件"""
        try:
            value = condition.value
            
            # 基础比较操作
            if condition.operator == FilterOperator.EQ:
                return column == value
            elif condition.operator == FilterOperator.NE:
                return column != value
            elif condition.operator == FilterOperator.GT:
                return column > value
            elif condition.operator == FilterOperator.GTE:
                return column >= value
            elif condition.operator == FilterOperator.LT:
                return column < value
            elif condition.operator == FilterOperator.LTE:
                return column <= value
                
            # 文本操作
            elif condition.operator == FilterOperator.LIKE:
                return column.like(value)
            elif condition.operator == FilterOperator.ILIKE:
                return column.ilike(value)
                
            # 集合操作
            elif condition.operator == FilterOperator.IN:
                if isinstance(value, (list, tuple)) and value:
                    return column.in_(value)
            elif condition.operator == FilterOperator.NOT_IN:
                if isinstance(value, (list, tuple)) and value:
                    return ~column.in_(value)
                    
            # 空值检查
            elif condition.operator == FilterOperator.IS_NULL:
                return column.is_(None)
            elif condition.operator == FilterOperator.IS_NOT_NULL:
                return column.is_not(None)
                
            # 范围查询
            elif condition.operator == FilterOperator.BETWEEN:
                if isinstance(value, (list, tuple)) and len(value) == 2:
                    return column.between(value[0], value[1])
                    
            # PostgreSQL JSON 操作符
            elif condition.operator == FilterOperator.JSON_CONTAINS:
                return column.op('@>')(func.cast(value, JSONB))
            elif condition.operator == FilterOperator.JSON_CONTAINED_BY:
                return column.op('<@')(func.cast(value, JSONB))
            elif condition.operator == FilterOperator.JSON_HAS_KEY:
                return column.op('?')(value)
            elif condition.operator == FilterOperator.JSON_HAS_ANY_KEY:
                if isinstance(value, (list, tuple)):
                    return column.op('?|')(value)
            elif condition.operator == FilterOperator.JSON_HAS_ALL_KEYS:
                if isinstance(value, (list, tuple)):
                    return column.op('?&')(value)
                
        except Exception:
            pass

        return None
    
    @classmethod
    def from_dict(cls, filters: Dict[str, Any]) -> 'FilterBuilder':
        """从字典创建过滤器"""
        builder = cls()
        
        for key, value in filters.items():
            if value is None:
                continue
                
            if '__' in key:
                field, op_str = key.rsplit('__', 1)
                try:
                    operator = FilterOperator(op_str)
                except ValueError:
                    field = key
                    operator = FilterOperator.EQ
            else:
                field = key
                operator = FilterOperator.EQ
                
            builder.add_condition(field, operator, value)
        
        return builder


class RepositoryConfig:
    """仓库配置类"""
    
    def __init__(self, **kwargs):
        self.default_soft_delete: bool = kwargs.get('default_soft_delete', True)
        self.default_batch_size: int = kwargs.get('default_batch_size', 1000)
        self.auto_timestamps: bool = kwargs.get('auto_timestamps', True)
        self.cache_enabled: bool = kwargs.get('cache_enabled', False)
        self.cache_ttl: int = kwargs.get('cache_ttl', 3600)
        self.max_batch_size: int = kwargs.get('max_batch_size', 5000)
        self.enable_logging: bool = kwargs.get('enable_logging', True)


class BaseRepository(
    Generic[ModelType, CreateSchemaType, UpdateSchemaType],
    QueryMixin,
    BatchMixin,
    SoftDeleteMixin,
    CacheMixin,
    RelationMixin
):
    """
    优化后的通用仓储基类，专门针对PostgreSQL数据库。
    
    主要特性：
    - 支持PostgreSQL JSON/JSONB字段操作
    - 统一的过滤查询API
    - 高性能批量操作
    - 软删除支持
    - 完整的CRUD操作
    - 缓存支持
    """
    
    def __init__(
        self,
        db: AsyncSession,
        model: Type[ModelType],
        config: Optional[RepositoryConfig] = None,
        redis=None
    ):
        """初始化仓库
        
        Args:
            db: 数据库会话
            model: 模型类
            config: 配置对象
            redis: Redis客户端实例
        """
        self.db = db
        self.model = model
        self.config = config or RepositoryConfig()
        
        # 初始化混入类
        CacheMixin.__init__(self, redis)
        
        # 延迟导入的日期时间工具
        self._datetime_utils = None
    
    def _get_datetime_utils(self):
        """获取日期时间工具函数"""
        if self._datetime_utils is None:
            self._datetime_utils = get_datetime_utils()
        return self._datetime_utils
    
    # ==================== 核心CRUD方法 ====================
    
    async def create(self, data: Any) -> ModelType:
        """创建新实体
        
        Args:
            data: 创建数据
            
        Returns:
            ModelType: 创建的实体对象
        """
        model_data = prepare_data(data, self.model)
        
        if self.config.auto_timestamps:
            model_data = add_timestamps(model_data, is_update=False, model=self.model)
        
        obj = self.model(**model_data)
        self.db.add(obj)
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def update(self, obj: ModelType, data: Dict[str, Any]) -> ModelType:
        """更新实体
        
        Args:
            obj: 实体对象
            data: 更新数据
            
        Returns:
            ModelType: 更新后的实体对象
        """
        update_data = prepare_data(data, self.model)
        
        if self.config.auto_timestamps:
            update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        for field, value in update_data.items():
            if hasattr(obj, field):
                setattr(obj, field, value)
        
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def delete(self, obj: ModelType, soft: bool = None) -> None:
        """删除实体
        
        Args:
            obj: 实体对象
            soft: 是否软删除，None表示使用配置默认值
        """
        if soft is None:
            soft = self.config.default_soft_delete
        
        if soft:
            await self.soft_delete(obj)
        else:
            await self.permanent_delete(obj)

    # ==================== 实用方法 ====================
    
    async def update_by_id(self, id: int, data: Dict[str, Any]) -> Optional[ModelType]:
        """根据ID更新实体
        
        Args:
            id: 实体ID
            data: 更新数据
            
        Returns:
            Optional[ModelType]: 更新后的实体对象，不存在则返回None
        """
        obj = await self.get_by_id(id)
        if not obj:
            return None
        
        return await self.update(obj, data)
    
    async def delete_by_id(self, id: int, soft: bool = None) -> Optional[ModelType]:
        """根据ID删除实体
        
        Args:
            id: 实体ID
            soft: 是否软删除，None表示使用配置默认值
            
        Returns:
            Optional[ModelType]: 删除前的实体对象，不存在则返回None
        """
        obj = await self.get_by_id(id)
        if not obj:
            return None
        
        await self.delete(obj, soft)
        return obj
    
    async def get_or_create(self, defaults: Dict[str, Any] = None, **filters) -> Tuple[ModelType, bool]:
        """获取或创建实体
        
        Args:
            defaults: 创建时的默认值
            **filters: 查找条件
            
        Returns:
            Tuple[ModelType, bool]: (实体对象, 是否新创建)
        """
        # 尝试查找现有实体
        obj = await self.get_one(**filters)
        if obj:
            return obj, False
        
        # 创建新实体
        create_data = {}
        if defaults:
            create_data.update(defaults)
        create_data.update(filters)
        
        obj = await self.create(create_data)
        return obj, True
    
    async def increment_field(self, id: int, field: str, amount: int = 1) -> Optional[ModelType]:
        """增量更新字段值
        
        Args:
            id: 实体ID
            field: 字段名
            amount: 增量值
            
        Returns:
            Optional[ModelType]: 更新后的实体对象
        """
        if not validate_field_exists(self.model, field):
            raise ValidationError(field, amount, f"Field '{field}' does not exist")
        
        obj = await self.get_by_id(id)
        if not obj:
            return None
        
        # 获取当前值
        current_value = getattr(obj, field, 0)
        if current_value is None:
            current_value = 0
        
        # 计算新值
        new_value = current_value + amount
        
        # 更新字段
        return await self.update(obj, {field: new_value})
    
    async def update_status(self, obj: ModelType, status: str) -> ModelType:
        """更新状态
        
        Args:
            obj: 实体对象
            status: 新状态
            
        Returns:
            ModelType: 更新后的实体对象
        """
        if not validate_field_exists(self.model, 'status'):
            raise ValidationError("status", status, "Model does not have status field")
        
        return await self.update(obj, {"status": status})
    
    async def toggle_active(self, obj: ModelType) -> ModelType:
        """切换激活状态
        
        Args:
            obj: 实体对象
            
        Returns:
            ModelType: 更新后的实体对象
        """
        if not validate_field_exists(self.model, 'is_active'):
            raise ValidationError("is_active", None, "Model does not have is_active field")
        
        current_active = getattr(obj, 'is_active', False)
        return await self.update(obj, {"is_active": not current_active})

    # ==================== JSON查询方法 ====================
    
    async def json_query(
        self,
        field: str,
        json_condition: Dict[str, Any],
        **other_filters
    ) -> List[ModelType]:
        """JSON字段查询
        
        Args:
            field: JSON字段名
            json_condition: JSON查询条件
            **other_filters: 其他过滤条件
            
        Returns:
            List[ModelType]: 查询结果
        """
        if not validate_field_exists(self.model, field):
            return []
            
        filters = FilterBuilder()
        
        for operator, value in json_condition.items():
            try:
                op = FilterOperator(operator)
                filters.add_condition(field, op, value)
            except ValueError:
                continue
                
        # 构建查询
        query = self._build_base_query()
        
        # 应用JSON过滤条件
        conditions = []
        for condition in filters.conditions:
            column = getattr(self.model, condition.field)
            sql_condition = filters.build_sql_condition(column, condition)
            if sql_condition is not None:
                conditions.append(sql_condition)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 应用其他过滤条件
        query = self._apply_filters(query, other_filters)
        
        result = await self.db.execute(query)
        return result.scalars().all()

    async def json_contains_query(
        self,
        field: str,
        contains_value: Dict[str, Any],
        **other_filters
    ) -> List[ModelType]:
        """JSON包含查询
        
        Args:
            field: JSON字段名
            contains_value: 包含的值
            **other_filters: 其他过滤条件
            
        Returns:
            List[ModelType]: 查询结果
        """
        filters = FilterBuilder().add_condition(field, FilterOperator.JSON_CONTAINS, contains_value)
        
        # 构建查询
        query = self._build_base_query()
        
        # 应用JSON过滤条件
        conditions = []
        for condition in filters.conditions:
            column = getattr(self.model, condition.field)
            sql_condition = filters.build_sql_condition(column, condition)
            if sql_condition is not None:
                conditions.append(sql_condition)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 应用其他过滤条件
        query = self._apply_filters(query, other_filters)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def json_has_key_query(
        self,
        field: str,
        key: str,
        **other_filters
    ) -> List[ModelType]:
        """JSON包含键查询
        
        Args:
            field: JSON字段名
            key: 键名
            **other_filters: 其他过滤条件
            
        Returns:
            List[ModelType]: 查询结果
        """
        filters = FilterBuilder().add_condition(field, FilterOperator.JSON_HAS_KEY, key)
        
        # 构建查询
        query = self._build_base_query()
        
        # 应用JSON过滤条件
        conditions = []
        for condition in filters.conditions:
            column = getattr(self.model, condition.field)
            sql_condition = filters.build_sql_condition(column, condition)
            if sql_condition is not None:
                conditions.append(sql_condition)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 应用其他过滤条件
        query = self._apply_filters(query, other_filters)
        
        result = await self.db.execute(query)
        return result.scalars().all()