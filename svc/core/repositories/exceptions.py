"""仓库层自定义异常类。

定义仓库操作中可能出现的各种异常类型，提供标准化的错误处理。
"""


class RepositoryError(Exception):
    """基础仓库异常类"""
    
    def __init__(self, message: str, code: str = None, details: dict = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class RecordNotFoundError(RepositoryError):
    """记录不存在异常"""
    
    def __init__(self, resource_type: str, resource_id: any, details: dict = None):
        message = f"{resource_type} with id {resource_id} not found"
        super().__init__(message, "RECORD_NOT_FOUND", details)


class ValidationError(RepositoryError):
    """数据验证异常"""
    
    def __init__(self, field: str, value: any, reason: str, details: dict = None):
        message = f"Validation failed for field '{field}' with value '{value}': {reason}"
        super().__init__(message, "VALIDATION_ERROR", details)


class BatchOperationError(RepositoryError):
    """批量操作异常"""
    
    def __init__(self, operation: str, failed_count: int, total_count: int, details: dict = None):
        message = f"Batch {operation} failed: {failed_count}/{total_count} operations failed"
        super().__init__(message, "BATCH_OPERATION_ERROR", details)


class DatabaseConnectionError(RepositoryError):
    """数据库连接异常"""
    
    def __init__(self, message: str = "Database connection failed", details: dict = None):
        super().__init__(message, "DATABASE_CONNECTION_ERROR", details)


class QueryExecutionError(RepositoryError):
    """查询执行异常"""
    
    def __init__(self, query: str, error: str, details: dict = None):
        message = f"Query execution failed: {error}"
        super().__init__(message, "QUERY_EXECUTION_ERROR", details)


class CacheError(RepositoryError):
    """缓存操作异常"""
    
    def __init__(self, operation: str, error: str, details: dict = None):
        message = f"Cache {operation} failed: {error}"
        super().__init__(message, "CACHE_ERROR", details)
