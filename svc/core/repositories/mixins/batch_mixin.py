"""批量操作混入类。

提供优化的批量操作功能，包括批量更新、创建、删除等。
"""

from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from sqlalchemy import update, delete, and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from ..utils import safe_batch_size, prepare_data, add_timestamps, validate_field_exists
from ..exceptions import BatchOperationError

ModelType = TypeVar("ModelType")


class BatchMixin:
    """批量操作混入类"""
    
    async def batch_update(
        self,
        ids: List[int],
        data: Dict[str, Any],
        batch_size: int = 1000
    ) -> int:
        """批量更新记录
        
        Args:
            ids: 记录ID列表
            data: 更新数据
            batch_size: 批量大小
            
        Returns:
            int: 更新的记录数量
        """
        if not ids or not data:
            return 0
        
        # 验证ID字段
        if not validate_field_exists(self.model, 'id'):
            raise BatchOperationError("update", 0, len(ids), {"error": "Model has no 'id' field"})
        
        # 预处理数据
        update_data = prepare_data(data, self.model)
        update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        if not update_data:
            return 0
        
        total_updated = 0
        safe_batch = safe_batch_size(batch_size)
        
        # 分批处理
        for i in range(0, len(ids), safe_batch):
            batch_ids = ids[i:i + safe_batch]
            
            # 构建更新条件
            conditions = [self.model.id.in_(batch_ids)]
            if validate_field_exists(self.model, 'deleted_at'):
                conditions.append(self.model.deleted_at.is_(None))
            
            # 执行批量更新
            stmt = update(self.model).where(and_(*conditions)).values(**update_data)
            result = await self.db.execute(stmt)
            total_updated += result.rowcount
        
        await self.db.flush()
        return total_updated
    
    async def bulk_create(
        self,
        data_list: List[Dict[str, Any]],
        batch_size: int = 1000,
        return_objects: bool = False
    ) -> Union[List[ModelType], int]:
        """批量创建记录
        
        Args:
            data_list: 数据列表
            batch_size: 批量大小
            return_objects: 是否返回对象列表
            
        Returns:
            Union[List[ModelType], int]: 创建的对象列表或数量
        """
        if not data_list:
            return [] if return_objects else 0
        
        # 预处理数据
        processed_data = []
        for data in data_list:
            processed = prepare_data(data, self.model)
            processed = add_timestamps(processed, is_update=False, model=self.model)
            if processed:
                processed_data.append(processed)
        
        if not processed_data:
            return [] if return_objects else 0
        
        safe_batch = safe_batch_size(batch_size)
        
        if return_objects:
            # 返回对象列表
            created_objects = []
            for i in range(0, len(processed_data), safe_batch):
                batch = processed_data[i:i + safe_batch]
                batch_objects = []
                
                for item_data in batch:
                    obj = self.model(**item_data)
                    self.db.add(obj)
                    batch_objects.append(obj)
                
                await self.db.flush()
                for obj in batch_objects:
                    await self.db.refresh(obj)
                
                created_objects.extend(batch_objects)
            
            return created_objects
        else:
            # 返回创建数量
            total_created = 0
            for i in range(0, len(processed_data), safe_batch):
                batch = processed_data[i:i + safe_batch]
                stmt = self.model.__table__.insert().values(batch)
                await self.db.execute(stmt)
                total_created += len(batch)
            
            await self.db.flush()
            return total_created
    
    async def batch_delete(
        self,
        ids: List[int],
        soft_delete: bool = True,
        batch_size: int = 1000
    ) -> int:
        """批量删除记录
        
        Args:
            ids: 记录ID列表
            soft_delete: 是否软删除
            batch_size: 批量大小
            
        Returns:
            int: 删除的记录数量
        """
        if not ids:
            return 0
        
        # 验证ID字段
        if not validate_field_exists(self.model, 'id'):
            raise BatchOperationError("delete", 0, len(ids), {"error": "Model has no 'id' field"})
        
        total_deleted = 0
        safe_batch = safe_batch_size(batch_size)
        
        # 分批处理
        for i in range(0, len(ids), safe_batch):
            batch_ids = ids[i:i + safe_batch]
            
            # 调试信息
            has_deleted_at = validate_field_exists(self.model, 'deleted_at')
           
            
            if soft_delete and has_deleted_at:
                # 软删除
                update_data = {'deleted_at': self._get_datetime_utils()()}
                if validate_field_exists(self.model, 'is_active'):
                    update_data['is_active'] = False
                if validate_field_exists(self.model, 'status'):
                    update_data['status'] = 'deleted'
                
                conditions = [
                    self.model.id.in_(batch_ids),
                    self.model.deleted_at.is_(None)
                ]
                
                stmt = update(self.model).where(and_(*conditions)).values(**update_data)
                print(f"✅ 执行软删除，更新数据: {update_data}")
                print(f"✅ 软删除条件: {conditions}")
            else:
                # 硬删除
                stmt = delete(self.model).where(self.model.id.in_(batch_ids))
                print(f"❌ 执行硬删除，原因: soft_delete={soft_delete}, has_deleted_at={has_deleted_at}")
            
            result = await self.db.execute(stmt)
            total_deleted += result.rowcount
        
        await self.db.flush()
        return total_deleted
    
    async def batch_get(
        self,
        ids: List[int],
        include_deleted: bool = False
    ) -> List[ModelType]:
        """批量获取记录
        
        Args:
            ids: 记录ID列表
            include_deleted: 是否包含已删除记录
            
        Returns:
            List[ModelType]: 记录列表
        """
        if not ids:
            return []
        
        # 验证ID字段
        if not validate_field_exists(self.model, 'id'):
            return []
        
        query = select(self.model).where(self.model.id.in_(ids))
        
        # 软删除过滤
        if not include_deleted and validate_field_exists(self.model, 'deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def bulk_upsert(
        self,
        data_list: List[Dict[str, Any]],
        unique_fields: List[str],
        batch_size: int = 1000
    ) -> int:
        """批量插入或更新记录
        
        Args:
            data_list: 数据列表
            unique_fields: 唯一字段列表
            batch_size: 批量大小
            
        Returns:
            int: 处理的记录数量
        """
        if not data_list or not unique_fields:
            return 0
        
        # 验证唯一字段
        for field in unique_fields:
            if not validate_field_exists(self.model, field):
                raise BatchOperationError("upsert", 0, len(data_list), {"error": f"Field '{field}' not found"})
        
        # 预处理数据
        processed_data = []
        for data in data_list:
            processed = prepare_data(data, self.model)
            processed = add_timestamps(processed, is_update=False, model=self.model)
            if processed:
                processed_data.append(processed)
        
        if not processed_data:
            return 0
        
        total_processed = 0
        safe_batch = safe_batch_size(batch_size)
        
        # 分批处理
        for i in range(0, len(processed_data), safe_batch):
            batch = processed_data[i:i + safe_batch]
            
            # 使用ON CONFLICT进行批量upsert
            stmt = self.model.__table__.insert().values(batch)
            
            # 构建ON CONFLICT子句
            conflict_fields = [getattr(self.model, field) for field in unique_fields]
            update_fields = {k: v for k, v in batch[0].items() if k not in unique_fields}
            
            if update_fields:
                stmt = stmt.on_conflict_do_update(
                    index_elements=conflict_fields,
                    set_=update_fields
                )
            
            await self.db.execute(stmt)
            total_processed += len(batch)
        
        await self.db.flush()
        return total_processed
    
    async def batch_update_status(
        self,
        ids: List[int],
        status: str,
        batch_size: int = 1000
    ) -> int:
        """批量更新状态
        
        Args:
            ids: 记录ID列表
            status: 新状态
            batch_size: 批量大小
            
        Returns:
            int: 更新的记录数量
        """
        if not validate_field_exists(self.model, 'status'):
            raise BatchOperationError("update_status", 0, len(ids), {"error": "Model has no 'status' field"})
        
        return await self.batch_update(ids, {"status": status}, batch_size)
    
    def _get_datetime_utils(self):
        """获取日期时间工具函数"""
        from ..utils import get_datetime_utils
        return get_datetime_utils()
