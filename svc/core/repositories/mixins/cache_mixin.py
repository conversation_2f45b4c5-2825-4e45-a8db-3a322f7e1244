"""缓存支持混入类。

提供简单的缓存功能，包括缓存获取、设置、失效等。
"""

from typing import Any, Callable, Dict, List, Optional, Type, TypeVar
import json
import hashlib
from functools import wraps

from ..utils import build_cache_key
from ..exceptions import CacheError

ModelType = TypeVar("ModelType")


class CacheMixin:
    """缓存支持混入类"""
    
    def __init__(self, redis=None):
        """初始化缓存混入类
        
        Args:
            redis: Redis客户端实例
        """
        self.redis = redis
        self._cache_enabled = redis is not None
    
    async def get_cached(
        self,
        key: str,
        fetch_func: Callable,
        ttl: int = 3600,
        **kwargs
    ) -> Any:
        """获取缓存数据，如果不存在则调用获取函数
        
        Args:
            key: 缓存键
            fetch_func: 获取数据的函数
            ttl: 缓存过期时间（秒）
            **kwargs: 传递给fetch_func的参数
            
        Returns:
            Any: 缓存的数据
        """
        if not self._cache_enabled:
            return await fetch_func(**kwargs)
        
        try:
            # 尝试从缓存获取
            cached_data = await self.redis.get(key)
            if cached_data:
                return json.loads(cached_data)
            
            # 缓存未命中，调用获取函数
            data = await fetch_func(**kwargs)
            
            # 设置缓存
            if data is not None:
                await self.redis.setex(key, ttl, json.dumps(data, default=str))
            
            return data
            
        except Exception as e:
            # 缓存操作失败，直接调用获取函数
            return await fetch_func(**kwargs)
    
    async def set_cache(self, key: str, data: Any, ttl: int = 3600) -> bool:
        """设置缓存
        
        Args:
            key: 缓存键
            data: 要缓存的数据
            ttl: 缓存过期时间（秒）
            
        Returns:
            bool: 是否设置成功
        """
        if not self._cache_enabled:
            return False
        
        try:
            await self.redis.setex(key, ttl, json.dumps(data, default=str))
            return True
        except Exception as e:
            raise CacheError("set", str(e))
    
    async def get_cache(self, key: str) -> Optional[Any]:
        """获取缓存数据
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存的数据或None
        """
        if not self._cache_enabled:
            return None
        
        try:
            cached_data = await self.redis.get(key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            raise CacheError("get", str(e))
    
    async def delete_cache(self, key: str) -> bool:
        """删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否删除成功
        """
        if not self._cache_enabled:
            return False
        
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            raise CacheError("delete", str(e))
    
    async def invalidate_cache(self, pattern: str) -> int:
        """批量删除匹配模式的缓存
        
        Args:
            pattern: 缓存键模式
            
        Returns:
            int: 删除的缓存数量
        """
        if not self._cache_enabled:
            return 0
        
        try:
            # 获取匹配的键
            keys = await self.redis.keys(pattern)
            if keys:
                await self.redis.delete(*keys)
                return len(keys)
            return 0
        except Exception as e:
            raise CacheError("invalidate", str(e))
    
    async def warm_cache(self, keys: List[str], fetch_func: Callable) -> Dict[str, Any]:
        """预热缓存
        
        Args:
            keys: 缓存键列表
            fetch_func: 获取数据的函数
            fetch_func: 获取数据的函数
            
        Returns:
            Dict[str, Any]: 预热结果
        """
        if not self._cache_enabled:
            return {}
        
        results = {}
        for key in keys:
            try:
                data = await fetch_func(key)
                if data is not None:
                    await self.set_cache(key, data)
                    results[key] = data
            except Exception as e:
                # 记录错误但继续处理其他键
                results[key] = None
        
        return results
    
    def _generate_cache_key(self, method: str, **params) -> str:
        """生成缓存键
        
        Args:
            method: 方法名
            **params: 参数
            
        Returns:
            str: 缓存键
        """
        model_name = self.model.__name__.lower()
        return build_cache_key(f"{model_name}:{method}", **params)
    
    def cached(self, ttl: int = 3600, key_prefix: str = None):
        """缓存装饰器
        
        Args:
            ttl: 缓存过期时间（秒）
            key_prefix: 缓存键前缀
            
        Returns:
            Callable: 装饰器函数
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(self, *args, **kwargs):
                if not self._cache_enabled:
                    return await func(self, *args, **kwargs)
                
                # 生成缓存键
                prefix = key_prefix or f"{self.model.__name__.lower()}:{func.__name__}"
                cache_key = self._generate_cache_key(prefix, args=args, kwargs=kwargs)
                
                # 尝试从缓存获取
                cached_data = await self.get_cache(cache_key)
                if cached_data is not None:
                    return cached_data
                
                # 缓存未命中，调用原函数
                result = await func(self, *args, **kwargs)
                
                # 设置缓存
                if result is not None:
                    await self.set_cache(cache_key, result, ttl)
                
                return result
            
            return wrapper
        return decorator
    
    async def clear_model_cache(self) -> int:
        """清除模型相关的所有缓存
        
        Returns:
            int: 清除的缓存数量
        """
        if not self._cache_enabled:
            return 0
        
        pattern = f"{self.model.__name__.lower()}:*"
        return await self.invalidate_cache(pattern)
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        if not self._cache_enabled:
            return {"enabled": False}
        
        try:
            info = await self.redis.info()
            return {
                "enabled": True,
                "used_memory": info.get("used_memory", 0),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0)
            }
        except Exception as e:
            return {"enabled": True, "error": str(e)}
