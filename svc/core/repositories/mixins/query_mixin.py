"""查询增强混入类。

提供简化的查询API和性能优化功能。
"""

from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from sqlalchemy import Select, select, and_, or_, desc, asc, func
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from ..utils import validate_field_exists, build_filter_conditions, safe_limit_offset

ModelType = TypeVar("ModelType")
logger = logging.getLogger(__name__)


def validate_id_field(id_value: Any) -> bool:
    """验证ID字段值
    
    Args:
        id_value: ID值
        
    Returns:
        bool: ID是否有效
    """
    if id_value is None:
        return False
    
    # 数字类型
    if isinstance(id_value, (int, float)):
        return id_value > 0
    
    # 字符串类型
    if isinstance(id_value, str):
        return len(id_value.strip()) > 0
    
    return True


class QueryMixin:
    """查询增强混入类"""
    
    def _get_default_load_options(self) -> List[Any]:
        """获取默认的关系加载选项
        
        根据模型的关系定义，返回默认的加载选项。
        子类可以重写此方法来提供特定的加载策略。
        
        Returns:
            List[Any]: 默认的加载选项列表
        """
        from sqlalchemy.orm import selectinload
        
        load_options = []
        
        # 检查模型是否有关系属性
        if hasattr(self.model, '__mapper__'):
            mapper = self.model.__mapper__
            
            # 为selectin关系添加预加载
            for rel_name, rel_prop in mapper.relationships.items():
                if rel_prop.lazy == 'selectin':
                    try:
                        # 动态创建selectinload
                        load_options.append(selectinload(getattr(self.model, rel_name)))
                        logger.debug(f"Added default selectinload for {self.model.__name__}.{rel_name}")
                    except Exception as e:
                        logger.warning(f"Failed to add selectinload for {self.model.__name__}.{rel_name}: {str(e)}")
        
        return load_options
    
    def _build_base_query(self, include_deleted: bool = False, load_options: Optional[List[Any]] = None) -> Select:
        """构建基础查询
        
        Args:
            include_deleted: 是否包含已删除记录
            load_options: SQLAlchemy加载选项
            
        Returns:
            Select: 基础查询对象
        """
        query = select(self.model)
        
        # 如果没有提供load_options，使用默认的
        if load_options is None:
            load_options = self._get_default_load_options()
        
        # 应用加载选项
        if load_options:
            try:
                query = query.options(*load_options)
                logger.debug(f"Applied load options: {load_options}")
            except Exception as e:
                logger.error(f"Failed to apply load options {load_options}: {str(e)}")
                # 不抛出异常，但记录错误，让查询继续执行
        
        # 软删除过滤
        if not include_deleted and validate_field_exists(self.model, 'deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))
        
        return query
    
    def _apply_filters(self, query: Select, filters: Dict[str, Any]) -> Select:
        """应用过滤条件
        
        Args:
            query: 查询对象
            filters: 过滤条件
            
        Returns:
            Select: 应用过滤条件后的查询
        """
        if not filters:
            return query
        
        conditions = []
        for field, value in filters.items():
            if value is None:
                continue
                
            # 处理特殊操作符
            if field == "__or__":
                if isinstance(value, dict):
                    or_conditions = []
                    for or_field, or_value in value.items():
                        if or_value is not None:
                            # 处理字段名中的操作符
                            if "__ilike" in or_field:
                                actual_field = or_field.replace("__ilike", "")
                                if validate_field_exists(self.model, actual_field):
                                    column = getattr(self.model, actual_field)
                                    or_conditions.append(column.ilike(or_value))
                            elif "__like" in or_field:
                                actual_field = or_field.replace("__like", "")
                                if validate_field_exists(self.model, actual_field):
                                    column = getattr(self.model, actual_field)
                                    or_conditions.append(column.like(or_value))
                            else:
                                if validate_field_exists(self.model, or_field):
                                    column = getattr(self.model, or_field)
                                    or_conditions.append(column == or_value)
                    
                    if or_conditions:
                        conditions.append(or_(*or_conditions))
            else:
                # 处理普通字段
                if validate_field_exists(self.model, field):
                    column = getattr(self.model, field)
                    conditions.append(column == value)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        return query
    
    def _apply_ordering(self, query: Select, order_by: Optional[str] = None, direction: str = "asc") -> Select:
        """应用排序
        
        Args:
            query: 查询对象
            order_by: 排序字段
            direction: 排序方向
            
        Returns:
            Select: 应用排序后的查询
        """
        if not order_by or not validate_field_exists(self.model, order_by):
            return query
        
        column = getattr(self.model, order_by)
        if direction.lower() == "desc":
            query = query.order_by(desc(column))
        else:
            query = query.order_by(asc(column))
        
        return query
    
    async def get_by_id(
        self,
        id: Any,
        include_deleted: bool = False,
        load_options: Optional[List[Any]] = None
    ) -> Optional[ModelType]:
        """通过ID获取实体
        
        Args:
            id: 实体ID
            include_deleted: 是否包含已删除记录
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[ModelType]: 实体对象或None
        """
        if not validate_id_field(id):
            return None
        
        query = self._build_base_query(include_deleted, load_options)
        query = query.filter_by(id=id)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_one(self, load_options: Optional[List[Any]] = None, **filters) -> Optional[ModelType]:
        """根据条件获取单个实体
        
        Args:
            load_options: SQLAlchemy加载选项
            **filters: 过滤条件
            
        Returns:
            Optional[ModelType]: 实体对象或None
        """
        if not filters:
            return None
            
        query = self._build_base_query(load_options=load_options)
        query = self._apply_filters(query, filters)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_by_field(self, field: str, value: Any, include_deleted: bool = False, load_options: Optional[List[Any]] = None) -> Optional[ModelType]:
        """通过字段值获取单个记录
        
        Args:
            field: 字段名
            value: 字段值
            include_deleted: 是否包含已删除记录
            load_options: SQLAlchemy加载选项
            
        Returns:
            Optional[ModelType]: 记录对象或None
        """
        if not validate_field_exists(self.model, field):
            return None
        
        query = self._build_base_query(include_deleted, load_options)
        query = query.filter_by(**{field: value})
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_by_fields(self, include_deleted: bool = False, load_options: Optional[List[Any]] = None, **fields) -> Optional[ModelType]:
        """通过多个字段值获取单个记录
        
        Args:
            include_deleted: 是否包含已删除记录
            load_options: SQLAlchemy加载选项
            **fields: 字段条件
            
        Returns:
            Optional[ModelType]: 记录对象或None
        """
        if not fields:
            return None
        
        query = self._build_base_query(include_deleted, load_options)
        query = self._apply_filters(query, fields)
        
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def search(self, term: str, fields: List[str], limit: int = 100, load_options: Optional[List[Any]] = None) -> List[ModelType]:
        """搜索记录
        
        Args:
            term: 搜索词
            fields: 搜索字段列表
            limit: 限制数量
            load_options: SQLAlchemy加载选项
            
        Returns:
            List[ModelType]: 搜索结果列表
        """
        if not term or not fields:
            return []
        
        query = self._build_base_query(load_options=load_options)
        
        # 构建搜索条件
        search_conditions = []
        for field in fields:
            if validate_field_exists(self.model, field):
                column = getattr(self.model, field)
                search_conditions.append(column.ilike(f"%{term}%"))
        
        if search_conditions:
            query = query.where(or_(*search_conditions))
        
        query = query.limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_by_condition(self, condition: callable, load_options: Optional[List[Any]] = None, **kwargs) -> List[ModelType]:
        """通过自定义条件获取记录
        
        Args:
            condition: 条件函数
            load_options: SQLAlchemy加载选项
            **kwargs: 其他参数
            
        Returns:
            List[ModelType]: 记录列表
        """
        query = self._build_base_query(load_options=load_options)
        query = condition(query, **kwargs)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_paginated(
        self,
        page_num: int = 1,
        page_size: int = 10,
        order_by: Optional[str] = None,
        direction: str = "asc",
        order_direction: Optional[str] = None,
        include_deleted: bool = False,
        filters: Optional[Dict[str, Any]] = None,
        load_options: Optional[List[Any]] = None,
        **kwargs
    ) -> tuple[List[ModelType], int]:
        """分页查询
        
        Args:
            page_num: 页码
            page_size: 每页大小
            order_by: 排序字段
            direction: 排序方向（新参数）
            order_direction: 排序方向（兼容旧参数）
            include_deleted: 是否包含已删除记录
            filters: 过滤条件字典
            load_options: SQLAlchemy加载选项
            **kwargs: 其他过滤条件
            
        Returns:
            tuple: (记录列表, 总数)
        """
        # 兼容性处理：支持新旧参数
        actual_page = page_num
        actual_size = page_size 
        actual_direction = order_direction if order_direction is not None else direction
        
        # 合并过滤条件
        all_filters = {}
        if filters:
            all_filters.update(filters)
        if kwargs:
            all_filters.update(kwargs)
        
        # 安全的分页参数
        safe_size, safe_offset = safe_limit_offset(actual_size, (actual_page - 1) * actual_size)
        
        # 构建查询
        query = self._build_base_query(include_deleted, load_options)
        query = self._apply_filters(query, all_filters)
        query = self._apply_ordering(query, order_by, actual_direction)
        query = query.offset(safe_offset).limit(safe_size)
        
        # 执行查询
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count()).select_from(self.model)
        if not include_deleted and validate_field_exists(self.model, 'deleted_at'):
            count_query = count_query.where(self.model.deleted_at.is_(None))
        count_query = self._apply_filters(count_query, all_filters)
        
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return items, total
    
    async def get_list(
        self,
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        direction: str = "asc",
        order_direction: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        load_options: Optional[List[Any]] = None,
        **kwargs
    ) -> List[ModelType]:
        """获取实体列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            order_by: 排序字段
            direction: 排序方向（新参数）
            order_direction: 排序方向（兼容旧参数）
            filters: 过滤条件字典（兼容旧参数）
            load_options: SQLAlchemy加载选项
            **kwargs: 其他过滤条件
            
        Returns:
            List[ModelType]: 实体列表
        """
        safe_limit, safe_offset = safe_limit_offset(limit, skip)
        
        # 兼容性处理：支持新旧参数
        actual_direction = order_direction if order_direction is not None else direction
        
        # 合并过滤条件
        all_filters = {}
        if filters:
            all_filters.update(filters)
        if kwargs:
            all_filters.update(kwargs)

        query = self._build_base_query(load_options=load_options)
        query = self._apply_filters(query, all_filters)
        query = self._apply_ordering(query, order_by, actual_direction)
        query = query.offset(safe_offset).limit(safe_limit)

        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def exists(self, filters: Optional[Dict[str, Any]] = None, include_deleted: bool = False, **kwargs) -> bool:
        """检查是否存在符合条件的记录
        
        Args:
            filters: 过滤条件字典（兼容旧参数）
            include_deleted: 是否包含已删除记录
            **kwargs: 其他过滤条件
            
        Returns:
            bool: 是否存在
        """
        # 合并过滤条件
        all_filters = {}
        if filters:
            all_filters.update(filters)
        if kwargs:
            all_filters.update(kwargs)
        
        query = self._build_base_query(include_deleted=include_deleted)
        query = self._apply_filters(query, all_filters)
        
        exists_query = select(query.exists())
        result = await self.db.execute(exists_query)
        return result.scalar() or False
    
    async def count(self, filters: Optional[Dict[str, Any]] = None, **kwargs) -> int:
        """计算符合条件的记录数量
        
        Args:
            filters: 过滤条件字典（兼容旧参数）
            **kwargs: 其他过滤条件
            
        Returns:
            int: 记录数量
        """
        # 合并过滤条件
        all_filters = {}
        if filters:
            all_filters.update(filters)
        if kwargs:
            all_filters.update(kwargs)
        
        query = select(func.count()).select_from(self.model)
        
        # 应用软删除过滤
        if validate_field_exists(self.model, 'deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))
        
        query = self._apply_filters(query, all_filters)
        
        result = await self.db.execute(query)
        count = result.scalar()
        return count if count is not None else 0

    async def load_dynamic_relations(self, obj: ModelType, relation_names: List[str]) -> ModelType:
        """加载lazy="dynamic"关系的数据
        
        Args:
            obj: 实体对象
            relation_names: 要加载的关系名称列表
            
        Returns:
            ModelType: 加载了关系数据的实体对象
        """
        for relation_name in relation_names:
            if hasattr(obj, relation_name):
                relation_attr = getattr(obj, relation_name)
                if hasattr(relation_attr, 'all'):
                    # 对于dynamic关系，调用all()方法加载数据
                    try:
                        loaded_data = await relation_attr.all()
                        # 将加载的数据设置到对象上
                        setattr(obj, f"_{relation_name}_loaded", loaded_data)
                        logger.debug(f"Loaded dynamic relation {relation_name} for {obj.__class__.__name__}")
                    except Exception as e:
                        logger.error(f"Failed to load dynamic relation {relation_name}: {str(e)}")
        
        return obj
    
    async def get_with_dynamic_relations(
        self, 
        id: Any, 
        relation_names: List[str], 
        include_deleted: bool = False
    ) -> Optional[ModelType]:
        """获取实体并加载指定的dynamic关系
        
        Args:
            id: 实体ID
            relation_names: 要加载的dynamic关系名称列表
            include_deleted: 是否包含已删除记录
            
        Returns:
            Optional[ModelType]: 实体对象或None
        """
        obj = await self.get_by_id(id, include_deleted)
        if obj:
            obj = await self.load_dynamic_relations(obj, relation_names)
        return obj
