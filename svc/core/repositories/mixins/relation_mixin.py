"""关系管理混入类。

提供 SQLAlchemy 多对多关系的管理功能，包括检查、添加、移除、清除关系等操作。
"""

from typing import Any, List, Optional, Type, TypeVar
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import RelationshipProperty

ModelType = TypeVar("ModelType")


class RelationMixin:
    """关系管理混入类"""
    
    def _get_secondary_table(self, relation_attr) -> Any:
        """获取中间表对象，支持字符串引用"""
        secondary = relation_attr.property.secondary
        
        # 如果secondary是字符串，从metadata中获取表对象
        if isinstance(secondary, str):
            # 从关系的mapper中获取metadata
            mapper = relation_attr.property.mapper
            return mapper.class_.metadata.tables[secondary]
        
        # 如果secondary是表对象，直接返回
        return secondary
    
    async def has_relation(self, obj: ModelType, relation_name: str, target: Any) -> bool:
        """检查对象是否与目标存在关系
        
        Args:
            obj: 源对象
            relation_name: 关系名称
            target: 目标对象
            
        Returns:
            bool: 关系是否存在
        """
        try:
            # 检查关系是否存在
            if not hasattr(obj.__class__, relation_name):
                return False
            
            # 获取关系集合
            relation_collection = getattr(obj, relation_name)
            
            # 检查目标是否在关系中
            if hasattr(relation_collection, '__contains__'):
                return target in relation_collection
            
            # 如果关系是动态的，需要查询数据库
            if hasattr(relation_collection, 'filter'):
                return await relation_collection.filter_by(id=target.id).first() is not None
            
            return False
            
        except Exception:
            return False
    
    async def add_relation(self, obj: ModelType, relation_name: str, target: Any) -> None:
        """为对象添加关系
        
        Args:
            obj: 源对象
            relation_name: 关系名称
            target: 目标对象
        """
        try:
            # 检查关系是否存在
            if not hasattr(obj.__class__, relation_name):
                raise ValueError(f"Relation '{relation_name}' not found on {obj.__class__.__name__}")
            
            # 获取关系属性
            relation_attr = getattr(obj.__class__, relation_name)
            
            # 检查是否有中间表（通过检查关系属性是否有 secondary 参数）
            has_secondary = hasattr(relation_attr.property, 'secondary')
            
            # 对于多对多关系，强制使用手动操作中间表的方式
            if has_secondary:
                await self._add_relation_manually(obj, relation_name, target)
            else:
                # 对于没有中间表的关系，使用 SQLAlchemy 的自动管理
                relation_collection = getattr(obj, relation_name)
                if hasattr(relation_collection, 'append'):
                    relation_collection.append(target)
                elif hasattr(relation_collection, 'add'):
                    relation_collection.add(target)
                else:
                    raise ValueError(f"Cannot add relation '{relation_name}': unsupported collection type")
            
            await self.db.flush()
            
        except Exception as e:
            raise ValueError(f"Failed to add relation '{relation_name}': {str(e)}")
    
    async def remove_relation(self, obj: ModelType, relation_name: str, target: Any) -> None:
        """移除对象的关系
        
        Args:
            obj: 源对象
            relation_name: 关系名称
            target: 目标对象
        """
        try:
            # 检查关系是否存在
            if not hasattr(obj.__class__, relation_name):
                raise ValueError(f"Relation '{relation_name}' not found on {obj.__class__.__name__}")
            
            # 获取关系属性
            relation_attr = getattr(obj.__class__, relation_name)
            
            # 检查是否有中间表
            has_secondary = hasattr(relation_attr.property, 'secondary')
            
            # 对于多对多关系，强制使用手动操作中间表的方式
            if has_secondary:
                await self._remove_relation_manually(obj, relation_name, target)
            else:
                # 对于没有中间表的关系，使用 SQLAlchemy 的自动管理
                relation_collection = getattr(obj, relation_name)
                if hasattr(relation_collection, 'remove'):
                    relation_collection.remove(target)
                elif hasattr(relation_collection, 'discard'):
                    relation_collection.discard(target)
                else:
                    raise ValueError(f"Cannot remove relation '{relation_name}': unsupported collection type")
            
            await self.db.flush()
            
        except Exception as e:
            raise ValueError(f"Failed to remove relation '{relation_name}': {str(e)}")
    
    async def clear_relations(self, obj: ModelType, relation_name: str) -> None:
        """清除对象的所有关系
        
        Args:
            obj: 源对象
            relation_name: 关系名称
        """
        try:
            # 检查关系是否存在
            if not hasattr(obj.__class__, relation_name):
                raise ValueError(f"Relation '{relation_name}' not found on {obj.__class__.__name__}")
            
            # 获取关系属性
            relation_attr = getattr(obj.__class__, relation_name)
            
            # 检查是否有中间表
            has_secondary = hasattr(relation_attr.property, 'secondary')
            
            # 对于多对多关系，强制使用手动操作中间表的方式
            if has_secondary:
                await self._clear_relations_manually(obj, relation_name)
            else:
                # 对于没有中间表的关系，使用 SQLAlchemy 的自动管理
                relation_collection = getattr(obj, relation_name)
                if hasattr(relation_collection, 'clear'):
                    relation_collection.clear()
                else:
                    raise ValueError(f"Cannot clear relations '{relation_name}': unsupported collection type")
            
            await self.db.flush()
            
        except Exception as e:
            raise ValueError(f"Failed to clear relations '{relation_name}': {str(e)}")
    
    async def _add_relation_manually(self, obj: ModelType, relation_name: str, target: Any) -> None:
        """手动添加关系（用于有中间表的关系）"""
        relation_attr = getattr(obj.__class__, relation_name)
        secondary = self._get_secondary_table(relation_attr)
        
        if secondary is not None:
            # 构建插入语句
            # 尝试不同的列名模式
            possible_columns = [
                f"{obj.__class__.__tablename__}_id",
                f"{obj.__class__.__tablename__.rstrip('s')}_id",
                f"{obj.__class__.__name__.lower()}_id",
                f"{obj.__class__.__name__.lower().rstrip('s')}_id",
                # 添加更多可能的模式
                "role_id",  # 直接匹配
                "user_id",  # 直接匹配
                "category_id",  # 直接匹配
            ]
            
            possible_target_columns = [
                f"{target.__class__.__tablename__}_id",
                f"{target.__class__.__tablename__.rstrip('s')}_id",
                f"{target.__class__.__name__.lower()}_id",
                f"{target.__class__.__name__.lower().rstrip('s')}_id",
                # 添加更多可能的模式
                "permission_id",  # 直接匹配
                "spec_id",  # 直接匹配
                "option_id",  # 直接匹配
            ]
            
            # 找到实际存在的列
            source_column = None
            target_column = None
            
            for col in possible_columns:
                if hasattr(secondary.c, col):
                    source_column = col
                    break
            
            for col in possible_target_columns:
                if hasattr(secondary.c, col):
                    target_column = col
                    break
            
            if source_column and target_column:
                # 检查关系是否已存在
                check_stmt = secondary.select().where(
                    secondary.c[source_column] == obj.id,
                    secondary.c[target_column] == target.id
                )
                result = await self.db.execute(check_stmt)
                if result.fetchone():
                    # 关系已存在，不需要重复添加
                    return
                
                # 插入新关系
                insert_values = {
                    source_column: obj.id,
                    target_column: target.id
                }
                
                try:
                    stmt = secondary.insert().values(insert_values)
                    await self.db.execute(stmt)
                except Exception as e:
                    raise
            else:
                raise ValueError(f"Cannot determine column names for relation '{relation_name}'. Source: {source_column}, Target: {target_column}")
        else:
            raise ValueError(f"Secondary table is None for relation '{relation_name}'")
    
    async def _remove_relation_manually(self, obj: ModelType, relation_name: str, target: Any) -> None:
        """手动移除关系（用于有中间表的关系）"""
        relation_attr = getattr(obj.__class__, relation_name)
        secondary = self._get_secondary_table(relation_attr)
        
        if secondary:
            # 构建删除语句
            # 尝试不同的列名模式
            possible_columns = [
                f"{obj.__class__.__tablename__}_id",
                f"{obj.__class__.__tablename__.rstrip('s')}_id",
                f"{obj.__class__.__name__.lower()}_id",
                f"{obj.__class__.__name__.lower().rstrip('s')}_id",
                # 添加更多可能的模式
                "role_id",  # 直接匹配
                "user_id",  # 直接匹配
                "category_id",  # 直接匹配
            ]
            
            possible_target_columns = [
                f"{target.__class__.__tablename__}_id",
                f"{target.__class__.__tablename__.rstrip('s')}_id",
                f"{target.__class__.__name__.lower()}_id",
                f"{target.__class__.__name__.lower().rstrip('s')}_id",
                # 添加更多可能的模式
                "permission_id",  # 直接匹配
                "spec_id",  # 直接匹配
                "option_id",  # 直接匹配
            ]
            
            # 找到实际存在的列
            source_column = None
            target_column = None
            
            for col in possible_columns:
                if hasattr(secondary.c, col):
                    source_column = col
                    break
            
            for col in possible_target_columns:
                if hasattr(secondary.c, col):
                    target_column = col
                    break
            
            if source_column and target_column:
                stmt = delete(secondary).where(
                    secondary.c[source_column] == obj.id,
                    secondary.c[target_column] == target.id
                )
                await self.db.execute(stmt)
            else:
                raise ValueError(f"Cannot determine column names for relation '{relation_name}'")
    
    async def _clear_relations_manually(self, obj: ModelType, relation_name: str) -> None:
        """手动清除关系（用于有中间表的关系）"""
        relation_attr = getattr(obj.__class__, relation_name)
        secondary = self._get_secondary_table(relation_attr)
        
        if secondary is not None:
            # 构建删除语句
            # 尝试不同的列名模式
            possible_columns = [
                f"{obj.__class__.__tablename__}_id",
                f"{obj.__class__.__tablename__.rstrip('s')}_id",
                f"{obj.__class__.__name__.lower()}_id",
                f"{obj.__class__.__name__.lower().rstrip('s')}_id",
                # 添加更多可能的模式
                "role_id",  # 直接匹配
                "user_id",  # 直接匹配
                "category_id",  # 直接匹配
            ]
            
            # 找到实际存在的列
            source_column = None
            
            for col in possible_columns:
                if hasattr(secondary.c, col):
                    source_column = col
                    break
            
            if source_column:
                stmt = delete(secondary).where(
                    secondary.c[source_column] == obj.id
                )
                await self.db.execute(stmt)
            else:
                raise ValueError(f"Cannot determine column names for relation '{relation_name}'")
        else:
            raise ValueError(f"Secondary table is None for relation '{relation_name}'")
