"""软删除混入类。

提供统一的软删除处理功能，包括软删除、恢复、查询已删除记录等。
"""

from typing import Any, Dict, List, Optional, Type, TypeVar
from sqlalchemy import Select, select, update, and_
from sqlalchemy.ext.asyncio import AsyncSession

from ..utils import validate_field_exists, add_timestamps
from ..exceptions import ValidationError

ModelType = TypeVar("ModelType")


class SoftDeleteMixin:
    """软删除混入类"""
    
    def _apply_soft_delete_filter(self, query: Select) -> Select:
        """应用软删除过滤条件
        
        Args:
            query: 查询对象
            
        Returns:
            Select: 应用软删除过滤后的查询
        """
        if validate_field_exists(self.model, 'deleted_at'):
            query = query.where(self.model.deleted_at.is_(None))
        return query
    
    async def soft_delete(self, obj: ModelType) -> ModelType:
        """软删除记录
        
        Args:
            obj: 要删除的记录对象
            
        Returns:
            ModelType: 删除后的记录对象
        """
        if not validate_field_exists(self.model, 'deleted_at'):
            raise ValidationError("deleted_at", None, "Model does not support soft delete")
        
        # 设置删除时间
        update_data = {'deleted_at': self._get_datetime_utils()()}
        
        # 如果有is_active字段，设置为False
        if validate_field_exists(self.model, 'is_active'):
            update_data['is_active'] = False
        
        # 如果有status字段，设置为deleted
        if validate_field_exists(self.model, 'status'):
            update_data['status'] = 'deleted'
        
        # 更新时间戳
        update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        # 更新对象
        for field, value in update_data.items():
            if hasattr(obj, field):
                setattr(obj, field, value)
        
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def restore(self, obj: ModelType) -> ModelType:
        """恢复软删除的记录
        
        Args:
            obj: 要恢复的记录对象
            
        Returns:
            ModelType: 恢复后的记录对象
        """
        if not validate_field_exists(self.model, 'deleted_at'):
            raise ValidationError("deleted_at", None, "Model does not support soft delete")
        
        # 清除删除时间
        update_data = {'deleted_at': None}
        
        # 如果有is_active字段，设置为True
        if validate_field_exists(self.model, 'is_active'):
            update_data['is_active'] = True
        
        # 如果有status字段，设置为active
        if validate_field_exists(self.model, 'status'):
            update_data['status'] = 'active'
        
        # 更新时间戳
        update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        # 更新对象
        for field, value in update_data.items():
            if hasattr(obj, field):
                setattr(obj, field, value)
        
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    
    async def get_deleted(self, **filters) -> List[ModelType]:
        """获取已删除的记录
        
        Args:
            **filters: 过滤条件
            
        Returns:
            List[ModelType]: 已删除的记录列表
        """
        if not validate_field_exists(self.model, 'deleted_at'):
            return []
        
        query = select(self.model).where(self.model.deleted_at.is_not(None))
        
        # 应用过滤条件
        for field, value in filters.items():
            if validate_field_exists(self.model, field) and value is not None:
                column = getattr(self.model, field)
                query = query.where(column == value)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def permanent_delete(self, obj: ModelType) -> None:
        """永久删除记录
        
        Args:
            obj: 要删除的记录对象
        """
        await self.db.delete(obj)
        await self.db.flush()
    
    async def soft_delete_by_id(self, id: Any) -> Optional[ModelType]:
        """通过ID软删除记录
        
        Args:
            id: 记录ID
            
        Returns:
            Optional[ModelType]: 删除后的记录对象或None
        """
        # 使用get_by_id方法，包含已删除记录
        obj = await self.get_by_id(id, include_deleted=True)
        if not obj:
            return None
        
        return await self.soft_delete(obj)
    
    async def restore_by_id(self, id: Any) -> Optional[ModelType]:
        """通过ID恢复记录
        
        Args:
            id: 记录ID
            
        Returns:
            Optional[ModelType]: 恢复后的记录对象或None
        """
        # 使用get_by_id方法，包含已删除记录
        obj = await self.get_by_id(id, include_deleted=True)
        if not obj:
            return None
        
        return await self.restore(obj)
    
    async def batch_soft_delete(self, ids: List[int]) -> int:
        """批量软删除记录
        
        Args:
            ids: 记录ID列表
            
        Returns:
            int: 删除的记录数量
        """
        if not validate_field_exists(self.model, 'deleted_at'):
            raise ValidationError("deleted_at", None, "Model does not support soft delete")
        
        if not ids:
            return 0
        
        # 构建更新数据
        update_data = {'deleted_at': self._get_datetime_utils()()}
        
        if validate_field_exists(self.model, 'is_active'):
            update_data['is_active'] = False
        
        if validate_field_exists(self.model, 'status'):
            update_data['status'] = 'deleted'
        
        update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        # 执行批量更新
        conditions = [
            self.model.id.in_(ids),
            self.model.deleted_at.is_(None)
        ]
        
        stmt = update(self.model).where(and_(*conditions)).values(**update_data)
        result = await self.db.execute(stmt)
        
        await self.db.flush()
        return result.rowcount
    
    async def batch_restore(self, ids: List[int]) -> int:
        """批量恢复记录
        
        Args:
            ids: 记录ID列表
            
        Returns:
            int: 恢复的记录数量
        """
        if not validate_field_exists(self.model, 'deleted_at'):
            raise ValidationError("deleted_at", None, "Model does not support soft delete")
        
        if not ids:
            return 0
        
        # 构建更新数据
        update_data = {'deleted_at': None}
        
        if validate_field_exists(self.model, 'is_active'):
            update_data['is_active'] = True
        
        if validate_field_exists(self.model, 'status'):
            update_data['status'] = 'active'
        
        update_data = add_timestamps(update_data, is_update=True, model=self.model)
        
        # 执行批量更新
        conditions = [
            self.model.id.in_(ids),
            self.model.deleted_at.is_not(None)
        ]
        
        stmt = update(self.model).where(and_(*conditions)).values(**update_data)
        result = await self.db.execute(stmt)
        
        await self.db.flush()
        return result.rowcount
    
    def _get_datetime_utils(self):
        """获取日期时间工具函数"""
        from ..utils import get_datetime_utils
        return get_datetime_utils()
