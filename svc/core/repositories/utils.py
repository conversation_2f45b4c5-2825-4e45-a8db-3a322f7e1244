"""仓库层工具函数。

提供仓库操作中常用的工具函数，包括数据验证、预处理、缓存等。
"""

from typing import Any, Dict, Type, Union, Optional
from datetime import datetime


def validate_field_exists(model: Type, field: str) -> bool:
    """验证字段是否存在于模型中
    
    Args:
        model: 模型类
        field: 字段名
        
    Returns:
        bool: 字段是否存在
    """
    if not field or not isinstance(field, str):
        return False
    return hasattr(model, field)


def prepare_data(data: Any, model: Type, exclude_none: bool = True) -> Dict[str, Any]:
    """预处理数据，过滤无效字段
    
    Args:
        data: 原始数据
        model: 目标模型类
        exclude_none: 是否排除None值
        
    Returns:
        Dict[str, Any]: 处理后的数据
    """
    if hasattr(data, 'model_dump'):
        model_data = data.model_dump(exclude_none=exclude_none)
    elif isinstance(data, dict):
        model_data = data.copy()
        if exclude_none:
            model_data = {k: v for k, v in model_data.items() if v is not None}
    else:
        model_data = data
    
    # 过滤不存在的字段
    return {k: v for k, v in model_data.items() if validate_field_exists(model, k)}


def add_timestamps(data: Dict[str, Any], is_update: bool = False, model: Optional[Type] = None) -> Dict[str, Any]:
    """添加时间戳字段
    
    Args:
        data: 数据字典
        is_update: 是否为更新操作
        model: 目标模型类，用于检查字段是否存在
        
    Returns:
        Dict[str, Any]: 添加时间戳后的数据
    """
    now = datetime.utcnow()
    
    if not is_update and 'created_at' not in data:
        if model is None or validate_field_exists(model, 'created_at'):
            data['created_at'] = now
    
    if 'updated_at' not in data:
        if model is None or validate_field_exists(model, 'updated_at'):
            data['updated_at'] = now
    
    return data


def build_cache_key(prefix: str, **params) -> str:
    """构建缓存键
    
    Args:
        prefix: 缓存键前缀
        **params: 参数键值对
        
    Returns:
        str: 缓存键
    """
    if not params:
        return prefix
    
    param_str = ":".join([f"{k}={v}" for k, v in sorted(params.items())])
    return f"{prefix}:{param_str}"


def safe_batch_size(size: int, max_size: int = 5000) -> int:
    """安全的批量大小
    
    Args:
        size: 请求的批量大小
        max_size: 最大允许的批量大小
        
    Returns:
        int: 安全的批量大小
    """
    return min(max(1, size), max_size)


def get_datetime_utils():
    """获取日期时间工具函数
    
    Returns:
        Callable: 获取当前时间的函数
    """
    from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
    return get_utc_now_without_tzinfo


def build_filter_conditions(filters: Dict[str, Any]) -> Dict[str, Any]:
    """构建过滤条件
    
    Args:
        filters: 原始过滤条件
        
    Returns:
        Dict[str, Any]: 处理后的过滤条件
    """
    if not filters:
        return {}
    
    # 过滤None值
    return {k: v for k, v in filters.items() if v is not None}


def safe_limit_offset(limit: int, offset: int, max_limit: int = 10000) -> tuple:
    """安全的限制和偏移量
    
    Args:
        limit: 限制数量
        offset: 偏移量
        max_limit: 最大限制数量
        
    Returns:
        tuple: (safe_limit, safe_offset)
    """
    safe_limit = min(max(1, limit), max_limit)
    safe_offset = max(0, offset)
    return safe_limit, safe_offset
