"""
通用批量操作Schema定义。
提供批量更新、批量删除等操作的通用数据模型。
"""
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

from svc.core.models.base import CamelCaseModel

# 定义泛型类型变量
UpdateDataType = TypeVar("UpdateDataType", bound=BaseModel)


class BatchUpdateRequest(CamelCaseModel, Generic[UpdateDataType]):
    """通用批量更新请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "update_data": {
                    "status": "active",
                    "is_featured": True
                }
            }
        }
    )
    
    resource_ids: List[int] = Field(..., min_length=1, max_length=100, description="资源ID列表，最多100个")
    update_data: UpdateDataType = Field(..., description="更新数据")


class BatchUpdateResponse(CamelCaseModel):
    """通用批量更新响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "updated_count": 5,
                "failed_ids": [],
                "total_requested": 5,
                "success_rate": 1.0
            }
        }
    )
    
    updated_count: int = Field(..., description="成功更新的资源数量")
    failed_ids: List[int] = Field(default=[], description="更新失败的资源ID列表")
    total_requested: int = Field(..., description="请求更新的资源总数")
    success_rate: float = Field(..., description="成功率（0.0-1.0）")
    
    def __init__(self, **data):
        # 自动计算成功率
        if 'success_rate' not in data:
            total_requested = data.get('total_requested', 0)
            updated_count = data.get('updated_count', 0)
            if total_requested > 0:
                data['success_rate'] = updated_count / total_requested
            else:
                data['success_rate'] = 0.0
        super().__init__(**data)


class BatchDeleteRequest(CamelCaseModel):
    """通用批量删除请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "resource_ids": [1, 2, 3, 4, 5],
                "soft_delete": True
            }
        }
    )
    
    resource_ids: List[int] = Field(..., min_length=1, max_length=100, description="资源ID列表，最多100个")
    soft_delete: bool = Field(default=True, description="是否软删除")


class BatchDeleteResponse(CamelCaseModel):
    """通用批量删除响应模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "deleted_count": 5,
                "failed_ids": [],
                "total_requested": 5,
                "success_rate": 1.0
            }
        }
    )
    
    deleted_count: int = Field(..., description="成功删除的资源数量")
    failed_ids: List[int] = Field(default=[], description="删除失败的资源ID列表")
    total_requested: int = Field(..., description="请求删除的资源总数")
    success_rate: float = Field(..., description="成功率（0.0-1.0）")
    
    def __init__(self, **data):
        # 自动计算成功率
        if 'success_rate' not in data:
            total_requested = data.get('total_requested', 0)
            deleted_count = data.get('deleted_count', 0)
            if total_requested > 0:
                data['success_rate'] = deleted_count / total_requested
            else:
                data['success_rate'] = 0.0
        super().__init__(**data)


class BatchOperationResult(CamelCaseModel):
    """通用批量操作结果模型"""
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "operation_type": "update",
                "total_requested": 10,
                "success_count": 8,
                "failed_count": 2,
                "failed_ids": [3, 7],
                "success_rate": 0.8,
                "details": {}
            }
        }
    )
    
    operation_type: str = Field(..., description="操作类型（update/delete/create等）")
    total_requested: int = Field(..., description="请求处理的资源总数")
    success_count: int = Field(..., description="成功处理的资源数量")
    failed_count: int = Field(..., description="失败的资源数量")
    failed_ids: List[int] = Field(default=[], description="失败的资源ID列表")
    success_rate: float = Field(..., description="成功率（0.0-1.0）")
    details: Dict[str, Any] = Field(default={}, description="操作详细信息")
    
    def __init__(self, **data):
        # 自动计算成功率和失败数量
        if 'success_rate' not in data or 'failed_count' not in data:
            total_requested = data.get('total_requested', 0)
            success_count = data.get('success_count', 0)
            if total_requested > 0:
                if 'success_rate' not in data:
                    data['success_rate'] = success_count / total_requested
                if 'failed_count' not in data:
                    data['failed_count'] = total_requested - success_count
            else:
                if 'success_rate' not in data:
                    data['success_rate'] = 0.0
                if 'failed_count' not in data:
                    data['failed_count'] = 0
        super().__init__(**data)
