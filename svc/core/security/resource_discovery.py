"""
启动期资源自动发现与注册。

- 扫描指定的 models 包，发现继承 ResourceMixin 且声明了 __resource_type__ 的 ORM 模型
  并注册到 PermissionService。
"""

from __future__ import annotations

import importlib
import logging
from types import ModuleType
from typing import Dict, Iterable, List, Optional, Tuple, Type

from sqlalchemy.orm import Mapper

from svc.core.models.base import Base
from svc.core.models.resource_mixin import ResourceMixin
from svc.core.security.permissions import PermissionService


logger = logging.getLogger(__name__)


def _import_models_modules(module_paths: Iterable[str]) -> List[ModuleType]:
    imported = []
    for path in module_paths:
        try:
            mod = importlib.import_module(path)
            imported.append(mod)
        except Exception as e:
            logger.warning(f"导入模型模块失败: {path} -> {e}")
    return imported


def _iter_mapped_model_classes() -> Iterable[Type]:
    # 遍历 SQLAlchemy 已映射的类
    for mapper in list(Base.registry.mappers):  # type: ignore[attr-defined]
        if isinstance(mapper, Mapper):
            cls = mapper.class_
            if isinstance(cls, type):
                yield cls


def discover_and_register_resources(
    permission_service: PermissionService,
    module_packages: Iterable[str],
    strict: bool = True
) -> Dict[str, Type]:
    """发现并注册资源模型。

    Args:
        permission_service: 权限服务实例
        module_packages: 需要导入的 models 包路径列表，如 ["svc.apps.auth.models", ...]
        strict: 是否启用严格冲突检测（同名资源映射不同模型时抛出异常）

    Returns:
        已注册的资源映射表 {resource_type: model_cls}
    """

    # 先导入模型模块，确保 ORM 映射可见
    module_packages = list(module_packages)
    logger.info(f"开始资源自动发现，目标包数量: {len(module_packages)}")
    _import_models_modules(module_packages)

    discovered: Dict[str, Type] = {}

    for cls in _iter_mapped_model_classes():
        # 仅处理继承 ResourceMixin 的模型
        if not issubclass(cls, ResourceMixin):
            continue

        rtype = cls.get_resource_type()
        if not rtype:
            continue

        # 主类型
        if rtype in discovered and discovered[rtype] is not cls:
            msg = f"资源类型冲突: {rtype} -> {discovered[rtype].__module__}.{discovered[rtype].__name__} 与 {cls.__module__}.{cls.__name__}"
            if strict:
                raise RuntimeError(msg)
            logger.warning(msg)
        discovered[rtype] = cls

        # 别名
        for alias in cls.get_resource_aliases():
            if alias in discovered and discovered[alias] is not cls:
                msg = f"资源别名冲突: {alias} -> {discovered[alias].__module__}.{discovered[alias].__name__} 与 {cls.__module__}.{cls.__name__}"
                if strict:
                    raise RuntimeError(msg)
                logger.warning(msg)
            discovered[alias] = cls

    # 批量注册
    for rtype, model_cls in discovered.items():
        permission_service.register_resource(rtype, model_cls)

    logger.info(f"资源自动发现与注册完成，共注册 {len(discovered)} 项")
    return discovered


def setup_permission_system(
    module_packages: Iterable[str],
    strict: bool = True,
    auto_configure_auth: bool = True
) -> PermissionService:
    """初始化完整的权限系统。
    
    包括资源发现、注册和权限服务配置。
    
    Args:
        module_packages: 需要导入的 models 包路径列表
        strict: 是否启用严格冲突检测
        auto_configure_auth: 是否自动配置认证依赖
        
    Returns:
        配置完成的权限服务实例
    """
    from svc.core.security.permissions import permission_service
    
    logger.info("开始初始化权限系统...")
    
    # 1. 发现并注册资源
    discovered_resources = discover_and_register_resources(
        permission_service, 
        module_packages, 
        strict=strict
    )
    
    # 2. 冻结权限服务注册表
    permission_service.freeze()
    logger.info("权限资源已发现并注册，注册表已冻结")
    
    # 3. 自动配置认证依赖
    if auto_configure_auth:
        try:
            from svc.core.dependencies.auth import configure_auth_dependencies
            configure_auth_dependencies(permission_service=permission_service)
            logger.info("权限服务已配置到认证依赖中")
        except Exception as e:
            logger.warning(f"自动配置认证依赖失败: {e}")
    
    logger.info(f"权限系统初始化完成，共注册 {len(discovered_resources)} 个资源类型")
    return permission_service


