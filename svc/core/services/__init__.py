"""
服务层模块初始化文件。

导出重构后的服务层核心组件，包括基类、配置类、混入类和异常类。
"""

from .base import BaseService
from .config import ServiceConfig
from .exceptions import (BatchOperationError, BusinessLogicError, CacheError,
                         ConfigurationError, ExternalServiceError,
                         PermissionDeniedError, ResourceAlreadyExistsError,
                         ResourceNotFoundError, ServiceError, ValidationError)
from .mixins.batch_operation import BatchOperationMixin
from .mixins.cache import CacheMixin
from .mixins.crud import CRUDMixin
from .mixins.error_result import ErrorResultMixin
from .mixins.event import EventMixin
from .mixins.logger import LoggerMixin

__all__ = [
    # 核心类
    "BaseService",
    "ServiceConfig",

    # 异常类
    "ServiceError",
    "ResourceNotFoundError",
    "ResourceAlreadyExistsError",
    "ValidationError",
    "PermissionDeniedError",
    "BusinessLogicError",
    "ExternalServiceError",
    "CacheError",
    "ConfigurationError",
    "BatchOperationError",

    # 混入类
    "BatchOperationMixin",
    "CacheMixin",
    "CRUDMixin",
    "ErrorResultMixin",
    "EventMixin",
    "LoggerMixin",
]
