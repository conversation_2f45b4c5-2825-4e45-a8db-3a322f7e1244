"""
服务层基类模块。

完全重构的服务层基类，参照仓库层架构模式，提供统一的服务接口和功能。
自动组合常用mixins，支持配置化管理，提供标准化的CRUD操作。

使用示例:
```python
class UserService(BaseService[User, UserCreate, UserUpdate, UserResponse]):
    def __init__(self, user_repo: UserRepository, **kwargs):
        config = ServiceConfig(resource_type="user", enable_batch_operations=True)
        super().__init__(user_repo, config, **kwargs)
```
"""
from typing import Any, Dict, Generic, Optional, TypeVar, Union

from redis.asyncio import Redis

from .config import ServiceConfig
from .mixins.cache import CacheMixin
from .mixins.crud import CRUDMixin
from .mixins.error_result import ErrorResultMixin
from .mixins.event import EventMixin
from .mixins.logger import LoggerMixin

# 泛型类型定义
ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")
ResponseSchemaType = TypeVar("ResponseSchemaType")


class BaseService(
    Generic[ModelType, CreateSchemaType, UpdateSchemaType, ResponseSchemaType],
    LoggerMixin,
    ErrorResultMixin,
    CacheMixin,
    EventMixin,
    CRUDMixin
):
    """
    重构后的服务基类，自动组合所有常用功能。

    主要特性：
    - 自动组合常用mixins（日志、缓存、错误处理、事件、CRUD）
    - 配置化管理服务行为
    - 标准化的CRUD操作接口
    - 完整的泛型类型支持
    - 自动缓存和事件处理
    """

    def __init__(
        self,
        repository: Any,
        config: Optional[ServiceConfig] = None,
        redis: Optional[Redis] = None,
        **dependencies
    ):
        """初始化服务

        Args:
            repository: 数据仓库实例
            config: 服务配置对象
            redis: Redis客户端实例
            **dependencies: 其他依赖项
        """
        self.repository = repository
        self.config = config or ServiceConfig()
        self.dependencies = dependencies

        # 初始化所有mixins
        CacheMixin.__init__(self, redis)
        EventMixin.__init__(self)

        # 设置资源类型（向后兼容）
        self.resource_type = self.config.resource_type

    async def get_resource_by_id(self, resource_id: Union[str, int]) -> Optional[ModelType]:
        """获取指定ID的资源（向后兼容方法）

        Args:
            resource_id: 资源ID

        Returns:
            Optional[ModelType]: 资源对象，不存在时返回None
        """
        return await self.repository.get_by_id(resource_id)

    async def warm_up_cache(self, **filters) -> None:
        """预热缓存

        Args:
            **filters: 缓存预热的过滤条件
        """
        if not self.config.cache_enabled or not self.redis:
            return

        try:
            # 获取热门资源进行缓存预热
            resources, _ = await self.repository.get_paginated(
                filters=filters,
                page_size=50,  # 预热前50个资源
                order_by="id"
            )

            for resource in resources:
                cache_key = self._get_resource_cache_key(resource.id)
                response_data = self._serialize_resource(resource)
                await self.cache_resource(cache_key, response_data, self.config.get_cache_ttl("long"))

            self.logger.info(f"缓存预热完成: {len(resources)}个{self.config.resource_type}")

        except Exception as e:
            self.logger.error(f"缓存预热失败: {str(e)}", exc_info=True)            