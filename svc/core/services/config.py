"""
服务层配置模块。

提供统一的服务配置管理，包括缓存、事件、批量操作等配置选项。
所有服务类都应该使用ServiceConfig来管理其行为配置。
"""
from typing import Optional


class ServiceConfig:
    """服务配置类，统一管理服务层的各种配置选项"""
    
    def __init__(self, **kwargs):
        """初始化服务配置
        
        Args:
            resource_type: 资源类型名称，用于缓存键、事件名称等
            cache_enabled: 是否启用缓存功能
            cache_ttl: 缓存过期时间（秒）
            cache_ttl_short: 短期缓存过期时间（秒）
            cache_ttl_long: 长期缓存过期时间（秒）
            enable_events: 是否启用事件触发
            enable_batch_operations: 是否启用批量操作功能
            auto_cache_on_create: 创建资源时是否自动缓存
            auto_cache_on_update: 更新资源时是否自动缓存
            auto_cache_on_get: 获取资源时是否自动缓存
            event_prefix: 事件名称前缀，默认使用resource_type
            enable_soft_delete: 是否启用软删除
            default_page_size: 默认分页大小
            max_page_size: 最大分页大小
            enable_audit_log: 是否启用审计日志
        """
        # 基础配置
        self.resource_type: str = kwargs.get('resource_type', "资源")
        
        # 缓存配置
        self.cache_enabled: bool = kwargs.get('cache_enabled', True)
        self.cache_ttl: int = kwargs.get('cache_ttl', 3600)  # 1小时
        self.cache_ttl_short: int = kwargs.get('cache_ttl_short', 300)  # 5分钟
        self.cache_ttl_long: int = kwargs.get('cache_ttl_long', 86400)  # 24小时
        self.auto_cache_on_create: bool = kwargs.get('auto_cache_on_create', True)
        self.auto_cache_on_update: bool = kwargs.get('auto_cache_on_update', True)
        self.auto_cache_on_get: bool = kwargs.get('auto_cache_on_get', True)
        
        # 事件配置
        self.enable_events: bool = kwargs.get('enable_events', True)
        self.event_prefix: str = kwargs.get('event_prefix', self.resource_type)
        
        # 批量操作配置
        self.enable_batch_operations: bool = kwargs.get('enable_batch_operations', False)
        
        # 删除配置
        self.enable_soft_delete: bool = kwargs.get('enable_soft_delete', True)
        
        # 分页配置
        self.default_page_size: int = kwargs.get('default_page_size', 20)
        self.max_page_size: int = kwargs.get('max_page_size', 100)
        
        # 审计配置
        self.enable_audit_log: bool = kwargs.get('enable_audit_log', False)
    
    def get_cache_ttl(self, cache_type: str = "default") -> int:
        """获取指定类型的缓存过期时间
        
        Args:
            cache_type: 缓存类型 (default/short/long)
            
        Returns:
            int: 缓存过期时间（秒）
        """
        if cache_type == "short":
            return self.cache_ttl_short
        elif cache_type == "long":
            return self.cache_ttl_long
        else:
            return self.cache_ttl
    
    def get_event_name(self, action: str) -> str:
        """生成标准化的事件名称
        
        Args:
            action: 操作名称
            
        Returns:
            str: 完整的事件名称
        """
        return f"{self.event_prefix}:{action}"
    
    def __repr__(self) -> str:
        return f"ServiceConfig(resource_type='{self.resource_type}', cache_enabled={self.cache_enabled})"
