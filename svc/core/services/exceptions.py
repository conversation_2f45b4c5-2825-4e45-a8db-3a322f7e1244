"""
服务层异常类。

定义服务层特有的异常类型，提供更精确的错误处理和分类。
"""


class ServiceError(Exception):
    """服务层基础异常类"""
    
    def __init__(self, message: str, error_code: int = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        return f"ServiceError(code={self.error_code}, message='{self.message}')"


class ResourceNotFoundError(ServiceError):
    """资源不存在异常"""
    
    def __init__(self, resource_type: str, resource_id: any, error_code: int = 404):
        message = f"{resource_type}(ID={resource_id})不存在"
        super().__init__(message, error_code, {"resource_type": resource_type, "resource_id": resource_id})


class ResourceAlreadyExistsError(ServiceError):
    """资源已存在异常"""
    
    def __init__(self, resource_type: str, identifier: str, error_code: int = 409):
        message = f"{resource_type}({identifier})已存在"
        super().__init__(message, error_code, {"resource_type": resource_type, "identifier": identifier})


class ValidationError(ServiceError):
    """数据验证异常"""
    
    def __init__(self, field: str, value: any, message: str, error_code: int = 400):
        full_message = f"字段'{field}'验证失败: {message}"
        super().__init__(full_message, error_code, {"field": field, "value": value, "validation_message": message})


class PermissionDeniedError(ServiceError):
    """权限不足异常"""
    
    def __init__(self, resource_type: str, action: str, user_id: int = None, error_code: int = 403):
        message = f"对{resource_type}执行{action}操作权限不足"
        super().__init__(message, error_code, {
            "resource_type": resource_type, 
            "action": action, 
            "user_id": user_id
        })


class BusinessLogicError(ServiceError):
    """业务逻辑异常"""
    
    def __init__(self, message: str, error_code: int = 422, context: dict = None):
        super().__init__(message, error_code, context or {})


class ExternalServiceError(ServiceError):
    """外部服务异常"""
    
    def __init__(self, service_name: str, message: str, error_code: int = 502, response_data: dict = None):
        full_message = f"外部服务'{service_name}'错误: {message}"
        super().__init__(full_message, error_code, {
            "service_name": service_name,
            "original_message": message,
            "response_data": response_data
        })


class CacheError(ServiceError):
    """缓存操作异常"""
    
    def __init__(self, operation: str, key: str, message: str, error_code: int = 500):
        full_message = f"缓存{operation}操作失败(key={key}): {message}"
        super().__init__(full_message, error_code, {
            "operation": operation,
            "cache_key": key,
            "original_message": message
        })


class ConfigurationError(ServiceError):
    """配置错误异常"""
    
    def __init__(self, config_key: str, message: str, error_code: int = 500):
        full_message = f"配置错误({config_key}): {message}"
        super().__init__(full_message, error_code, {
            "config_key": config_key,
            "original_message": message
        })


class BatchOperationError(ServiceError):
    """批量操作异常"""
    
    def __init__(self, operation: str, total_count: int, failed_count: int, 
                 failed_ids: list = None, error_code: int = 422):
        message = f"批量{operation}操作部分失败: 总数{total_count}, 失败{failed_count}"
        super().__init__(message, error_code, {
            "operation": operation,
            "total_count": total_count,
            "failed_count": failed_count,
            "failed_ids": failed_ids or []
        })
