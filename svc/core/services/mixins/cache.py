import json
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from redis.asyncio import Redis

from svc.core.config.settings import get_settings

settings = get_settings()
CACHE_TTL = 3600  # 默认缓存时间1小时
CACHE_TTL_SHORT = 300
CACHE_TTL_LONG = 86400
CACHE_VERSION = "v1"


class JSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class CacheMixin(object):
    """增强的缓存相关功能mixin"""
    redis: Optional[Redis] = None

    def __init__(self, redis: Optional[Redis] = None, *args, **kwargs):
        """初始化缓存混入类

        Args:
            redis: Redis客户端实例
        """
        super().__init__(*args, **kwargs)
        self.redis = redis
        self._cache_stats = {"hit": 0, "miss": 0, "error": 0}

    def _get_resource_cache_key(self, resource_id: int) -> str:
        """生成资源缓存键"""
        resource_type = getattr(self, 'config', None) and self.config.resource_type or getattr(self, 'resource_type', '资源')
        return f"{resource_type}:{CACHE_VERSION}:{resource_id}"

    def _get_collection_cache_key(self, collection_name: str, **filters) -> str:
        """生成集合缓存键"""
        resource_type = getattr(self, 'config', None) and self.config.resource_type or getattr(self, 'resource_type', '资源')
        filter_str = ":".join(f"{k}={v}" for k, v in sorted(filters.items()))
        return f"{resource_type}:{CACHE_VERSION}:collection:{collection_name}:{filter_str}"

    def _get_cache_stats_key(self) -> str:
        """获取缓存统计键"""
        return f"cache:stats:{self.__class__.__name__}"

    async def _increment_cache_stats(self, stat_type: str) -> None:
        """增加缓存统计
        
        Args:
            stat_type: 统计类型 (hit/miss/error)
        """
        if not self.redis:
            return
        try:
            stats_key = self._get_cache_stats_key()
            await self.redis.hincrby(stats_key, stat_type, 1)
        except Exception as e:
            print(f"更新缓存统计失败: stat_type={stat_type}, 错误={str(e)}")

    async def cache_resource(self, key: str, resource: Any, expire: int = CACHE_TTL) -> None:
        if not self.redis:
            return
        try:
            if hasattr(resource, 'to_dict'):
                data = resource.to_dict()
            elif hasattr(resource, 'model_dump'):
                data = resource.model_dump()
            else:
                data = resource
            try:
                # 使用自定义JSON编码器处理datetime对象
                await self.redis.set(key, json.dumps(data, ensure_ascii=False, cls=JSONEncoder), ex=expire)
            except Exception as e:
                print(f"缓存资源时出错: key={key}, 错误={str(e)}")
            print(f"资源缓存成功: {key}")
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"缓存资源失败: key={key}, 错误={str(e)}")

    async def get_cached_resource(self, key: str, deserializer: Callable[[Dict[str, Any]], Any]) -> Optional[Any]:
        if not self.redis:
            return None
        try:
            try:
                data = await self.redis.get(key)
                if data:
                    await self._increment_cache_stats("hit")
                    return deserializer(json.loads(data))
                else:
                    await self._increment_cache_stats("miss")
                    return None
            except Exception as e:
                print(f"获取缓存资源时出错: key={key}, 错误={str(e)}")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"获取缓存资源失败: key={key}, 错误={str(e)}")
            return None

    async def delete_cache(self, key: str) -> None:
        if not self.redis:
            return
        try:
            await self.redis.delete(key)
            print(f"缓存删除成功: {key}")
        except Exception as e:
            print(f"删除缓存失败: key={key}, 错误={str(e)}")

    async def cache_collection(self, collection_name: str, items: List[Any], filters: Dict[str, Any] = None, expire: int = CACHE_TTL) -> None:
        if not self.redis:
            return
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            serialized_items = []
            for item in items:
                if hasattr(item, 'to_dict'):
                    serialized_items.append(item.to_dict())
                elif hasattr(item, 'model_dump'):
                    serialized_items.append(item.model_dump())
                else:
                    serialized_items.append(item)
            # 使用自定义JSON编码器处理datetime对象
            await self.redis.set(key, json.dumps(serialized_items, ensure_ascii=False, cls=JSONEncoder), ex=expire)
            print(f"集合缓存成功: {key}, count={len(items)}")
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"缓存集合失败: name={collection_name}, 错误={str(e)}")

    async def get_cached_collection(self, collection_name: str, deserializer: Callable[[List[Dict[str, Any]]], List[Any]], filters: Dict[str, Any] = None) -> Optional[List[Any]]:
        if not self.redis:
            return None
        try:
            key = self._get_collection_cache_key(collection_name, **(filters or {}))
            data = await self.redis.get(key)
            if data:
                await self._increment_cache_stats("hit")
                return deserializer(json.loads(data))
            else:
                await self._increment_cache_stats("miss")
                return None
        except Exception as e:
            await self._increment_cache_stats("error")
            print(f"获取缓存集合失败: name={collection_name}, 错误={str(e)}")
            return None

    async def get_cache_stats(self) -> Dict[str, int]:
        if not self.redis:
            return {"hit": 0, "miss": 0, "error": 0}
        try:
            stats_key = self._get_cache_stats_key()
            stats = await self.redis.hgetall(stats_key)
            return {
                "hit": int(stats.get(b"hit", 0)),
                "miss": int(stats.get(b"miss", 0)),
                "error": int(stats.get(b"error", 0))
            }
        except Exception as e:
            print(f"获取缓存统计失败: 错误={str(e)}", exc_info=True)
            return {"hit": 0, "miss": 0, "error": 0}            