"""
CRUD操作混入类。

提供标准化的CRUD操作接口，统一服务层的数据操作模式。
"""
from typing import Any, Dict, List, Optional, Union
from svc.core.models.result import Result
from svc.core.exceptions.error_codes import ErrorCode


class CRUDMixin:
    """CRUD操作混入类，提供标准化的数据操作接口"""
    
    async def get_by_id(self, resource_id: Union[str, int], use_cache: bool = None) -> Result:
        """获取指定ID的资源
        
        Args:
            resource_id: 资源ID
            use_cache: 是否使用缓存，None表示使用配置默认值
            
        Returns:
            Result: 包含资源数据的结果对象
        """
        try:
            # 确定是否使用缓存
            should_use_cache = (
                use_cache if use_cache is not None 
                else (hasattr(self, 'config') and self.config.cache_enabled and self.config.auto_cache_on_get)
            )
            
            # 尝试从缓存获取
            if should_use_cache and hasattr(self, 'redis') and self.redis:
                cache_key = self._get_resource_cache_key(resource_id)
                cached_data = await self.get_cached_resource(
                    cache_key,
                    lambda data: self._deserialize_resource(data)
                )
                if cached_data:
                    return self.create_success_result(cached_data)
            
            # 从数据库获取
            resource = await self.repository.get_by_id(resource_id)
            if not resource:
                return self.resource_not_found_result(resource_id)
            
            # 转换为响应格式
            response_data = self._serialize_resource(resource)
            
            # 自动缓存
            if should_use_cache and hasattr(self, 'redis') and self.redis:
                cache_key = self._get_resource_cache_key(resource_id)
                await self.cache_resource(cache_key, response_data, self.config.get_cache_ttl())
            
            return self.create_success_result(response_data)
            
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"获取资源失败: id={resource_id}, 错误={str(e)}", exc_info=True)
            
            # 触发错误事件
            if hasattr(self, 'dispatch_error_event'):
                self.dispatch_error_event("get_by_id", e, {"resource_id": resource_id})
            
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取{self.config.resource_type}失败: {str(e)}"
            )
    
    async def get_list(self, filters: Dict[str, Any] = None, page_num: int = 1, 
                      page_size: int = None, order_by: str = None) -> Result:
        """获取资源列表
        
        Args:
            filters: 过滤条件
            page_num: 页码
            page_size: 每页大小
            order_by: 排序字段
            
        Returns:
            Result: 包含分页列表数据的结果对象
        """
        try:
            # 使用配置的默认分页大小
            if page_size is None and hasattr(self, 'config'):
                page_size = self.config.default_page_size
            elif page_size is None:
                page_size = 20
            
            # 限制最大分页大小
            if hasattr(self, 'config') and page_size > self.config.max_page_size:
                page_size = self.config.max_page_size
            
            # 从仓库获取分页数据
            resources, total = await self.repository.get_paginated(
                filters=filters or {},
                page_num=page_num,
                page_size=page_size,
                order_by=order_by
            )
            
            # 转换为响应格式
            response_items = [self._serialize_resource(resource) for resource in resources]
            
            # 构建分页响应
            response_data = {
                "items": response_items,
                "total": total,
                "page_num": page_num,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size if total > 0 else 0
            }
            
            return self.create_success_result(response_data)
            
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"获取{self.config.resource_type}列表失败: 错误={str(e)}", exc_info=True)
            
            # 触发错误事件
            if hasattr(self, 'dispatch_error_event'):
                self.dispatch_error_event("get_list", e, {"filters": filters})
            
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取{self.config.resource_type}列表失败: {str(e)}"
            )
    
    async def create(self, data: Any, user_id: Optional[int] = None) -> Result:
        """创建资源
        
        Args:
            data: 创建数据
            user_id: 创建用户ID（用于审计）
            
        Returns:
            Result: 包含创建资源数据的结果对象
        """
        try:
            # 预处理数据
            create_data = self._prepare_create_data(data)
            
            # 创建资源
            resource = await self.repository.create(create_data)
            
            # 转换为响应格式
            response_data = self._serialize_resource(resource)
            
            # 自动缓存
            if (hasattr(self, 'config') and self.config.cache_enabled and 
                self.config.auto_cache_on_create and hasattr(self, 'redis') and self.redis):
                cache_key = self._get_resource_cache_key(resource.id)
                await self.cache_resource(cache_key, response_data, self.config.get_cache_ttl())
            
            # 触发创建事件
            if hasattr(self, 'dispatch_crud_event'):
                self.dispatch_crud_event("created", response_data, user_id=user_id)
            
            # 触发审计事件
            if hasattr(self, 'dispatch_audit_event'):
                self.dispatch_audit_event("created", user_id, resource.id, {"data": create_data})
            
            if hasattr(self, 'logger'):
                self.logger.info(f"{self.config.resource_type}创建成功: id={resource.id}")
            
            return self.create_success_result(response_data)
            
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"创建{self.config.resource_type}失败: 错误={str(e)}", exc_info=True)
            
            # 触发错误事件
            if hasattr(self, 'dispatch_error_event'):
                self.dispatch_error_event("create", e, {"data": data})
            
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建{self.config.resource_type}失败: {str(e)}"
            )
    
    def _serialize_resource(self, resource: Any) -> Dict[str, Any]:
        """序列化资源对象为响应格式
        
        Args:
            resource: 资源对象
            
        Returns:
            Dict[str, Any]: 序列化后的数据
        """
        if hasattr(resource, 'to_dict'):
            return resource.to_dict()
        elif hasattr(resource, 'model_dump'):
            return resource.model_dump()
        else:
            return resource
    
    def _deserialize_resource(self, data: Dict[str, Any]) -> Any:
        """反序列化数据为资源对象
        
        Args:
            data: 序列化的数据
            
        Returns:
            Any: 反序列化后的资源对象
        """
        # 子类可以重写此方法来提供特定的反序列化逻辑
        return data
    
    def _prepare_create_data(self, data: Any) -> Dict[str, Any]:
        """预处理创建数据
        
        Args:
            data: 原始创建数据
            
        Returns:
            Dict[str, Any]: 预处理后的数据
        """
        if hasattr(data, 'model_dump'):
            return data.model_dump()
        elif hasattr(data, 'dict'):
            return data.dict()
        elif isinstance(data, dict):
            return data
        else:
            return data
