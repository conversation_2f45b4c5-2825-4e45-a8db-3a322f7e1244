"""
事件处理混入类。

提供统一的事件触发和管理功能，标准化服务层的事件处理模式。
"""
from typing import Any, Dict, Optional
from svc.core.utils.datetime_utils import get_utc_now


class EventMixin:
    """事件处理混入类，提供标准化的事件触发功能"""
    
    def __init__(self, *args, **kwargs):
        """初始化事件混入类"""
        super().__init__(*args, **kwargs)
    
    def dispatch_event(self, action: str, payload: Dict[str, Any], **kwargs) -> None:
        """触发事件
        
        Args:
            action: 操作名称
            payload: 事件载荷数据
            **kwargs: 额外的事件参数
        """
        if not hasattr(self, 'config') or not self.config.enable_events:
            return
        
        try:
            from fastapi_events.dispatcher import dispatch
            
            event_name = self.config.get_event_name(action)
            
            # 标准化事件载荷
            standardized_payload = {
                "resource_type": self.config.resource_type,
                "action": action,
                "timestamp": get_utc_now().isoformat(),
                "data": payload,
                **kwargs
            }
            
            dispatch(event_name, payload=standardized_payload)
            
            if hasattr(self, 'logger'):
                self.logger.debug(f"事件已触发: {event_name}")
                
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"触发事件失败: action={action}, 错误={str(e)}")
    
    def dispatch_crud_event(self, action: str, resource_data: Dict[str, Any], **kwargs) -> None:
        """触发CRUD操作事件
        
        Args:
            action: CRUD操作名称 (created/updated/deleted等)
            resource_data: 资源数据
            **kwargs: 额外的事件参数
        """
        payload = {
            "resource_data": resource_data
        }
        
        # 添加资源ID（如果存在）
        if isinstance(resource_data, dict) and 'id' in resource_data:
            payload["resource_id"] = resource_data['id']
        elif hasattr(resource_data, 'id'):
            payload["resource_id"] = resource_data.id
        
        self.dispatch_event(action, payload, **kwargs)
    
    def dispatch_batch_event(self, action: str, resource_ids: list, **kwargs) -> None:
        """触发批量操作事件
        
        Args:
            action: 批量操作名称 (batch_updated/batch_deleted等)
            resource_ids: 资源ID列表
            **kwargs: 额外的事件参数
        """
        payload = {
            "resource_ids": resource_ids,
            "count": len(resource_ids)
        }
        
        self.dispatch_event(action, payload, **kwargs)
    
    def dispatch_error_event(self, action: str, error: Exception, context: Dict[str, Any] = None) -> None:
        """触发错误事件
        
        Args:
            action: 操作名称
            error: 异常对象
            context: 错误上下文信息
        """
        payload = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {}
        }
        
        self.dispatch_event(f"{action}_failed", payload)
    
    def dispatch_audit_event(self, action: str, user_id: Optional[int] = None, 
                           resource_id: Optional[int] = None, metadata: Dict[str, Any] = None) -> None:
        """触发审计事件
        
        Args:
            action: 操作名称
            user_id: 用户ID
            resource_id: 资源ID
            metadata: 审计元数据
        """
        if not hasattr(self, 'config') or not self.config.enable_audit_log:
            return
        
        try:
            from fastapi_events.dispatcher import dispatch
            
            payload = {
                "user_id": user_id,
                "action": action,
                "resource_type": self.config.resource_type,
                "resource_id": resource_id,
                "metadata": metadata or {}
            }
            
            dispatch("system:audit_log:recorded", payload=payload)
            
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"触发审计事件失败: action={action}, 错误={str(e)}")
