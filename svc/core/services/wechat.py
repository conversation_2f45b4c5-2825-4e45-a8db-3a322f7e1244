"""
微信小程序授权服务模块。
提供微信小程序授权登录相关功能。
"""
import asyncio
import json
import logging
from typing import Any, Dict, Optional, Tuple

import aiohttp
from fastapi import HTTPException, status
from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.models import User, WechatUser
from svc.apps.auth.repositories import RoleRepository, UserRepository
from svc.apps.auth.schemas.wechat import WechatLoginParams
from svc.core.config.settings import get_settings
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.models.result import Result
from svc.core.security.token import TokenService
from svc.core.services.base import BaseService
from svc.core.services.mixins.batch_operation import BatchOperationMixin
from svc.core.services.mixins.cache import CacheMixin
from svc.core.services.mixins.error_result import ErrorResultMixin
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 获取应用设置
settings = get_settings()

# 微信API地址
WX_CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session"


class WechatAuthService(BaseService, ErrorResultMixin, BatchOperationMixin, CacheMixin):
    """
    微信小程序认证服务类，提供微信用户认证和令牌管理功能
    
    该服务类负责：
    1. 处理微信小程序授权登录
    2. 绑定微信用户和系统用户
    3. 生成和验证JWT令牌
    """
    
    # 设置资源类型名称
    resource_type = "wechat_auth"
    
    def __init__(self, db, redis=None, token_service=None):
        BaseService.__init__(self)
        self.db = db
        self.redis = redis
        self.token_service = token_service
        # 其它依赖赋值
        self.settings = get_settings()
    
    async def code2Session(self, code: str) -> Dict[str, Any]:
        """
        通过微信临时登录凭证code获取用户唯一标识openid和会话密钥session_key
        
        Args:
            code: 微信临时登录凭证
            
        Returns:
            Dict: 微信返回的数据，包含openid和session_key
            
        Raises:
            HTTPException: 当请求微信API失败或返回错误码时
        """
        if not code:
            self.logger.error("微信code参数为空")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="微信code参数不能为空"
            )
            
        self.logger.info(f"正在请求微信code2session, code: {code[:5]}...")
        
        # 构建请求参数
        params = {
            "appid": self.settings.wechat_app_id,
            "secret": self.settings.wechat_app_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        # 记录请求参数(去除敏感信息)
        safe_params = params.copy()
        if "secret" in safe_params:
            safe_params["secret"] = "***" 
        self.logger.debug(f"微信API请求参数: {safe_params}")
        try:
            timeout = aiohttp.ClientTimeout(total=10)  # 设置超时为10秒
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(WX_CODE2SESSION_URL, params=params) as response:
                    # 检查HTTP状态码
                    if response.status != 200:
                        self.logger.error(f"微信API请求失败, 状态码: {response.status}, headers: {response.headers}")
                        result_text = await response.text()
                        self.logger.error(f"微信API错误响应内容: {result_text[:500]}")
                        raise HTTPException(
                            status_code=status.HTTP_502_BAD_GATEWAY,
                            detail=f"微信服务器请求失败: 状态码 {response.status}"
                        )
                    
                    # 先获取文本内容，然后尝试解析为JSON
                    content_type = response.headers.get('Content-Type', '')
                    text = await response.text()
                    
                    self.logger.debug(f"微信API响应内容类型: {content_type}")
                    self.logger.debug(f"微信API响应内容: {text[:200]}")
                    
                    # 尝试解析为JSON
                    try:
                        resp_data = json.loads(text)
                    except json.JSONDecodeError as e:
                        self.logger.error(f"解析微信API响应失败: {str(e)}, 响应内容: {text[:200]}")
                        raise HTTPException(
                            status_code=status.HTTP_502_BAD_GATEWAY,
                            detail=f"解析微信服务响应失败: {text[:100]}"
                        )
                    
                    # 检查微信API返回结果
                    if "errcode" in resp_data and resp_data["errcode"] != 0:
                        error_code = resp_data.get("errcode")
                        error_msg = resp_data.get("errmsg", "未知错误")
                        self.logger.error(f"微信API返回错误: {error_code}, {error_msg}")
                        
                        if error_code == 40029:
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="无效的code"
                            )
                        elif error_code == 45011:
                            raise HTTPException(
                                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                                detail="API调用频率超限"
                            )
                        elif error_code == 40226:
                            raise HTTPException(
                                status_code=status.HTTP_403_FORBIDDEN,
                                detail="高风险等级用户，小程序登录拦截"
                            )
                        elif error_code == -1:
                            raise HTTPException(
                                status_code=status.HTTP_502_BAD_GATEWAY,
                                detail="微信系统繁忙，请稍后再试"
                            )
                        else:
                            raise HTTPException(
                                status_code=status.HTTP_502_BAD_GATEWAY,
                                detail=f"微信错误: {error_code} - {error_msg}"
                            )
                    
                    if "openid" not in resp_data:
                        self.logger.error(f"微信API返回数据缺少openid: {resp_data}")
                        raise HTTPException(
                            status_code=status.HTTP_502_BAD_GATEWAY,
                            detail="微信服务返回数据格式异常: 缺少openid"
                        )
                    
                    self.logger.info(f"微信code2session请求成功, openid: {resp_data['openid'][:8]}...")
                    return resp_data
                    
        except aiohttp.ClientError as e:
            self.logger.error(f"请求微信API时发生网络错误: {type(e).__name__}, {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"请求微信服务器失败: {str(e)}"
            )
        except asyncio.TimeoutError:
            self.logger.error("请求微信API超时")
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="请求微信服务器超时"
            )
        except Exception as e:
            self.logger.error(f"请求微信API时发生未预期错误: {type(e).__name__}, {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"处理微信请求时发生错误: {str(e)}"
            )
    
    async def _get_wechat_user_info(self, code: str) -> Dict[str, Any]:
        """获取微信用户信息
        
        Args:
            code: 微信临时登录凭证
            
        Returns:
            Dict[str, Any]: 包含openid等用户信息
        """
        wx_session_info = await self.code2Session(code)
        self.logger.debug(f"获取到微信用户信息: openid={wx_session_info.get('openid')[:8] if wx_session_info.get('openid') else 'None'}...")
        return {
            "openid": wx_session_info.get("openid"),
            "session_key": wx_session_info.get("session_key"),
            "unionid": wx_session_info.get("unionid")
        }

    async def _verify_system_user(self, user_id: int) -> Tuple[Optional[User], Optional[Result]]:
        """验证系统用户状态
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            Tuple[Optional[User], Optional[Result]]: 返回用户对象和错误结果（如有）
        """
        user = await self.get_user_by_id(user_id)
        
        if not user:
            self.logger.warning(f"关联的系统用户不存在: {user_id}")
            return None, self.create_error_result(
                error_code=ErrorCode.USER_NOT_FOUND,
                error_message="用户不存在"
            )
        
        if not user.is_active:
            self.logger.warning(f"关联的系统用户未激活: {user_id}")
            return None, self.create_error_result(
                error_code=ErrorCode.USER_INACTIVE,
                error_message="用户未激活"
            )
        
        return user, None

    async def _update_login_cache(self, openid: str, user_id: int, token_data) -> None:
        """更新登录相关缓存
        
        Args:
            openid: 微信openid
            user_id: 系统用户ID
            token_data: 令牌数据
        """
        if not self.redis:
            return
            
        try:
            # 缓存令牌
            token_key = f"token:{token_data.access_token}"
            await self.redis.set(
                token_key,
                user_id,
                ex=token_data.expires_in
            )
            
            # 缓存微信用户与系统用户的映射
            wx_user_key = f"wx:openid:{openid}"
            await self.redis.set(
                wx_user_key,
                user_id,
                ex=86400 * 7  # 7天
            )
            
            self.logger.debug(f"微信用户登录缓存更新成功: user_id={user_id}")
        except Exception as e:
            self.logger.warning(f"微信用户登录缓存更新失败: {str(e)}")

    async def _emit_login_event(self, user_id: int, wechat_user_id: int,ip_address: str, user_agent: str,last_login: str) -> None: 
        """发送登录事件
        
        Args:
            user_id: 系统用户ID
            wechat_user_id: 微信用户ID
        """
        event_data= {
                    "user_id": user_id,
                    "login_type": "wechat",
                    "wechat_user_id": wechat_user_id,
                    "ip_address": ip_address,
                    "user_agent": user_agent,
                    "last_login_time": last_login
                }
        try:
            dispatch(
                event_name=event_names.AUTH_USER_LOGGED_IN,payload=event_data
            )
        except Exception as e:
            # 记录错误但不阻止主流程
            self.logger.error(f"发送微信登录事件失败: {str(e)}", exc_info=True)

    async def _update_user_last_login(self, user) -> None:
        """更新用户最后登录时间
        
        Args:
            user: 用户对象
        """
        try:
            current_time = get_utc_now_without_tzinfo()
            from svc.apps.auth.repositories.user import UserRepository
            await UserRepository(self.db).update_last_login(user)
            self.logger.debug(f"更新用户最后登录时间成功: user_id={user.id}, time={current_time}")
        except Exception as e:
            self.logger.warning(f"更新用户最后登录时间失败: user_id={user.id}, 错误={str(e)}")

    async def login(self, login_params: WechatLoginParams) -> Result:
        """
        处理微信小程序登录请求
        
        Args:
            login_params: 微信登录参数，包含code和用户信息
                
        Returns:
            Result: 登录结果
        """
        try:
            self.logger.info("处理微信小程序登录")
            
            # 1. 获取微信用户信息
            wx_info = await self._get_wechat_user_info(login_params.code)
            openid = wx_info["openid"]
            
            if not openid:
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_CREDENTIALS,
                    error_message="无法获取微信用户信息"
                )
            
            # 2. 查找或创建微信用户
            wechat_user, created = await self.find_or_create_wechat_user(
                openid=openid,
                unionid=wx_info.get("unionid"),
                user_info=login_params.user_info,
                from_code=login_params.from_code
            )
            
            # 3. 处理系统用户关联
            user = None
            
            # 3.1 已关联系统用户的情况
            if wechat_user.user_id:
                user, error_result = await self._verify_system_user(wechat_user.user_id)
                if error_result:
                    return error_result
            
            # 3.2 未关联系统用户的情况
            else:
                # 根据配置决定是否自动创建用户
                if getattr(self.settings, 'wechat_auto_create_user', True):
                    user = await self.create_user_from_wechat(wechat_user)
                    
                    # 更新微信用户关联
                    wechat_user.user_id = user.id
                    await self.update_wechat_user(wechat_user)
                else:
                    # 返回微信用户信息，但不创建系统用户
                    return self.create_success_result({
                        "wechat_user": wechat_user.to_dict(),
                        "needs_register": True
                    })
            
            # 3.3 更新用户最后登录时间
            if user:
                await self._update_user_last_login(user)
            
            # 4. 创建token
            token_data = self._create_token_data(user, {
                "openid": openid,
                "wechat_user_id": wechat_user.id,
            })
            
            # 5. 发送登录事件与更新缓存（并行处理）
            import asyncio
            await asyncio.gather(
                self._emit_login_event(user.id, wechat_user.id,login_params.ip_address,login_params.user_agent,user.last_login),
                self._update_login_cache(openid, user.id, token_data)
            )
            
            # 6. 返回登录成功结果
            from svc.apps.auth.schemas.auth import WechatResponse
            self.logger.info(f"微信用户登录成功: openid={openid[:8] if openid else 'None'}..., user_id={user.id}")
            return self.create_success_result(WechatResponse(
                token_data=token_data,
                from_code=login_params.from_code,
                is_new_user=created,
                user_info=wechat_user.to_dict()
            ))
                
        except HTTPException as e:
            # 直接抛出HTTP异常，让外层处理
            raise
        except Exception as e:
            self.logger.error(f"微信登录失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.LOGIN_FAILED,
                error_message="微信登录失败，请稍后再试"
            )
    
    async def find_or_create_wechat_user(
        self, 
        openid: str, 
        unionid: Optional[str] = None,
        user_info: Optional[Dict[str, Any]] = None,
        from_code:Optional[str]=None
    ) -> Tuple[Any, bool]:
        """
        查找或创建微信用户
        
        Args:
            openid: 微信用户的openid
            unionid: 微信用户的unionid
            user_info: 微信用户信息
            
        Returns:
            Tuple[WechatUser, bool]: 微信用户对象和是否是新创建的标志
        """
        # 注意: 这里假设已有WechatUser模型
        from svc.apps.auth.models.wechat_user import WechatUser

        # 先尝试通过openid查找
        from svc.apps.auth.repositories.wechat_user import WechatUserRepository
        wechat_user_repo = WechatUserRepository(self.db)
        wechat_user = await wechat_user_repo.get_one(openid=openid)
        
        # 如果找到，更新用户信息
        if wechat_user:
            self.logger.info(f"找到已存在的微信用户: id={wechat_user.id}")
            
            # 更新最后登录时间
            wechat_user.last_login = get_utc_now_without_tzinfo()
            
            # 如果提供了用户信息，则更新
            if user_info:
                wechat_user.nickname = user_info.get("nickName") or wechat_user.nickname
                wechat_user.avatar_url = user_info.get("avatarUrl") or wechat_user.avatar_url
                wechat_user.gender = user_info.get("gender") or wechat_user.gender
                wechat_user.country = user_info.get("country") or wechat_user.country
                wechat_user.province = user_info.get("province") or wechat_user.province
                wechat_user.city = user_info.get("city") or wechat_user.city
                wechat_user.language = user_info.get("language") or wechat_user.language
                
                # 更新unionid（如果之前没有）
                if unionid and not wechat_user.unionid:
                    wechat_user.unionid = unionid
                    
                await self.update_wechat_user(wechat_user)
                
            return wechat_user, False
        
        # 如果未找到，创建新用户
        self.logger.info(f"创建新的微信用户: openid={openid[:8]}...")
        
        # 创建微信用户
        wechat_user = WechatUser(
            openid=openid,
            unionid=unionid,
            nickname=user_info.get("nickName") if user_info else None,
            avatar_url=user_info.get("avatarUrl") if user_info else None,
            gender=user_info.get("gender") if user_info else None,
            country=user_info.get("country") if user_info else None,
            province=user_info.get("province") if user_info else None,
            city=user_info.get("city") if user_info else None,
            language=user_info.get("language") if user_info else None,
            from_code=from_code,
            last_login=get_utc_now_without_tzinfo()
        )
        
        # 保存微信用户
        wechat_user = await wechat_user_repo.create(wechat_user.__dict__)
        
        return wechat_user, True
    
    async def update_wechat_user(self, wechat_user) -> None:
        """
        更新微信用户信息
        
        Args:
            wechat_user: 微信用户对象
        """
        # 保存到数据库
        from svc.apps.auth.repositories.wechat_user import WechatUserRepository
        wechat_user_repo = WechatUserRepository(self.db)
        await wechat_user_repo.update(wechat_user, wechat_user.__dict__)
        
        # 更新缓存
        if self.redis:
            try:
                cache_key = f"wx:user:{wechat_user.id}"
                await self.cache_resource(cache_key, wechat_user)
                
                openid_key = f"wx:openid:{wechat_user.openid}"
                if wechat_user.user_id:
                    await self.redis.set(openid_key, wechat_user.user_id, ex=86400 * 7)  # 7天
            except Exception as e:
                self.logger.warning(f"缓存微信用户信息失败: {str(e)}")
    
    async def get_user_by_id(self, user_id: int):
        """
        获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            User: 用户对象
        """
        from svc.apps.auth.repositories.user import UserRepository
        return await UserRepository(self.db).get_by_id(user_id)
    
    async def create_user_from_wechat(self, wechat_user:WechatUser) -> Any:
        """
        从微信用户创建系统用户
        
        Args:
            wechat_user: 微信用户对象
            
        Returns:
            User: 创建的用户对象
        """
       
        import uuid

        from svc.core.security.password import get_password_hash

        # 生成随机用户名、邮箱和密码
        username = f"wx_{wechat_user.openid[:8]}_{uuid.uuid4().hex[:8]}"
        email = f"{username}@wechat.user"
        random_password = uuid.uuid4().hex
        
        # 创建新用户（复用仓库 create），并补充额外字段
        user_data={
            "email": email,
            "hashed_password": get_password_hash(random_password),
            "fullname": wechat_user.nickname or username,
            "is_superuser": False,
            "username": username,
        }
        user = await UserRepository(self.db).create(
           user_data
        )
        # 设置额外属性
        await UserRepository(self.db).update(user, {
            "avatar_url": wechat_user.avatar_url,
            "locale": "zh-CN",
            "last_login": wechat_user.last_login,
            "is_active": True,
        })
        
        # 添加默认角色
        default_role_name = getattr(self.settings, 'default_user_role', 'user')
        await self.add_user_role(user, default_role_name)
        
        self.logger.info(f"从微信用户创建系统用户成功: user_id={user.id}")
        
        return user
    
    async def add_user_role(self, user: User, role_name: str) -> None:
        """
        为用户添加角色
        
        Args:
            user: 用户对象
            role_name: 角色名称
        """
        # 获取角色
        role = await RoleRepository(self.db).get_one(name=role_name)
        
        if role:
            # 添加角色
            await UserRepository(self.db).add_role( user, role)
        else:
            self.logger.warning(f"角色不存在: {role_name}")
    
    def _create_token_data(self, user: any, extra_data: Dict[str, Any] = None) -> Any:
        """
        创建令牌数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            TokenData: 令牌数据对象
        """
        from svc.apps.auth.schemas import TokenData

        # 使用TokenService创建令牌
        access_token = self.token_service.create_access_token(
            user_id=user.id,
            extra_data=extra_data or {}
        )
        
        # 创建可选的刷新令牌
        refresh_token = self.token_service.create_refresh_token(user.id)
        
        return TokenData(
            last_login=user.last_login,
            access_token=access_token,
            token_type="bearer",
            expires_in=self.settings.access_token_expire_minutes * 60,
            refresh_token=refresh_token,
            # user_id=user_id
        )
    
    async def bind_wechat_user(self, wechat_user_id: int, user_id: int) -> Result:
        """
        绑定微信用户和系统用户
        
        Args:
            wechat_user_id: 微信用户ID
            user_id: 系统用户ID
            
        Returns:
            Result: 绑定结果
        """
        try:
            from svc.apps.auth.models.user import User

            # 获取微信用户
            wechat_user = await self.get_wechat_user_by_id(wechat_user_id)
            if not wechat_user:
                return self.resource_not_found_result(
                    resource_id=wechat_user_id,
                    result_code=ErrorCode.NOT_FOUND
                )
            
            # 获取系统用户
            from svc.apps.auth.repositories.user import UserRepository
            user_repo = UserRepository(self.db)
            user = await user_repo.get_by_id(user_id)
            if not user:
                return self.resource_not_found_result(
                    result_code=ErrorCode.USER_NOT_FOUND,
                    resource_id=user_id
                )
            
            # 检查是否已绑定其他用户
            if wechat_user.user_id and wechat_user.user_id != user_id:
                self.logger.warning(f"微信用户已绑定其他系统用户: wechat_user_id={wechat_user_id}, user_id={wechat_user.user_id}")
                return self.create_error_result(
                    result_code=ErrorCode.OPERATION_FAILED,
                    result_msg="该微信账号已绑定其他用户"
                )
            
            # 绑定用户
            wechat_user.user_id = user_id
            await self.update_wechat_user(wechat_user)
            
            self.logger.info(f"微信用户绑定成功: wechat_user_id={wechat_user_id}, user_id={user_id}")
            
            return self.create_success_result({
                "wechat_user_id": wechat_user_id,
                "user_id": user_id
            })
            
        except Exception as e:
            self.logger.error(f"绑定微信用户失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                result_code=ErrorCode.OPERATION_FAILED,
                result_msg=f"绑定微信用户失败: {str(e)}"
            )
    
    async def get_wechat_user_by_id(self, wechat_user_id: int) -> Optional[Any]:
        """
        根据微信用户ID获取微信用户信息
        
        Args:
            wechat_user_id: 微信用户ID
            
        Returns:
            Optional[WechatUser]: 微信用户对象，如果不存在则返回None
        """
        from svc.apps.auth.repositories.wechat_user import WechatUserRepository
        wechat_user_repo = WechatUserRepository(self.db)
        return await wechat_user_repo.get_by_id(wechat_user_id)
        
    async def get_wechat_user_by_user_id(self, user_id: int) -> Optional[Any]:
        """
        根据系统用户ID获取关联的微信用户信息
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            Optional[WechatUser]: 微信用户对象，如果不存在则返回None
        """
        from svc.apps.auth.repositories.wechat_user import WechatUserRepository
        wechat_user_repo = WechatUserRepository(self.db)
        return await wechat_user_repo.get_one(user_id=user_id)
        
    async def update_wechat_user_info(self, user_id: int, update_data: Dict[str, Any]) -> Result:
        """
        更新微信用户信息
        
        Args:
            user_id: 系统用户ID
            update_data: 要更新的数据
            
        Returns:
            Result: 更新结果
        """
        try:
            # 获取微信用户
            wechat_user = await self.get_wechat_user_by_user_id(user_id)
            
            if not wechat_user:
                return self.resource_not_found_result(
                    resource_id=user_id,
                    result_code=ErrorCode.NOT_FOUND
                )
            
            # 更新信息
            if update_data:
                from svc.apps.auth.repositories.wechat_user import WechatUserRepository
                wechat_user_repo = WechatUserRepository(self.db)
                await wechat_user_repo.update(wechat_user, update_data)
                
            event_data = {
                "user_id": user_id,
                "updated_fields": list(update_data.keys()),
                "old_values": {k: getattr(wechat_user, k) for k in update_data if k != 'password'},
                "updated_at": wechat_user.updated_at.isoformat() if wechat_user.updated_at else None,
                "updater_id": user_id # 更新者ID
            }
            dispatch(event_names.AUTH_USER_UPDATED, payload=event_data)
            # 返回更新后的微信用户信息
            return self.create_success_result({
                "wechat_user": wechat_user.to_dict()
            })
        except Exception as e:
            self.logger.error(f"更新微信用户信息失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新微信用户信息失败: {str(e)}"
            ) 