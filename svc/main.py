"""
应用入口模块。
"""
import logging
import sys
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi_events.middleware import EventHandlerASGIMiddleware

from svc.core.config.env_loader import get_current_env
from svc.core.config.settings import get_settings
from svc.core.database import close_engine, get_engine
from svc.core.events.local_handlers import local_handler
from svc.core.middleware import middleware_config
from svc.core.middleware.registry import setup_middlewares
from svc.core.router.router_factory import register_routers

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理器。
    在应用启动和关闭时执行必要的操作。
    """
    try:
        # 加载环境配置
        env = get_current_env()
        logger.info(f"应用启动 - 环境: {env}")
        
        # 初始化数据库引擎
        await get_engine(testing=False)
        logger.info("数据库引擎已初始化")
        
        # 启动期权限系统初始化
        try:
            from svc.core.security.resource_discovery import setup_permission_system

            module_packages = [
                "svc.apps.albums.models",
                "svc.apps.auth.models",
                "svc.apps.billing.models",
                "svc.apps.products.models",
                "svc.apps.system.models",
                "svc.apps.shops.models",
                "svc.apps.marketing.models",
            ]

            # 使用新的权限系统初始化函数
            permission_service = setup_permission_system(
                module_packages=module_packages,
                strict=True,
                auto_configure_auth=True
            )
            logger.info("权限系统初始化完成")
        except Exception as e:
            logger.error(f"权限系统初始化失败: {e}")
            raise
        
        
        yield
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    finally:
        # 关闭数据库引擎
        await close_engine()
        logger.info("数据库引擎已关闭")
        logger.info("应用关闭")



def create_app() -> FastAPI:
    """
    创建FastAPI应用实例。
    
    Returns:
        FastAPI: 应用实例
    """
    # get_config_info()
    # 获取配置
    settings = get_settings()
    # 创建应用实例
    app = FastAPI(
        title=settings.project_name,
        description="AI Tools Service",
        version=settings.version,
        docs_url="/docs",
        redoc_url="/redocs",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    setup_middlewares(app, middleware_config)
    # Add the event handler middleware
    app.add_middleware(EventHandlerASGIMiddleware, handlers=[local_handler])
    
    # 注册路由
    register_routers(app)
    
    @app.get("/")
    async def root():
        return {"message": f"欢迎使用 {settings.project_name} API {settings.version}"}
    
    return app

# 创建应用实例
app = create_app()

# 应用启动和关闭事件

