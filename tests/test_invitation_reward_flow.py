"""
邀请奖励流程测试
验证邀请注册业务流程的完整性和正确性
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from svc.apps.marketing.services.reward import RewardRecordService
from svc.apps.marketing.services.invitation import InvitationService
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.models.campaign import Campaign
from svc.apps.marketing.models.reward import RewardRecord, RewardStrategy
from svc.core.models.result import Result


class TestInvitationRewardFlow:
    """邀请奖励流程测试类"""
    
    @pytest.fixture
    def mock_invitation(self):
        """模拟邀请记录"""
        invitation = MagicMock(spec=Invitation)
        invitation.id = 1
        invitation.inviter_id = 100
        invitation.invitee_id = 101
        invitation.campaign_id = 1
        invitation.code = "TEST123"
        invitation.is_used = False
        invitation.used_at = None
        return invitation
    
    @pytest.fixture
    def mock_campaign(self):
        """模拟活动记录"""
        campaign = MagicMock(spec=Campaign)
        campaign.id = 1
        campaign.status = "active"
        campaign.start_date = datetime(2024, 1, 1)
        campaign.end_date = datetime(2024, 12, 31)
        return campaign
    
    @pytest.fixture
    def mock_strategy(self):
        """模拟奖励策略"""
        strategy = MagicMock(spec=RewardStrategy)
        strategy.id = 1
        strategy.campaign_id = 1
        strategy.is_active = True
        strategy.check_constraints.return_value = True
        strategy.get_calculation_config.return_value = {"reward_type": "points", "amount": 100}
        strategy.get_distribution_config.return_value = {"channels": ["wallet"]}
        return strategy
    
    @pytest.fixture
    def mock_reward_record(self):
        """模拟奖励记录"""
        record = MagicMock(spec=RewardRecord)
        record.id = 1
        record.user_id = 100
        record.strategy_id = 1
        record.reward_value = 100
        record.status = "pending"
        return record
    
    @pytest.mark.asyncio
    async def test_process_invitation_rewards_success(self, mock_invitation, mock_campaign, mock_strategy, mock_reward_record):
        """测试邀请奖励处理成功"""
        # 模拟依赖服务
        mock_invitation_repo = AsyncMock()
        mock_invitation_repo.get_by_id.return_value = mock_invitation
        mock_invitation_repo.update_by_id.return_value = True
        
        mock_campaign_service = AsyncMock()
        mock_campaign_service.get_campaign.return_value = Result.success(mock_campaign)
        
        mock_strategy_service = AsyncMock()
        mock_strategy_service.get_applicable_strategies.return_value = Result.success([mock_strategy])
        
        mock_reward_record_repo = AsyncMock()
        mock_reward_record_repo.create_reward.return_value = mock_reward_record
        
        # 创建服务实例
        service = RewardRecordService(
            invitation_repo=mock_invitation_repo,
            campaign_service=mock_campaign_service,
            strategy_service=mock_strategy_service,
            reward_record_repo=mock_reward_record_repo
        )
        
        # 执行测试
        result = await service.process_invitation_rewards(invitation_id=1)
        
        # 验证结果
        assert result.is_success
        assert "reward_record_ids" in result.data
        assert len(result.data["reward_record_ids"]) > 0
        
        # 验证调用
        mock_invitation_repo.get_by_id.assert_called_once_with(1)
        mock_campaign_service.get_campaign.assert_called_once_with(1)
        mock_strategy_service.get_applicable_strategies.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_process_invitation_rewards_invitation_not_found(self):
        """测试邀请记录不存在的情况"""
        # 模拟依赖服务
        mock_invitation_repo = AsyncMock()
        mock_invitation_repo.get_by_id.return_value = None
        
        # 创建服务实例
        service = RewardRecordService(invitation_repo=mock_invitation_repo)
        
        # 执行测试
        result = await service.process_invitation_rewards(invitation_id=999)
        
        # 验证结果
        assert not result.is_success
        assert "邀请记录不存在" in result.result_msg
    
    @pytest.mark.asyncio
    async def test_process_invitation_rewards_campaign_not_found(self, mock_invitation):
        """测试活动不存在的情况"""
        # 模拟依赖服务
        mock_invitation_repo = AsyncMock()
        mock_invitation_repo.get_by_id.return_value = mock_invitation
        
        mock_campaign_service = AsyncMock()
        mock_campaign_service.get_campaign.return_value = Result.error("CAMPAIGN_NOT_FOUND", "活动不存在")
        
        # 创建服务实例
        service = RewardRecordService(
            invitation_repo=mock_invitation_repo,
            campaign_service=mock_campaign_service
        )
        
        # 执行测试
        result = await service.process_invitation_rewards(invitation_id=1)
        
        # 验证结果
        assert not result.is_success
        assert "关联活动不存在" in result.result_msg
    
    @pytest.mark.asyncio
    async def test_process_invitation_rewards_no_strategies(self, mock_invitation, mock_campaign):
        """测试没有适用奖励策略的情况"""
        # 模拟依赖服务
        mock_invitation_repo = AsyncMock()
        mock_invitation_repo.get_by_id.return_value = mock_invitation
        mock_invitation_repo.update_by_id.return_value = True
        
        mock_campaign_service = AsyncMock()
        mock_campaign_service.get_campaign.return_value = Result.success(mock_campaign)
        
        mock_strategy_service = AsyncMock()
        mock_strategy_service.get_applicable_strategies.return_value = Result.success([])
        
        # 创建服务实例
        service = RewardRecordService(
            invitation_repo=mock_invitation_repo,
            campaign_service=mock_campaign_service,
            strategy_service=mock_strategy_service
        )
        
        # 执行测试
        result = await service.process_invitation_rewards(invitation_id=1)
        
        # 验证结果
        assert result.is_success
        assert result.data["reward_record_ids"] == []
    
    @pytest.mark.asyncio
    async def test_invitation_complete_flow(self):
        """测试完整的邀请完成流程"""
        # 模拟邀请服务
        mock_invitation_service = AsyncMock()
        mock_invitation_service.complete_invitation.return_value = Result.success({
            "invitation_id": 1,
            "inviter_id": 100,
            "code": "TEST123",
            "campaign_id": 1
        })
        
        # 模拟奖励记录服务
        mock_reward_service = AsyncMock()
        mock_reward_service.process_invitation_rewards.return_value = Result.success({
            "reward_record_ids": [1, 2]
        })
        
        # 执行测试
        invitation_result = await mock_invitation_service.complete_invitation(
            code="TEST123",
            invitee_id=101,
            ip="127.0.0.1",
            device_info="test-device"
        )
        
        # 验证邀请完成
        assert invitation_result.is_success
        assert invitation_result.data["invitation_id"] == 1
        
        # 模拟奖励处理
        reward_result = await mock_reward_service.process_invitation_rewards(invitation_id=1)
        
        # 验证奖励处理
        assert reward_result.is_success
        assert len(reward_result.data["reward_record_ids"]) == 2


if __name__ == "__main__":
    pytest.main([__file__])
