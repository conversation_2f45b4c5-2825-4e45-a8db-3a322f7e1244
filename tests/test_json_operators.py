"""
JSON 操作符测试
"""
import pytest
from unittest.mock import Mock, MagicMock
from sqlalchemy import Column, String, JSON
from sqlalchemy.ext.declarative import declarative_base

from svc.core.repositories.base import FilterOperator, FilterBuilder, FilterCondition

Base = declarative_base()

class TestModel(Base):
    __tablename__ = "test_table"
    
    id = Column(String, primary_key=True)
    tags = Column(JSON, default=[])
    meta_data = Column(JSON, default={})

def test_json_contains_operator():
    """测试 JSON_CONTAINS 操作符"""
    # 创建模拟的列
    mock_column = Mock()
    mock_column.contains = Mock(return_value="tags @> '[\"banner\"]'")
    
    # 创建过滤条件
    condition = FilterCondition(
        field="tags",
        operator=FilterOperator.JSON_CONTAINS,
        value=["banner"]
    )
    
    # 构建 SQL 条件
    builder = FilterBuilder()
    result = builder.build_sql_condition(mock_column, condition)
    
    # 验证结果
    assert result is not None
    mock_column.contains.assert_called_once_with(["banner"])

def test_json_extract_operator():
    """测试 JSON_EXTRACT 操作符"""
    # 创建模拟的列
    mock_column = Mock()
    mock_column.__getitem__ = Mock(return_value=Mock())
    mock_column.__getitem__().__eq__ = Mock(return_value="meta_data->'user'->>'role' = 'admin'")
    
    # 创建过滤条件
    condition = FilterCondition(
        field="meta_data",
        operator=FilterOperator.JSON_EXTRACT,
        value=["user.role", "admin"]
    )
    
    # 构建 SQL 条件
    builder = FilterBuilder()
    result = builder.build_sql_condition(mock_column, condition)
    
    # 验证结果
    assert result is not None
    mock_column.__getitem__.assert_called_with("user.role")

def test_json_has_key_operator():
    """测试 JSON_HAS_KEY 操作符"""
    # 创建模拟的列
    mock_column = Mock()
    mock_column.has_key = Mock(return_value="meta_data ? 'user'")
    
    # 创建过滤条件
    condition = FilterCondition(
        field="meta_data",
        operator=FilterOperator.JSON_HAS_KEY,
        value="user"
    )
    
    # 构建 SQL 条件
    builder = FilterBuilder()
    result = builder.build_sql_condition(mock_column, condition)
    
    # 验证结果
    assert result is not None
    mock_column.has_key.assert_called_once_with("user")

def test_filter_builder_from_dict_with_json_operators():
    """测试 FilterBuilder.from_dict 方法对 JSON 操作符的支持"""
    # 创建过滤字典
    filters = {
        "tags__json_contains": ["banner"],
        "meta_data__json_has_key": "user",
        "meta_data__json_extract": ["user.role", "admin"]
    }
    
    # 从字典创建 FilterBuilder
    builder = FilterBuilder.from_dict(filters)
    
    # 验证条件数量
    assert len(builder.conditions) == 3
    
    # 验证操作符
    operators = [condition.operator for condition in builder.conditions]
    assert FilterOperator.JSON_CONTAINS in operators
    assert FilterOperator.JSON_HAS_KEY in operators
    assert FilterOperator.JSON_EXTRACT in operators
    
    # 验证字段
    fields = [condition.field for condition in builder.conditions]
    assert "tags" in fields
    assert "meta_data" in fields

def test_complex_json_filters():
    """测试复杂的 JSON 过滤条件"""
    # 创建简单的过滤字典，避免 __or__ 的复杂处理
    filters = {
        "status": "active",
        "tags__json_contains": ["featured"],
        "meta_data__json_has_key": "user"
    }
    
    # 从字典创建 FilterBuilder
    builder = FilterBuilder.from_dict(filters)
    
    # 验证条件数量
    assert len(builder.conditions) == 3
    
    # 验证操作符类型
    operators = [condition.operator for condition in builder.conditions]
    assert FilterOperator.EQ in operators  # status = "active"
    assert FilterOperator.JSON_CONTAINS in operators  # tags 包含
    assert FilterOperator.JSON_HAS_KEY in operators  # meta_data 包含键

if __name__ == "__main__":
    pytest.main([__file__])
