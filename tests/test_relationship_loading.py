"""
关系加载功能测试

测试新的关系加载功能是否正常工作
"""

import pytest
from sqlalchemy.orm import selectinload

from svc.apps.products.models.product import Product
from svc.apps.products.models.category import Category
from svc.apps.products.models.spec import Spec
from svc.apps.products.repositories.product import ProductRepository
from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.repositories.spec import SpecRepository


class TestRelationshipLoading:
    """关系加载功能测试类"""

    @pytest.mark.asyncio
    async def test_automatic_selectin_loading(self, db_session):
        """测试自动selectin关系加载"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 测试自动加载
        repo = ProductRepository(db_session)
        loaded_product = await repo.get_by_id(product.id)

        # 验证关系是否自动加载
        assert loaded_product.category is not None
        assert loaded_product.category.name == "测试分类"

    @pytest.mark.asyncio
    async def test_default_relationship_loading(self, db_session):
        """测试默认关系加载功能"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 测试默认关系加载（不传递load_options）
        repo = ProductRepository(db_session)
        loaded_product = await repo.get_by_id(product.id)

        # 验证默认关系是否正确加载
        assert loaded_product.category is not None
        assert loaded_product.category.name == "测试分类"

    @pytest.mark.asyncio
    async def test_manual_load_options_override(self, db_session):
        """测试手动指定load_options覆盖默认行为"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 测试手动加载选项覆盖默认行为
        repo = ProductRepository(db_session)
        loaded_product = await repo.get_by_id(
            product.id,
            load_options=[selectinload(Product.category)]  # 只加载分类
        )

        # 验证关系是否正确加载
        assert loaded_product.category is not None
        assert loaded_product.category.name == "测试分类"

    @pytest.mark.asyncio
    async def test_empty_load_options(self, db_session):
        """测试传递空load_options禁用默认加载"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 测试传递空load_options
        repo = ProductRepository(db_session)
        loaded_product = await repo.get_by_id(
            product.id,
            load_options=[]  # 禁用默认加载
        )

        # 验证关系是否被禁用（这里需要根据实际实现来验证）
        # 注意：由于selectin关系是自动加载的，可能仍然会被加载
        assert loaded_product is not None

    @pytest.mark.asyncio
    async def test_paginated_with_default_relations(self, db_session):
        """测试分页查询中的默认关系加载"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        for i in range(5):
            product = Product(
                name=f"测试商品{i}",
                category_id=category.id,
                status="active"
            )
            db_session.add(product)
        await db_session.commit()

        # 测试分页查询中的默认关系加载
        repo = ProductRepository(db_session)
        products, total = await repo.get_paginated(
            page_num=1,
            page_size=3
        )

        # 验证结果
        assert len(products) == 3
        assert total == 5
        for product in products:
            assert product.category is not None
            assert product.category.name == "测试分类"

    @pytest.mark.asyncio
    async def test_search_with_default_relations(self, db_session):
        """测试搜索功能中的默认关系加载"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 测试搜索功能中的默认关系加载
        repo = ProductRepository(db_session)
        products = await repo.search_products("测试")

        # 验证结果
        assert len(products) == 1
        assert products[0].category is not None
        assert products[0].category.name == "测试分类"

    @pytest.mark.asyncio
    async def test_category_default_relations(self, db_session):
        """测试分类的默认关系加载"""
        # 创建测试数据
        parent_category = Category(name="父分类", status="active")
        db_session.add(parent_category)
        await db_session.flush()

        child_category = Category(
            name="子分类", 
            status="active",
            parent_id=parent_category.id
        )
        db_session.add(child_category)
        await db_session.commit()

        # 测试分类的默认关系加载
        repo = CategoryRepository(db_session)
        loaded_category = await repo.get_by_id(child_category.id)

        # 验证父子关系是否正确加载
        assert loaded_category.parent is not None
        assert loaded_category.parent.name == "父分类"

    @pytest.mark.asyncio
    async def test_spec_default_relations(self, db_session):
        """测试规格的默认关系加载"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        spec = Spec(name="测试规格", status="active")
        spec.categories.append(category)
        db_session.add(spec)
        await db_session.commit()

        # 测试规格的默认关系加载
        repo = SpecRepository(db_session)
        loaded_spec = await repo.get_by_id(spec.id)

        # 验证关系是否正确加载
        assert len(loaded_spec.categories) == 1
        assert loaded_spec.categories[0].name == "测试分类"

    @pytest.mark.asyncio
    async def test_dynamic_relation_loading(self, db_session):
        """测试dynamic关系加载"""
        # 创建测试数据
        product = Product(name="测试商品", status="active")
        db_session.add(product)
        await db_session.commit()

        # 测试dynamic关系加载
        repo = ProductRepository(db_session)
        loaded_product = await repo.get_product_with_inventory_and_skus(product.id)

        # 验证dynamic关系是否加载
        assert loaded_product is not None
        # 注意：这里需要根据实际的dynamic关系实现来验证

    @pytest.mark.asyncio
    async def test_error_handling(self, db_session):
        """测试错误处理"""
        # 测试不存在的load_options
        repo = ProductRepository(db_session)
        
        # 这应该不会抛出异常，但会在日志中记录错误
        product = await repo.get_by_id(
            999,  # 不存在的ID
            load_options=[selectinload(Product.non_existent_relation)]
        )
        
        # 验证返回None而不是抛出异常
        assert product is None

    @pytest.mark.asyncio
    async def test_performance_comparison(self, db_session):
        """测试性能对比"""
        import time

        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        for i in range(10):
            product = Product(
                name=f"测试商品{i}",
                category_id=category.id,
                status="active"
            )
            db_session.add(product)
        await db_session.commit()

        repo = ProductRepository(db_session)

        # 测试不使用关系加载的性能
        start_time = time.time()
        products = await repo.get_list(limit=10, load_options=[])
        for product in products:
            # 模拟访问关系（会触发额外查询）
            pass
        time_without_relations = time.time() - start_time

        # 测试使用默认关系加载的性能
        start_time = time.time()
        products = await repo.get_list(limit=10)  # 使用默认关系加载
        for product in products:
            # 直接访问关系（无需额外查询）
            category_name = product.category.name if product.category else None
        time_with_relations = time.time() - start_time

        # 验证关系加载的性能更好（通常应该更快）
        # 注意：这是一个简化的测试，实际性能可能因数据库和网络延迟而异
        print(f"不使用关系加载耗时: {time_without_relations:.3f}秒")
        print(f"使用默认关系加载耗时: {time_with_relations:.3f}秒")

    @pytest.mark.asyncio
    async def test_service_layer_simplification(self, db_session):
        """测试服务层代码简化"""
        # 创建测试数据
        category = Category(name="测试分类", status="active")
        db_session.add(category)
        await db_session.flush()

        product = Product(
            name="测试商品",
            category_id=category.id,
            status="active"
        )
        db_session.add(product)
        await db_session.commit()

        # 模拟服务层代码
        repo = ProductRepository(db_session)
        
        # 服务层无需关心关系加载，直接使用
        product = await repo.get_by_id(product.id)
        
        # 直接访问关系数据，无需额外查询
        result = {
            "id": product.id,
            "name": product.name,
            "category": product.category.name if product.category else None
        }
        
        # 验证结果
        assert result["id"] == product.id
        assert result["name"] == "测试商品"
        assert result["category"] == "测试分类"
